import { defineStore } from 'pinia';
import { request } from '/@/utils/service';
import XEUtils from 'xe-utils';

export interface AreaNode {
  id: number;
  name: string;
  code: string;
  pcode: string;
  children?: AreaNode[];
}

export const useAreaDictStore = defineStore('areaDict', {
  state: () => ({
    areaTreeData: [] as AreaNode[],
  }),
  
  actions: {
    async getAreaDict() {
      try {
        const res = await request({
          url: '/api/system/area/',
          method: 'get',
          params: {
            limit: 110
          }
        });

        if (res?.code === 2000 && Array.isArray(res.data)) {
          this.areaTreeData = XEUtils.toArrayTree(res.data, {
            parentKey: 'pcode',
            key: 'code',
            children: 'children',
          });
          // this.areaTreeData = res.data;
        }
      } catch (error) {
        console.error('获取属地数据失败:', error);
      }
    }
  },
  persist: {
    enabled: true, // 开启持久化
    strategies: [
        {
            key: 'areaDict',
            storage: localStorage, // 使用sessionStorage持久化
            paths: ['areaTreeData']
        }
    ]
}
}); 