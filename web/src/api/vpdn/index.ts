import request from '/@/utils/request';

/**
 * VPDN管理API接口集合
 */

// 认证相关
export function getToken(params: { username: string; password: string }) {
    return request({
        url: '/api/token/',
        method: 'post',
        data: params,
    });
}

// 域名管理
export function getDomainList(params?: any) {
    return request({
        url: '/api/vpdn/domains/',
        method: 'get',
        params,
    });
}

export function createDomain(data: any) {
    return request({
        url: '/api/vpdn/domains/',
        method: 'post',
        data,
    });
}

export function updateDomain(id: number, data: any) {
    return request({
        url: `/api/vpdn/domains/${id}/`,
        method: 'put',
        data,
    });
}

export function deleteDomain(id: number) {
    return request({
        url: `/api/vpdn/domains/${id}/`,
        method: 'delete',
    });
}

// BRAS设备管理
export function getBrasList(params?: any) {
    return request({
        url: '/api/vpdn/bras/',
        method: 'get',
        params,
    });
}

export function createBras(data: any) {
    return request({
        url: '/api/vpdn/bras/',
        method: 'post',
        data,
    });
}

export function updateBras(id: number, data: any) {
    return request({
        url: `/api/vpdn/bras/${id}/`,
        method: 'put',
        data,
    });
}

export function deleteBras(id: number) {
    return request({
        url: `/api/vpdn/bras/${id}/`,
        method: 'delete',
    });
}

// 用户管理
export function getUserList(params?: any) {
    return request({
        url: '/api/vpdn/users/',
        method: 'get',
        params,
    });
}

export function createUser(data: any) {
    return request({
        url: '/api/vpdn/users/',
        method: 'post',
        data,
    });
}

export function updateUser(id: number, data: any) {
    return request({
        url: `/api/vpdn/users/${id}/`,
        method: 'put',
        data,
    });
}

export function deleteUser(id: number) {
    return request({
        url: `/api/vpdn/users/${id}/`,
        method: 'delete',
    });
}

// 用户操作
export function changePassword(id: number, data: { new_password: string }) {
    return request({
        url: `/api/vpdn/users/${id}/change_password/`,
        method: 'post',
        data,
    });
}

export function suspendUser(id: number) {
    return request({
        url: `/api/vpdn/users/${id}/suspend/`,
        method: 'post',
    });
}

export function resumeUser(id: number) {
    return request({
        url: `/api/vpdn/users/${id}/resume/`,
        method: 'post',
    });
}

export function cancelUser(id: number) {
    return request({
        url: `/api/vpdn/users/${id}/cancel/`,
        method: 'post',
    });
}

export function bindUserIP(id: number, data: { bind_ip: string }) {
    return request({
        url: `/api/vpdn/users/${id}/bind_ip/`,
        method: 'post',
        data,
    });
}

// 批量操作
export function batchOperation(data: { user_ids: number[]; operation: string }) {
    return request({
        url: '/api/vpdn/users/batch_operation/',
        method: 'post',
        data,
    });
}

// 用户统计
export function getUserStatistics() {
    return request({
        url: '/api/vpdn/users/user_statistics/',
        method: 'get',
    });
}

// 认证记录
export function getAuthRecordList(params?: any) {
    return request({
        url: '/api/vpdn/auth-records/',
        method: 'get',
        params,
    });
}

export function getAuthStatistics(params?: any) {
    return request({
        url: '/api/vpdn/auth-records/statistics/',
        method: 'get',
        params,
    });
}

// 清单查询
export function getDetailList(params?: any) {
    return request({
        url: '/api/vpdn/details/',
        method: 'get',
        params,
    });
}

export function billQuery(data: any) {
    return request({
        url: '/api/vpdn/details/bill_query/',
        method: 'post',
        data,
    });
}

export function getTrafficStatistics() {
    return request({
        url: '/api/vpdn/details/traffic_statistics/',
        method: 'get',
    });
}

export function exportData(data: any) {
    return request({
        url: '/api/vpdn/details/export_data/',
        method: 'post',
        data,
    });
}

// 在线记录
export function getOnlineRecordList(params?: any) {
    return request({
        url: '/api/vpdn/online-records/',
        method: 'get',
        params,
    });
}

export function traceQuery(data: any) {
    return request({
        url: '/api/vpdn/online-records/trace_query/',
        method: 'post',
        data,
    });
}

export function getRealTimeMonitor() {
    return request({
        url: '/api/vpdn/online-records/real_time_monitor/',
        method: 'get',
    });
}

export function forceOffline(data: { user_name: string; user_domain: string; reason?: string }) {
    return request({
        url: '/api/vpdn/online-records/force_offline/',
        method: 'post',
        data,
    });
}
