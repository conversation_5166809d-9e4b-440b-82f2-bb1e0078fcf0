import { request } from './service'

export interface DictConfig {
  url?: string
  data?: Array<any>
  value?: string
  label?: string
}

export function dictionary(config: DictConfig) {
  return {
    url: config.url,
    value: config.value || 'value',
    label: config.label || 'label',
    data: config.data || [],
    async getData() {
      if (this.url) {
        const res = await request({
          url: this.url,
          method: 'get'
        })
        return res.data
      }
      return this.data
    }
  }
}

export const dict = dictionary
