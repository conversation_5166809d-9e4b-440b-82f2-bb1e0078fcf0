import { RouteRecordRaw } from 'vue-router';

/**
 * VPDN管理模块路由配置
 */
export const vpdnRoutes: RouteRecordRaw = {
  path: '/vpdn',
  name: 'vpdn',
  component: () => import('/@/layout/routerView/parent.vue'),
  redirect: '/vpdn/dashboard',
  meta: {
    title: 'VPDN管理',
    isLink: '',
    isHide: false,
    isKeepAlive: true,
    isAffix: false,
    isIframe: false,
    roles: ['admin'],
    icon: 'iconfont icon-network'
  },
  children: [
    {
      path: '/vpdn/dashboard',
      name: 'vpdnDashboard',
      component: () => import('/@/views/vpdn/dashboard/index.vue'),
      meta: {
        title: 'VPDN仪表板',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-dashboard'
      }
    },
    {
      path: '/vpdn/domain',
      name: 'vpdnDomain',
      component: () => import('/@/views/vpdn/domain/index.vue'),
      meta: {
        title: '域名管理',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-domain'
      }
    },
    {
      path: '/vpdn/bras',
      name: 'vpdnBras',
      component: () => import('/@/views/vpdn/bras/index.vue'),
      meta: {
        title: 'BRAS设备管理',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-server'
      }
    },
    {
      path: '/vpdn/user',
      name: 'vpdnUser',
      component: () => import('/@/views/vpdn/user/index.vue'),
      meta: {
        title: '用户管理',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-user'
      }
    },
    {
      path: '/vpdn/authrecord',
      name: 'vpdnAuthRecord',
      component: () => import('/@/views/vpdn/authrecord/index.vue'),
      meta: {
        title: '认证记录',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-auth'
      }
    },
    {
      path: '/vpdn/detail',
      name: 'vpdnDetail',
      component: () => import('/@/views/vpdn/detail/index.vue'),
      meta: {
        title: '清单查询',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-list'
      }
    },
    {
      path: '/vpdn/onlinerecord',
      name: 'vpdnOnlineRecord',
      component: () => import('/@/views/vpdn/onlinerecord/index.vue'),
      meta: {
        title: '在线查询',
        isLink: '',
        isHide: false,
        isKeepAlive: true,
        isAffix: false,
        isIframe: false,
        roles: ['admin'],
        icon: 'iconfont icon-online'
      }
    }
  ]
};
