<template>
  <fs-page>
    <el-row>      
      <el-col :span="24" class="p-1">
        <el-card :body-style="{ height: '100%' }">
          <fs-crud ref="crudRef" v-bind="crudBinding">
            <template #actionbar-right>
              <importExcel api="api/tss/terminal/" v-auth="'terminal:Import'">导入</importExcel>
            </template>
          </fs-crud>
        </el-card>
      </el-col>
    </el-row>

  </fs-page>
</template>

<script lang="ts" setup name="user">
import { useExpose, useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { ref, onMounted } from 'vue';
import importExcel from '/@/components/importExcel/index.vue'


// 定义区域数据类型


// crud组件的ref
const crudRef = ref();
const crudBinding = ref();
const { crudExpose } = useExpose({ crudRef, crudBinding });


// 创建crud配置
const { crudOptions } = createCrudOptions({ crudExpose });


// 初始化crud配置
const { resetCrudOptions } = useCrud({ crudExpose, crudOptions });

// 获取属地数据并刷新

onMounted(() => {
  crudExpose.doRefresh();
});

</script>

<style lang="scss" scoped>
.el-row {
  height: 100%;

  .el-col {
    height: 100%;
  }
}

.el-card {
  height: 100%;
}

.font-normal {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
}
</style>
