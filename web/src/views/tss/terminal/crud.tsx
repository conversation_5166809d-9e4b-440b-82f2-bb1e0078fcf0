import { useI18n } from 'vue-i18n'
import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
    useFs
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { SystemConfigStore } from "/@/stores/systemConfig";
import { storeToRefs } from "pinia";
import { computed, toRaw } from "vue";
import { useAreaDictStore } from '/@/stores/areaDict';
import Console from './components/Console.vue'

export function createCrudOptions({ crudExpose }: CreateCrudOptionsProps) {


    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };
    const editRequest = async ({ form, row }: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    const delRequest = async ({ row }: DelReq) => {
        return await api.DelObj(row.id);
    };
    const addRequest = async ({ form }: AddReq) => {
        return await api.AddObj(form);
    };

    const exportRequest = async (query: UserPageQuery) => {
        return await api.exportData(query)
    }

    const systemConfigStore = SystemConfigStore()
    const { systemConfig } = storeToRefs(systemConfigStore)
    const getSystemConfig = computed(() => {
        // console.log(systemConfig.value)
        return systemConfig.value
    })

    const areaStore = useAreaDictStore();

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('terminal:Create')
                    },
                    export: {
                        text: "导出",//按钮文字
                        title: "导出",//鼠标停留显示的信息
                        show: auth('terminal:Export'),
                        click() {
                            return exportRequest(crudExpose!.getSearchFormData())
                        }
                    }
                }
            },
            rowHandle: {
                fixed: 'right',
                width: 250,
                buttons: {
                    view: {
                        show: auth('terminal:Retrieve'),
                        order: 1,
                        type: 'success'
                    },
                    edit: {
                        show: auth('terminal:Update'),
                        order: 2
                    },
                    remove: {
                        show: auth('terminal:Delete'),
                        order: 3
                    }
                },
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        align: 'center',
                        width: '70px',
                        columnSetDisabled: true,
                    }
                }, location: {
                    title: '属地',
                    search: { show: true },
                    type: "text",
                    dict: dict({
                        data: areaStore.areaTreeData,
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: areaStore.areaTreeData,
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code'
                            }
                        }
                    },
                    column: {
                        width: '200px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.location_name || ''
                        }
                    }
                },
                name: {
                    title: '终端名称',
                    type: 'input',
                    form: {
                        component: {
                            props: {
                                placeholder: '请输入终端名称',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                id: {
                    title: 'ID',
                    type: 'text',
                    form: { show: false },
                    column: { show: false }
                },
                fixed_network_ipv6_address: {
                    title: '固网IPv6地址',
                    type: 'text',
                    column: { width: '200px', align: 'center' }
                },
                mobile_network_ipv6_address: {
                    title: '移动网IPv6地址',
                    type: 'text',
                    column: { width: '200px', align: 'center' }
                },
                fixed_network_upload_bandwidth: {
                    title: '固网上行带宽',
                    type: 'text',
                    form: {
                        component: {
                            props: {
                                placeholder: '单位:Mbps'
                            }
                        },
                        helper: '单位:Mbps'
                    },
                    column: { width: '130px', align: 'center' }
                },
                fixed_network_download_bandwidth: {
                    title: '固网下行带宽',
                    type: 'text',
                    form: {
                        component: {
                            props: {
                                placeholder: '单位:Mbps'
                            }
                        },
                        helper: '单位:Mbps'
                    },
                    column: { width: '130px', align: 'center' }
                },
                mobile_network_upload_bandwidth: {
                    title: '移动网上行带宽',
                    type: 'text',
                    form: {
                        component: {
                            props: {
                                placeholder: '单位:Mbps'
                            }
                        },
                        helper: '单位:Mbps'
                    },
                    column: { width: '130px', align: 'center' }
                },
                mobile_network_download_bandwidth: {
                    title: '移动网下行带宽',
                    type: 'text',
                    form: {
                        component: {
                            props: {
                                placeholder: '单位:Mbps'
                            }
                        },
                        helper: '单位:Mbps'
                    },
                    column: { width: '130px', align: 'center' }
                },
                serial_number: {
                    title: '序列号',
                    type: 'text',
                    search: { show: true },
                    column: { width: '180px', align: 'center' }
                },
                is_active: {
                    title: '启用状态',
                    type: 'dict-switch',
                    dict: dict({
                        data: [
                            { value: true, label: '启用' },
                            { value: false, label: '禁用' }
                        ]
                    }),
                    column: { width: '100px', align: 'center' }
                },
                heartbeat_status: {
                    title: '心跳状态',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'normal', label: '正常' },
                            { value: 'interrupted', label: '中断' }
                        ]
                    }),
                    column: { width: '100px', align: 'center' }
                },
                last_heartbeat: {
                    title: '最后心跳时间',
                    type: 'datetime',
                    column: { width: '150px', align: 'center' }
                },
                ipv4_public: {
                    title: 'IPv4公网地址',
                    type: 'text',
                    column: { width: '130px', align: 'center' }
                },
                ipv4_private: {
                    title: 'IPv4私网地址',
                    type: 'text',
                    column: { width: '130px', align: 'center' }
                },
                mac_address: {
                    title: 'MAC地址',
                    type: 'text',
                    column: { width: '150px', align: 'center' }
                },
                ssh_reverse_port: {
                    title: 'SSH反向端口',
                    type: 'number',
                    column: { width: '110px', align: 'center' }
                },
                ssh_port: {
                    title: 'SSH端口',
                    type: 'number',
                    column: { width: '100px', align: 'center', show: false },
                    form: { show: false }
                },
                ssh_username: {
                    title: 'SSH用户名',
                    type: 'text',
                    column: { width: '100px', align: 'center', show: false },
                    form: { show: false }
                },
                ssh_password: {
                    title: 'SSH密码',
                    type: 'password',
                    column: { show: false },
                    form: { show: false }
                },
                create_datetime: {
                    title: '创建时间',
                    type: 'datetime',
                    form: { show: false },
                    column: { width: '160px', align: 'center' }
                },
                update_datetime: {
                    title: '更新时间',
                    type: 'datetime',
                    form: { show: false },
                    column: { width: '160px', align: 'center' }
                }, fixed_network_interface: {
                    title: '固网网卡',
                    type: 'text',
                    column: { width: '100px', align: 'center' }
                }, mobile_network_interface: {
                    title: '移动网卡',
                    type: 'text',
                    column: { width: '100px', align: 'center' }
                }
            }
        }
    }
} 