<template>
  <el-dialog
    v-model="visible"
    title="流量数据可视化"
    width="80%"
    :destroy-on-close="true"
  >
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="统计级别">
          <el-select
            v-model="queryParams.stat_level"
            class="stat-level-select"
            filterable
            clearable
            placeholder="请选择统计级别"
          >
            <el-option
              v-for="item in statLevelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="queryParams.time_range"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="终端">
          <el-select
            v-model="queryParams.device_id"
            class="device-select"
            filterable
            clearable
            placeholder="请选择终端"
          >
            <el-option
              v-for="item in deviceOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="属地">
          <el-tree-select
            v-model="queryParams.location"
            :data="areaStore.areaTreeData"
            :props="{
              value: 'code',
              label: 'name',
              children: 'children'
            }"
            class="area-select"
            clearable
            filterable
            check-strictly
          />
        </el-form-item>
        <el-form-item label="网络类型">
          <el-select 
            v-model="queryParams.network_type"
            class="network-type-select"
            clearable
            placeholder="请选择网络类型"
          >
            <el-option label="全部" :value="undefined" />
            <el-option label="固网" value="fixed" />
            <el-option label="移动网" value="mobile" />
            <el-option label="总计" value="total" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div ref="chartRef" style="width: 100%; height: 400px"></div>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { GetList } from '../api'
import { dict } from '/@/utils/dictionary'
import { useAreaDictStore } from '/@/stores/areaDict'
import { request } from '/@/utils/service'

interface TrafficData {
  stat_time: string
  device?: number
  device_name?: string
  location?: string
  area_name?: string
  upload_traffic: number
  download_traffic: number
  total_traffic: number
  upload_flowrate: number
  download_flowrate: number
  network_type: string
  stat_level: string
}

interface Device {
  id: number
  name: string
  location_name: string
}

export default defineComponent({
  name: 'TrafficDataViewer',
  setup() {
    const visible = ref(false)
    const chartRef = ref<HTMLElement>()
    let chart: echarts.ECharts | null = null
    const areaStore = useAreaDictStore()

    // 确保加载属地数据
    if (!areaStore.areaTreeData.length) {
      areaStore.getAreaDict()
    }

    const queryParams = ref({
      stat_level: 'province_day_total',
      time_range: [],
      device_id: undefined,
      location: undefined,
      network_type: undefined
    })

    // 终端选项数据
    const deviceOptions = ref<Device[]>([])

    // 获取终端列表
    const getDeviceList = async () => {
      try {
        const res = await request({
          url: '/api/tss/devices/',
          method: 'get'
        })
        if (res.code === 2000) {
          deviceOptions.value = res.data
        }
      } catch (error) {
        console.error('获取终端列表失败:', error)
      }
    }

    // 统计级别选项
    const statLevelOptions = [
      { value: 'terminal_day_fixed', label: '终端日级别-固网' },
      { value: 'terminal_day_mobile', label: '终端日级别-移动网' },
      { value: 'terminal_day_total', label: '终端日级别-总计' },
      { value: 'area_day_fixed', label: '属地日级别-固网' },
      { value: 'area_day_mobile', label: '属地日级别-移动网' },
      { value: 'area_day_total', label: '属地日级别-总计' },
      { value: 'province_day_fixed', label: '全省日级别-固网' },
      { value: 'province_day_mobile', label: '全省日级别-移动网' },
      { value: 'province_day_total', label: '全省日级别-总计' },
      
      { value: 'terminal_week_fixed', label: '终端周级别-固网' },
      { value: 'terminal_week_mobile', label: '终端周级别-移动网' },
      { value: 'terminal_week_total', label: '终端周级别-总计' },
      { value: 'area_week_fixed', label: '属地周级别-固网' },
      { value: 'area_week_mobile', label: '属地周级别-移动网' },
      { value: 'area_week_total', label: '属地周级别-总计' },
      { value: 'province_week_fixed', label: '全省周级别-固网' },
      { value: 'province_week_mobile', label: '全省周级别-移动网' },
      { value: 'province_week_total', label: '全省周级别-总计' },
      
      { value: 'terminal_month_fixed', label: '终端月级别-固网' },
      { value: 'terminal_month_mobile', label: '终端月级别-移动网' },
      { value: 'terminal_month_total', label: '终端月级别-总计' },
      { value: 'area_month_fixed', label: '属地月级别-固网' },
      { value: 'area_month_mobile', label: '属地月级别-移动网' },
      { value: 'area_month_total', label: '属地月级别-总计' },
      { value: 'province_month_fixed', label: '全省月级别-固网' },
      { value: 'province_month_mobile', label: '全省月级别-移动网' },
      { value: 'province_month_total', label: '全省月级别-总计' },
      
      { value: 'terminal_quarter_fixed', label: '终端季度级别-固网' },
      { value: 'terminal_quarter_mobile', label: '终端季度级别-移动网' },
      { value: 'terminal_quarter_total', label: '终端季度级别-总计' },
      { value: 'area_quarter_fixed', label: '属地季度级别-固网' },
      { value: 'area_quarter_mobile', label: '属地季度级别-移动网' },
      { value: 'area_quarter_total', label: '属地季度级别-总计' },
      { value: 'province_quarter_fixed', label: '全省季度级别-固网' },
      { value: 'province_quarter_mobile', label: '全省季度级别-移动网' },
      { value: 'province_quarter_total', label: '全省季度级别-总计' },
      
      { value: 'terminal_year_fixed', label: '终端年级别-固网' },
      { value: 'terminal_year_mobile', label: '终端年级别-移动网' },
      { value: 'terminal_year_total', label: '终端年级别-总计' },
      { value: 'area_year_fixed', label: '属地年级别-固网' },
      { value: 'area_year_mobile', label: '属地年级别-移动网' },
      { value: 'area_year_total', label: '属地年级别-总计' },
      { value: 'province_year_fixed', label: '全省年级别-固网' },
      { value: 'province_year_mobile', label: '全省年级别-移动网' },
      { value: 'province_year_total', label: '全省年级别-总计' }
    ]

    const initChart = () => {
      if (chartRef.value) {
        chart = echarts.init(chartRef.value)
      }
    }

    // 处理数据时按时间升序排序并去重
    const processData = (data: TrafficData[]) => {
      // 先按时间分组，合并同一时间点的数据
      const timeGroups = new Map<string, TrafficData>()
      
      data.forEach(item => {
        const time = item.stat_time
        if (!timeGroups.has(time)) {
          timeGroups.set(time, item)
        } else {
          // 如果同一时间点有多条数据，累加流量
          const existing = timeGroups.get(time)!
          existing.upload_traffic += item.upload_traffic
          existing.download_traffic += item.download_traffic
          existing.total_traffic = existing.upload_traffic + existing.download_traffic
        }
      })

      // 转换回数组并按时间排序
      return Array.from(timeGroups.values())
        .sort((a, b) => new Date(a.stat_time).getTime() - new Date(b.stat_time).getTime())
    }

    const loadData = async () => {
      try {
        // 清除上一次的图表数据
        chart?.clear()

        // 处理时间范围
        let startTime = null
        let endTime = null
        if (queryParams.value.time_range && queryParams.value.time_range.length === 2) {
          startTime = queryParams.value.time_range[0]
          endTime = queryParams.value.time_range[1]
        }

        // 构建查询参数
        const params = {
          stat_level: queryParams.value.stat_level || undefined,
          start_time: startTime ? new Date(startTime).toISOString().split('T')[0] : undefined,
          end_time: endTime ? new Date(endTime).toISOString().split('T')[0] : undefined,
          device_id: queryParams.value.device_id || undefined,
          location: queryParams.value.location || undefined,
          network_type: queryParams.value.network_type || undefined,
          page: 1,
          limit: 1000 // 获取足够多的数据点以显示趋势
        }

        // 调用 API - 使用正确的 API 路径
        const res = await request({
          url: '/api/tss/traffic_stats/',  // 修改为正确的 API 路径
          method: 'get',
          params
        })

        if (res.code === 2000 && Array.isArray(res.data)) {
          // 检查统计级别和是否选择了具体对象
          const isTerminalLevel = queryParams.value.stat_level?.startsWith('terminal_')
          const isAreaLevel = queryParams.value.stat_level?.startsWith('area_')
          const noDeviceSelected = !queryParams.value.device_id
          const noLocationSelected = !queryParams.value.location

          if ((isTerminalLevel && noDeviceSelected) || (isAreaLevel && noLocationSelected)) {
            // 按终端或属地分组数据
            const groupKey = isTerminalLevel ? 'device' : 'location'
            const groups = new Map<string | number, TrafficData[]>()
            res.data.forEach((item: TrafficData) => {
              const key = isTerminalLevel ? item.device : item.location
              if (!groups.has(key)) {
                groups.set(key, [])
              }
              groups.get(key)?.push(item)
            })

            // 获取所有时间点并排序
            const allTimes = [...new Set(res.data.map((item: TrafficData) => item.stat_time))]
            allTimes.sort()

            // 为每个分组创建数据系列
            const series: any[] = []
            groups.forEach((groupData, groupId) => {
              let name: string
              if (isTerminalLevel) {
                const deviceName = groupData[0]?.device_name || `终端${groupId}`
                const areaName = groupData[0]?.area_name || ''
                name = `${deviceName}(${areaName})`
              } else {
                name = groupData[0]?.area_name || `属地${groupId}`
              }

              // 上行流量
              series.push({
                name: `${name}-上行`,
                type: 'line',
                data: allTimes.map(time => {
                  const point = groupData.find(d => d.stat_time === time)
                  return point ? Number(point.upload_traffic).toFixed(2) : 0
                })
              })

              // 下行流量
              series.push({
                name: `${name}-下行`,
                type: 'line',
                data: allTimes.map(time => {
                  const point = groupData.find(d => d.stat_time === time)
                  return point ? Number(point.download_traffic).toFixed(2) : 0
                })
              })

              // 总流量
              series.push({
                name: `${name}-总计`,
                type: 'line',
                data: allTimes.map(time => {
                  const point = groupData.find(d => d.stat_time === time)
                  return point ? 
                    (Number(point.upload_traffic) + Number(point.download_traffic)).toFixed(2) : 0
                })
              })
            })

            // 更新图表
            chart?.setOption({
              title: {
                text: isTerminalLevel ? '终端流量趋势' : '属地流量趋势'
              },
              tooltip: {
                trigger: 'axis',
                formatter: function(params: any) {
                  let result = params[0].axisValue + '<br/>'
                  params.forEach((item: any) => {
                    const value = Number(item.value).toFixed(2)
                    result += item.marker + item.seriesName + ': ' + value + ' MB<br/>'
                  })
                  return result
                }
              },
              legend: {
                data: series.map(s => s.name),
                bottom: 0,
                type: 'scroll',
                pageButtonPosition: 'end'
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: processData(res.data).map(item => item.stat_time)
              },
              yAxis: {
                type: 'value',
                name: '流量(MB)',
                axisLabel: {
                  formatter: (value: number) => value.toFixed(2)
                }
              },
              series
            })
          } else {
            // 原有的单终端或其他级别的显示逻辑
            chart?.setOption({
              title: {
                text: '流量趋势'
              },
              tooltip: {
                trigger: 'axis',
                formatter: function(params: any) {
                  let result = params[0].axisValue + '<br/>'
                  params.forEach((item: any) => {
                    const value = Number(item.value).toFixed(2)
                    result += item.marker + item.seriesName + ': ' + value + ' MB<br/>'
                  })
                  return result
                }
              },
              legend: {
                data: ['上行流量', '下行流量', '总流量'],
                bottom: 0
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true
              },
              xAxis: {
                type: 'category',
                boundaryGap: false,
                data: processData(res.data).map(item => item.stat_time)
              },
              yAxis: {
                type: 'value',
                name: '流量(MB)',
                axisLabel: {
                  formatter: (value: number) => value.toFixed(2)
                }
              },
              series: [
                {
                  name: '上行流量',
                  type: 'line',
                  data: processData(res.data).map(item => Number(item.upload_traffic).toFixed(2))
                },
                {
                  name: '下行流量',
                  type: 'line',
                  data: processData(res.data).map(item => Number(item.download_traffic).toFixed(2))
                },
                {
                  name: '总流量',
                  type: 'line',
                  data: processData(res.data).map(item => 
                    (Number(item.upload_traffic) + Number(item.download_traffic)).toFixed(2)
                  )
                }
              ]
            })
          }
        }
      } catch (error) {
        console.error('加载流量数据失败:', error)
      }
    }

    const open = () => {
      visible.value = true
      // 每次打开时刷新终端列表
      getDeviceList()
      // 确保属地数据已加载
      if (!areaStore.areaTreeData.length) {
        areaStore.getAreaDict()
      }
      setTimeout(() => {
        initChart()
        loadData()
      }, 100)
    }

    // 组件挂载时获取终端列表
    onMounted(() => {
      getDeviceList()
    })

    return {
      visible,
      chartRef,
      queryParams,
      deviceOptions,
      statLevelOptions,
      areaStore,
      open,
      loadData
    }
  }
})
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 1rem;
}

.stat-level-select {
  width: 180px !important;
}

.device-select {
  width: 200px !important;
}

.area-select {
  width: 150px !important;
}

.network-type-select {
  width: 150px !important;
}

:deep(.el-select),
:deep(.el-tree-select) {
  width: 100%;
}

:deep(.fs-dict-select) {
  width: 100%;
}

// 确保下拉选项宽度足够
:deep(.el-select-dropdown) {
  min-width: 150px !important;
}
</style> 