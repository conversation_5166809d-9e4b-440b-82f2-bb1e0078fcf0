import { useI18n } from 'vue-i18n'
import * as api from './api';
import { ElTooltip, ElLink } from 'element-plus'
import { h, ref } from 'vue'
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
    useFs
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { SystemConfigStore } from "/@/stores/systemConfig";
import { storeToRefs } from "pinia";
import { computed, toRaw } from "vue";
import { useAreaDictStore } from '/@/stores/areaDict';
import Console from './components/Console.vue'
import TrafficDataViewer from './components/TrafficDataViewer.vue'
import { column } from 'element-plus/es/components/table-v2/src/common';

export function createCrudOptions({ crudExpose, trafficDataViewer }: any): CreateCrudOptionsRet {
    const areaStore = useAreaDictStore();

    if (!areaStore.areaTreeData.length) {
        areaStore.getAreaDict();
    }

    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };

    const deviceDict = dict({
        url: '/api/tss/devices/',
        value: 'id',
        label: 'name',
        cache: true
    })

    const areaDict = dict({
        url: '/api/system/area/',
        value: 'code',
        label: 'name',
        cache: true
    })

    return {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                buttons: {
                    trafficData: {
                        text: '流量数据可视化',
                        click: () => {
                            if (trafficDataViewer?.value) {
                                trafficDataViewer.value.open()
                            }
                        },
                        type: 'primary'
                    },
                    add: {
                        show: false
                    }
                }
            },
            columns: {
                location: {
                    title: '属地',
                    type: 'dict-select',
                    dict: areaDict,
                    search: {
                        show: true
                    }
                },
                device_id: {
                    title: '终端',
                    type: 'dict-select',
                    dict: deviceDict,
                    search: {
                        show: true,
                        component: {
                            props: {
                                filterable: true
                            }
                        }
                    },
                    column: {
                        width: 160,
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.device_name || ''
                        }
                    },
                    valueBinding: {
                        value: 'device_id',
                        label: 'device_name'
                    }
                },
                network_type: {
                    title: '网络类型',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'fixed', label: '固网' },
                            { value: 'mobile', label: '移动网' },
                            { value: 'total', label: '总计' }
                        ]
                    }),
                    search: {
                        show: true
                    }
                },
                stat_level: {
                    title: '统计级别',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'terminal_day_fixed', label: '终端日级别-固网' },
                            { value: 'terminal_day_mobile', label: '终端日级别-移动网' },
                            { value: 'terminal_day_total', label: '终端日级别-总计' },
                            { value: 'area_day_fixed', label: '属地日级别-固网' },
                            { value: 'area_day_mobile', label: '属地日级别-移动网' },
                            { value: 'area_day_total', label: '属地日级别-总计' },
                            { value: 'province_day_fixed', label: '全省日级别-固网' },
                            { value: 'province_day_mobile', label: '全省日级别-移动网' },
                            { value: 'province_day_total', label: '全省日级别-总计' },
                            
                            { value: 'terminal_week_fixed', label: '终端周级别-固网' },
                            { value: 'terminal_week_mobile', label: '终端周级别-移动网' },
                            { value: 'terminal_week_total', label: '终端周级别-总计' },
                            { value: 'area_week_fixed', label: '属地周级别-固网' },
                            { value: 'area_week_mobile', label: '属地周级别-移动网' },
                            { value: 'area_week_total', label: '属地周级别-总计' },
                            { value: 'province_week_fixed', label: '全省周级别-固网' },
                            { value: 'province_week_mobile', label: '全省周级别-移动网' },
                            { value: 'province_week_total', label: '全省周级别-总计' },
                            
                            { value: 'terminal_month_fixed', label: '终端月级别-固网' },
                            { value: 'terminal_month_mobile', label: '终端月级别-移动网' },
                            { value: 'terminal_month_total', label: '终端月级别-总计' },
                            { value: 'area_month_fixed', label: '属地月级别-固网' },
                            { value: 'area_month_mobile', label: '属地月级别-移动网' },
                            { value: 'area_month_total', label: '属地月级别-总计' },
                            { value: 'province_month_fixed', label: '全省月级别-固网' },
                            { value: 'province_month_mobile', label: '全省月级别-移动网' },
                            { value: 'province_month_total', label: '全省月级别-总计' },
                            
                            { value: 'terminal_quarter_fixed', label: '终端季度级别-固网' },
                            { value: 'terminal_quarter_mobile', label: '终端季度级别-移动网' },
                            { value: 'terminal_quarter_total', label: '终端季度级别-总计' },
                            { value: 'area_quarter_fixed', label: '属地季度级别-固网' },
                            { value: 'area_quarter_mobile', label: '属地季度级别-移动网' },
                            { value: 'area_quarter_total', label: '属地季度级别-总计' },
                            { value: 'province_quarter_fixed', label: '全省季度级别-固网' },
                            { value: 'province_quarter_mobile', label: '全省季度级别-移动网' },
                            { value: 'province_quarter_total', label: '全省季度级别-总计' },
                            
                            { value: 'terminal_year_fixed', label: '终端年级别-固网' },
                            { value: 'terminal_year_mobile', label: '终端年级别-移动网' },
                            { value: 'terminal_year_total', label: '终端年级别-总计' },
                            { value: 'area_year_fixed', label: '属地年级别-固网' },
                            { value: 'area_year_mobile', label: '属地年级别-移动网' },
                            { value: 'area_year_total', label: '属地年级别-总计' },
                            { value: 'province_year_fixed', label: '全省年级别-固网' },
                            { value: 'province_year_mobile', label: '全省年级别-移动网' },
                            { value: 'province_year_total', label: '全省年级别-总计' }
                        ]
                    }),
                    search: {
                        show: true,
                        component: {
                            props: {
                                filterable: true
                            }
                        }
                    },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                stat_time: {
                    title: '统计时间',
                    type: 'datetime',
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    },
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'daterange',
                                valueFormat: 'YYYY-MM-DD'
                            }
                        }
                    }
                },
                upload_flowrate: {
                    title: '上行速率(Mbps)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                download_flowrate: {
                    title: '下行速率(Mbps)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                upload_traffic: {
                    title: '上行流量(MB)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                download_traffic: {
                    title: '下行流量(MB)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                upload_duration: {
                    title: '上行时长(秒)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                download_duration: {
                    title: '下行时长(秒)',
                    type: 'number',
                    column: {
                        width: 140,
                        align: 'right'
                    }
                },
                create_datetime: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                update_datetime: {
                    title: '更新时间',
                    type: 'datetime',
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                }
            },
            rowHandle: {
                width: 100,
                fixed: 'right',
                
                buttons: {
                    view: {
                        show: true,
                        text: '查看',
                        type: 'success'
                    },
                    edit: {
                        show: false
                    },
                    remove: {
                        show: false
                    }
                }
            },
            table: {
                border: true,
                stripe: true,
                rowKey: 'id',
                scroll: {
                    x: 2000
                },
                'default-sort': {
                    prop: 'stat_time',
                    order: 'descending'
                }
            },
            pagination: {
                pageSize: 20,
                pageSizes: [20, 50, 100]
            },
            search: {
                show: true,
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            }
        }
    } as CreateCrudOptionsRet
} 