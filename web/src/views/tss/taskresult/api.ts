import { request, downloadFile } from '/@/utils/service';
import { PageQuery, AddReq, DelReq, EditReq, InfoReq } from '@fast-crud/fast-crud';

export const apiPrefix = '/api/tss/task_result/';

// 获取任务结果列表
export function GetList(query: PageQuery) {
    return request({
        url: apiPrefix,
        method: 'get',
        params: query
    });
}

// 获取单个任务结果
export function GetObj(id: number) {
    return request({
        url: apiPrefix + id + '/',
        method: 'get'
    });
}

// 添加任务结果
export function AddObj(obj: AddReq) {
    return request({
        url: apiPrefix,
        method: 'post',
        data: obj
    });
}

// 更新任务结果
export function UpdateObj(obj: EditReq) {
    return request({
        url: apiPrefix + obj.id + '/',
        method: 'put',
        data: obj
    });
}

// 删除任务结果
export function DelObj(id: number) {
    return request({
        url: apiPrefix + id + '/',
        method: 'delete',
        data: { id }
    });
} 