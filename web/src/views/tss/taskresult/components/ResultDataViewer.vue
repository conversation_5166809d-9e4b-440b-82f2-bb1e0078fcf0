<template>
  <el-dialog
    v-model="visible"
    title="测试结果详情"
    width="80%"
    :destroy-on-close="true"
    @opened="() => {
      bandwidthChartRef?.resize()
      trafficChartRef?.resize()
      rttChartRef?.resize()
      cpuChartRef?.resize()
    }"
  >
    <div class="result-data-viewer">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="图表视图" name="chart" v-if="hasChartData">
          <div class="charts-container">
            <div class="chart-item">
              <v-chart ref="bandwidthChartRef" :option="bandwidthOption" :init-options="{ renderer: 'canvas' }" autoresize />
            </div>
            <div class="chart-item">
              <v-chart ref="trafficChartRef" :option="trafficOption" :init-options="{ renderer: 'canvas' }" autoresize />
            </div>
            <div class="chart-item">
              <v-chart ref="rttChartRef" :option="rttOption" :init-options="{ renderer: 'canvas' }" autoresize />
            </div>
            <div class="chart-item">
              <v-chart ref="cpuChartRef" :option="cpuOption" :init-options="{ renderer: 'canvas' }" autoresize />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="JSON视图" name="json">
          <pre class="json-view">{{ formattedData }}</pre>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from 'vue'
import { use } from 'echarts/core'
import { LineChart, PieChart } from 'echarts/charts'
import { 
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent 
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'
import * as echarts from 'echarts'

// 注册必要的组件
use([
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
  LineChart,
  PieChart,
  CanvasRenderer
])

// 使用 ref 来存储数据
const currentData = ref({})
const visible = ref(false)
const activeTab = ref('chart')
const bandwidthChartRef = ref()
const trafficChartRef = ref()
const rttChartRef = ref()
const cpuChartRef = ref()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const formattedData = computed(() => {
  return JSON.stringify(currentData.value, null, 2)
})

const hasChartData = computed(() => {
  return currentData.value && currentData.value.intervals
})

// 带宽趋势图配置
const bandwidthOption = computed(() => {
  if (!currentData.value?.intervals) return {}
  
  const intervals = currentData.value.intervals
  const xAxisData = intervals.map(interval => interval.sum.end.toFixed(1))
  const bandwidthData = intervals.map(interval => 
    (interval.sum.bits_per_second / 1000000).toFixed(2) // 转换为 Mbps
  )

  return {
    title: {
      text: '流速趋势'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        return `时间: ${params[0].axisValue}s<br/>带宽: ${params[0].data}Mbps`
      }
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      name: '时间(秒)',
      nameLocation: 'middle' ,
      nameGap: 40
    },
    yAxis: {
      type: 'value',
      name: '带宽(Mbps)',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90
    },
    series: [{
      name: '带宽',
      data: bandwidthData,
      type: 'line',
      smooth: true,
      lineStyle: { width: 2 },
      areaStyle: { opacity: 0.2 }
    }]
  }
})

// 流量趋势图配置
const trafficOption = computed(() => {
  if (!currentData.value?.intervals) return {}
  
  const intervals = currentData.value.intervals
  const xAxisData = intervals.map(interval => interval.sum.end.toFixed(1))
  
  // 计算累计流量
  let accumulatedBytes = 0
  const trafficData = intervals.map(interval => {
    accumulatedBytes += interval.sum.bytes
    return (accumulatedBytes / (1024 * 1024)).toFixed(2) // 转换为 MB
  })
  
  // 计算总流量
  const totalBytes = currentData.value.end.sum_received.bytes
  const totalMB = (totalBytes / (1024 * 1024)).toFixed(2)
  
  return {
    title: {
      text: '流量趋势',
      subtext: `总流量: ${totalMB}MB`
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const traffic = params[0].data
        const time = params[0].axisValue
        const intervalData = intervals[params[0].dataIndex].sum
        const intervalBytes = (intervalData.bytes / (1024 * 1024)).toFixed(2)
        return `时间: ${time}s<br/>累计流量: ${traffic}MB<br/>区间流量: ${intervalBytes}MB`
      }
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      name: '时间(秒)',
      nameLocation: 'middle',
      nameGap: 40
    },
    yAxis: {
      type: 'value',
      name: '流量(MB)',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90
    },
    series: [{
      name: '累计流量',
      data: trafficData,
      type: 'line',
      smooth: true,
      lineStyle: { width: 2 },
      areaStyle: { opacity: 0.2 }
    }]
  }
})

// RTT趋势图配置
const rttOption = computed(() => {
  if (!currentData.value?.intervals) return {}
  
  const intervals = currentData.value.intervals
  const xAxisData = intervals.map(interval => interval.sum.end.toFixed(1))
  const rtt = intervals.map(interval => interval.streams?.[0]?.rtt)
  const minRtt = currentData.value.end.streams[0].sender.min_rtt
  const maxRtt = currentData.value.end.streams[0].sender.max_rtt
  const meanRtt = currentData.value.end.streams[0].sender.mean_rtt
  
  return {
    title: { text: 'RTT趋势' },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        return `时间: ${params[0].axisValue}s<br/>RTT: ${params[0].data}μs`
      }
    },
    legend: {
      data: ['RTT', '最小RTT', '平均RTT', '最大RTT']
    },
    xAxis: { 
      type: 'category', 
      data: xAxisData, 
      name: '时间(秒)',
      nameLocation: 'middle',
      nameGap: 40
    },
    yAxis: { 
      type: 'value', 
      name: 'RTT(μs)',
      nameLocation: 'middle',
      nameGap: 40,
      nameRotate: 90
    },
    series: [
      {
        name: 'RTT',
        data: rtt,
        type: 'line',
        smooth: true,
        lineStyle: { width: 2 }
      },
      {
        name: '最小RTT',
        type: 'line',
        markLine: {
          data: [{ yAxis: minRtt }],
          label: { formatter: 'Min: {c}μs' }
        }
      },
      {
        name: '平均RTT',
        type: 'line',
        markLine: {
          data: [{ yAxis: meanRtt }],
          label: { formatter: 'Avg: {c}μs' }
        }
      },
      {
        name: '最大RTT',
        type: 'line',
        markLine: {
          data: [{ yAxis: maxRtt }],
          label: { formatter: 'Max: {c}μs' }
        }
      }
    ]
  }
})

// CPU使用率饼图配置
const cpuOption = computed(() => {
  if (!currentData.value?.end?.cpu_utilization_percent) return {}
  
  const cpu = currentData.value.end.cpu_utilization_percent
  
  return {
    title: { text: 'CPU使用率' },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%'
    },
    legend: { 
      orient: 'vertical', 
      left: 'left',
      top: 'middle'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: true,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: true,
        formatter: '{b}: {c}%'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold'
        }
      },
      data: [
        { name: '用户空间', value: cpu.host_user.toFixed(2) },
        { name: '系统空间', value: cpu.host_system.toFixed(2) },
        { name: '空闲', value: (100 - cpu.host_total).toFixed(2) }
      ]
    }]
  }
})

const show = (data: any) => {
  currentData.value = data
  visible.value = true
  nextTick(() => {
    setTimeout(() => {
      bandwidthChartRef.value?.resize()
      trafficChartRef.value?.resize()
      rttChartRef.value?.resize()
      cpuChartRef.value?.resize()
    }, 300)
  })
}

defineExpose({
  show
})

// 监听数据变化，更新图表
watch(() => currentData.value, () => {
  if (visible.value) {
    nextTick(() => {
      bandwidthChartRef.value?.resize()
      trafficChartRef.value?.resize()
      rttChartRef.value?.resize()
      cpuChartRef.value?.resize()
    })
  }
}, { deep: true })

// 监听对话框显示状态
watch(visible, (val) => {
  if (val) {
    nextTick(() => {
      setTimeout(() => {
        bandwidthChartRef.value?.resize()
        trafficChartRef.value?.resize()
        rttChartRef.value?.resize()
        cpuChartRef.value?.resize()
      }, 300)
    })
  }
})
</script>

<style scoped>
.result-data-viewer {
  padding: 20px;
}

.charts-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin: 20px 0;
}

.chart-item {
  height: 400px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.json-view {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  max-height: 500px;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
}
</style> 