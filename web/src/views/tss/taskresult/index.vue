<template>
  <fs-page>
    <el-row>
      <el-col :span="24" class="p-1">
        <el-card :body-style="{ height: '100%' }">
          <fs-crud ref="crudRef" v-bind="crudBinding" />
        </el-card>
      </el-col>
    </el-row>
    <result-data-viewer ref="resultViewerRef" />
  </fs-page>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useCrud, useExpose } from '@fast-crud/fast-crud'
import { createCrudOptions } from './crud'
import ResultDataViewer from './components/ResultDataViewer.vue'

// crud组件的ref
const crudRef = ref()
const resultViewerRef = ref()

// crud 绑定的变量
const crudBinding = ref()
// crud 方法
const { crudExpose } = useExpose({ crudRef, crudBinding })

// 创建crud配置
const { crudOptions } = createCrudOptions({ 
  crudExpose,
  resultViewerRef: resultViewerRef
})

// 初始化crud配置
const { resetCrudOptions } = useCrud({ crudExpose, crudOptions })

// 页面加载完成后刷新数据
onMounted(() => {
  crudExpose.doRefresh()
})
</script>

<style lang="scss" scoped>
.el-row {
  height: 100%;

  .el-col {
    height: 100%;
  }
}

.el-card {
  height: 100%;
}

.font-normal {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
}
</style>