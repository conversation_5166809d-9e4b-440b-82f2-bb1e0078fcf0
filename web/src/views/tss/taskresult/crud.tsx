import { useI18n } from 'vue-i18n'
import * as api from './api';
import { ElTooltip, ElLink } from 'element-plus'
import { h, ref } from 'vue'
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
    useFs
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { SystemConfigStore } from "/@/stores/systemConfig";
import { storeToRefs } from "pinia";
import { computed, toRaw } from "vue";
import { useAreaDictStore } from '/@/stores/areaDict';
import Console from './components/Console.vue'
import ResultDataViewer from './components/ResultDataViewer.vue'

export function createCrudOptions({ crudExpose, resultViewerRef }: any) {

    const areaStore = useAreaDictStore();

    if (!areaStore.areaTreeData.length) {
        areaStore.loadAreaData();
    }
    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };
    const editRequest = async ({ form, row }: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    const delRequest = async ({ row }: DelReq) => {
        return await api.DelObj(row.id);
    };
    const addRequest = async ({ form }: AddReq) => {
        return await api.AddObj(form);
    };

    const exportRequest = async (query: UserPageQuery) => {
        return await api.exportData(query)
    }

    const systemConfigStore = SystemConfigStore()
    const { systemConfig } = storeToRefs(systemConfigStore)
    const getSystemConfig = computed(() => {
        // console.log(systemConfig.value)
        return systemConfig.value
    })

    const { crudOptions } = {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            search: {
                show: true,
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            rowHandle: {
                fixed: 'right',
                width: 250,
                align: 'center',
                buttons: {
                    view: {
                        show: auth('taskresult:Retrieve'),
                        order: 1,
                        type: 'success'                        
                    },  
                    viewresult: {
                        show: auth('taskresult:Retrieve'),
                        text: '结果数据可视化',
                        order: 2,
                        type: 'success',
                        click: ({ row }) => {
                            if (row.result_data && resultViewerRef?.value) {
                                resultViewerRef.value.show(row.result_data)
                            }
                        }                      
                    },                   
                    edit: {
                        show: false
                    },
                    remove: {
                        show: false 
                    }
                }
            },
            table: {
                scroll: {
                    x: 1500
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        align: 'center',
                        width: '70px',
                        fixed: 'left'
                    }
                },
                id: {
                    title: 'ID',
                    type: 'text',
                    column: { show: false }
                },
                parent_name: {
                    title: '主任务名称',
                    type: 'text',
                    search: {
                        show: false                        
                    },
                    column: {
                        width: '150px',
                        align: 'center',
                        component: {
                            name: 'fs-values-format',
                            vModel: 'modelValue'
                        }
                    }
                },                
                source_device_name: {
                    title: '源设备名称',
                    type: 'text',
                    column: {
                        width: '150px',
                        align: 'center',                       
                    },
                    search: {
                        show: true,  
                        width: '50px'                      
                    }
                },
                target_device_name: {
                    title: '目标设备名称',
                    type: 'text',
                    column: {
                        width: '150px',
                        align: 'center',                        
                    },
                    search: {
                        show: true, 
                        width: '50px'                        
                    }
                },
                network_type: {
                    title: '网络类型',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'fixed', label: '固网' },
                            { value: 'mobile', label: '移动网' }
                        ]
                    }),
                    column: {
                        width: '100px',
                        align: 'center'          
                    },
                    search: {
                        show: true                       
                    }
                },
                source_area: {
                    title: '流量起点属地',
                    search: { show: true },
                    type: "dict-tree",
                    dict: dict({
                        data: compute(() => areaStore.areaTreeData),
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: compute(() => areaStore.areaTreeData),
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code',
                            }
                        }
                    },
                    column: {
                        width: '110px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.source_area_name || ''
                        }
                    }
                },
                target_area: {
                    title: '流量终点属地',
                    search: { show: true },
                    type: "dict-tree",
                    dict: dict({
                        data: compute(() => areaStore.areaTreeData),
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: compute(() => areaStore.areaTreeData),
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code'
                            }
                        }
                    },
                    column: {
                        width: '110px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.target_area_name || ''
                        }
                    }
                },result: {
                    title: '执行结果',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'success', label: '成功' },
                            { value: 'failed', label: '失败' }
                        ]
                    }),
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },               
                failure_reason: {
                    title: '失败原因', 
                    type: 'text',
                    column: {
                        width: '200px',
                        align: 'center',
                        component: {
                            name: 'el-text',
                            props: {
                                type: 'info',
                                truncated: true
                            },
                            slots: {
                                default: ({ row }: { row: any }) => row.failure_reason || '-'
                            }
                        }
                    }
                },
                source_network_interface: {
                    title: '源网卡',
                    type: 'text',
                    column: {
                        width: '120px',
                        align: 'center'
                    }
                },
                target_network_interface: {
                    title: '目标网卡',
                    type: 'text',
                    column: {
                        width: '120px',
                        align: 'center'
                    }
                },
                source_ipv6: {
                    title: '源IPv6',
                    type: 'text',
                    column: {
                        width: '200px',
                        align: 'center'
                    }
                },
                target_ipv6: {
                    title: '目标IPv6',
                    type: 'text',
                    column: {
                        width: '200px',
                        align: 'center'
                    }
                },
                direction: {
                    title: '流量方向',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                schedule_type: {
                    title: '调度类型',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                period_type: {
                    title: '周期类型',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                period_days: {
                    title: '周期天数',
                    type: 'number',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                period_minute: {
                    title: '周期分钟',
                    type: 'number',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                period_time: {
                    title: '周期时间',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                target_start_time: {
                    title: '目标开始时间',
                    type: 'datetime',
                    column: {
                        width: '180px',
                        align: 'center'
                    }
                },
                target_duration: {
                    title: '目标持续时间',
                    type: 'number',
                    column: {
                        width: '120px',
                        align: 'center'
                    }
                },
                target_flowrate: {
                    title: '目标流速(Mbps)',
                    type: 'number',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                target_traffic: {
                    title: '目标流量',
                    type: 'number',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },                
                current_flowrate: {
                    title: '实际流速(Mbps)',
                    type: 'number',
                    column: {
                        width: '140px',
                        align: 'center'
                    }
                },
                total_traffic: {
                    title: '总流量(MB)',
                    type: 'number',
                    column: {
                        width: '120px',
                        align: 'center'
                    }
                },
                actual_duration: {
                    title: '实际持续时间(秒)',
                    type: 'number',
                    column: {
                        width: '150px',
                        align: 'center'
                    }
                },
                actual_start_time: {
                    title: '实际开始时间',
                    type: 'datetime',
                    column: {
                        width: '180px',
                        align: 'center'
                    }
                },
                actual_end_time: {
                    title: '实际结束时间',
                    type: 'datetime',
                    column: {
                        width: '180px',
                        align: 'center',
                        sortable: true
                    }
                },                
                create_datetime: {
                    title: '创建时间',
                    type: 'datetime',
                    column: {
                        width: '180px',
                        align: 'center'
                    }
                },
                update_datetime: {
                    title: '更新时间',
                    type: 'datetime',
                    column: {
                        width: '180px',
                        align: 'center'
                    }
                },
                creator_id: {
                    title: '创建者ID',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center'
                    }
                },
                parent_id: {
                    title: '主任务ID',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center',
                        component: {
                            name: 'fs-values-format',
                            vModel: 'modelValue'
                        }
                    }
                },
                task_detail_id: {
                    title: '子任务ID',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center',
                        component: {
                            name: 'fs-values-format',
                            vModel: 'modelValue'
                        }
                    }
                },source_device_id: {
                    title: '源设备ID',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center',                        
                    }
                },
                target_device_id: {
                    title: '目标设备ID',
                    type: 'text',
                    column: {
                        width: '100px',
                        align: 'center',                        
                    }
                },
                description: {
                    title: '描述',
                    type: 'text',
                    column: { 
                        width: '180px',
                        align: 'center'
                    }
                }
            },
            pagination: {
                // Add pagination configuration
            },
            sortOptions: {
                sortProp: 'actual_end_time',
                sortOrder: 'descending'
            }
        }
    }

    return {
        crudOptions
    }
} 