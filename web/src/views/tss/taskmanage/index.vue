<template>
  <fs-page>
    <el-row>
      <el-col :span="24" class="p-1">
        <el-card :body-style="{ height: '100%' }">
          <fs-crud ref="crudRef" v-bind="crudBinding">
            <template #cell_$expand="scope">
              <div class="expand-container">
                <SubTaskTable 
                  ref="subTaskTableRef"
                  :mainTaskId="scope.row.id" 
                  :sourceAreaId="scope.row.source_area"
                  :targetAreaId="scope.row.target_area"
                  :mainTaskConfig="{
                    target_flowrate: scope.row.target_flowrate,
                    target_duration: scope.row.target_duration,
                    target_traffic: scope.row.target_traffic
                  }"
                />
              </div>
            </template>
          </fs-crud>
        </el-card>
      </el-col>
    </el-row>
  </fs-page>
</template>

<script lang="ts" setup name="taskmanage">
import { useExpose, useCrud } from '@fast-crud/fast-crud';
import { createCrudOptions } from './crud';
import { ref, onMounted, watch, nextTick } from 'vue';
import SubTaskTable from './components/SubTaskTable'

const crudRef = ref();
const crudBinding = ref();
const { crudExpose } = useExpose({ crudRef, crudBinding });

// 创建crud配置
const { crudOptions } = createCrudOptions({ 
    crudExpose,
    afterUpdate: () => {
        // 主任务更新后，刷新展开的子任务列表
        const expandedRows = crudRef.value?.getExpandedRows();
        if (expandedRows?.length) {
            expandedRows.forEach((row: any) => {
                const subTableRef = document.querySelector(`[data-row-key="${row.id}"] .expand-container`)
                    ?.querySelector('.sub-task-table')?.__vueParentComponent?.ctx;
                if (subTableRef?.loadSubTasks) {
                    subTableRef.loadSubTasks();
                }
            });
        }
    }
});

// 初始化crud配置
const { resetCrudOptions } = useCrud({ crudExpose, crudOptions });

onMounted(() => {
    crudExpose.doRefresh();
});

</script>

<style lang="scss">
/* 移除 scoped，使用全局样式 */
.fs-crud-table .el-table__expanded-cell[class*=cell] {
  padding: 10px !important;
}
</style>

<style lang="scss" scoped>
.el-row {
  height: 100%;
  .el-col {
    height: 100%;
  }
}

.el-card {
  height: 100%;
}

.expand-container {
  padding: 8px 0;
  
  :deep(.el-table) {
    border: 1px solid #dcdfe6;
    
    .el-table__header-wrapper {
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
        padding: 8px 0;
      }
    }
    
    .el-table__row {
      td {
        padding: 8px 0;
      }
    }
  }
}
</style> 