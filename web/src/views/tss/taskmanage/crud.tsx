import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CreateCrudOptionsProps,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { useAreaDictStore } from '/@/stores/areaDict';
import { ElMessage } from 'element-plus';
import SubTaskTable from './components/SubTaskTable';

interface TaskRow {
    id: number;
    status: string;
    // 其他需要的字段...
}

// 添加时间格式化函数
const formatTime = (time: string) => {
    if (!time) return '';
    // 如果是 HH:mm:ss 格式，直接返回
    if (/^\d{2}:\d{2}:\d{2}$/.test(time)) {
        return time;
    }
    // 其他格式尝试用 Date 处理
    const date = new Date(time);
    if (isNaN(date.getTime())) {
        return time;
    }
    return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        second: '2-digit',
        hour12: false 
    });
};

export function createCrudOptions({ crudExpose }: CreateCrudOptionsProps) {
    const areaStore = useAreaDictStore();

    if (!areaStore.areaTreeData.length) {
        areaStore.loadAreaData();
    }

    const expandRowKeys = ref<string[]>([]);

    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };
    const editRequest = async ({ form, row }: EditReq) => {
        form.id = row.id;
        return await api.UpdateObj(form);
    };
    const delRequest = async ({ row }: DelReq) => {
        return await api.DelObj(row.id);
    };
    const addRequest = async (req: AddReq) => {
        return await api.AddObj(req.form);
    };

    const { crudOptions } = {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            options: {
                onError: ({ error, ctx }: { error: any, ctx: any }) => {
                    const errorMessage = error.response?.data?.detail || error.message || '请求发生错误';
                    ElMessage.error(errorMessage);
                    return false;
                }
            },
            form: {
                async afterSubmitError({ error }: { error: any }) {
                    const errorMessage = error.response?.data?.detail || error.message || '提交失败';
                    ElMessage.error(errorMessage);
                    return false;
                }
            },
            actionbar: {
                buttons: {
                    add: {
                        text: "新建调度任务",
                        show: auth('taskmanage:Create')
                    }
                }
            },
            rowHandle: {
                fixed: 'right',
                width: 250,
                align: 'center',
                buttons: {
                    view: {
                        show: auth('taskmanage:Retrieve'),
                        order: 1,
                        type: 'success'
                    },
                    edit: {
                        show: auth('taskmanage:Update'),
                        order: 2
                    },
                    remove: {
                        show: auth('taskmanage:Delete'),
                        order: 3
                    }
                }
            },
            table: {},
            columns: {
                $expand: {
                    title: "展开",
                    form: { show: false },
                    column: {
                        type: "expand",
                        align: "center",
                        width: "55px",
                        columnSetDisabled: true //禁止在列设置中选择
                    }
                },
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        align: 'center',
                        width: '70px',
                        columnSetDisabled: true,
                    }
                },
                task_name: {
                    title: '任务名称',
                    type: 'input',
                    search: { show: true },
                    column: {
                        width: 200,
                        align: 'left'
                    },
                    form: {
                        rules: [
                            { required: true, message: '任务名称不能为空' }
                        ],
                        component: {
                            props: {
                                placeholder: '请输入任务名称',
                                clearable: true
                            }
                        }
                    }
                },
                network_type: {
                    title: '网络类型',
                    type: 'dict-switch',
                    dict: dict({
                        data: [
                            { value: 'fixed', label: '固网' },
                            { value: 'mobile', label: '移动网' }
                        ]
                    }),
                    form: {
                        component: {
                            name: 'el-switch',
                            props: {
                                activeValue: 'mobile',
                                inactiveValue: 'fixed',
                                activeText: '移动网',
                                inactiveText: '固网',
                                inlinePrompt: false
                            }
                        },
                        helper: '选择使用固网还是移动网进行流量测试'
                    },
                    column: {
                        width: '120px',
                        align: 'center'
                    }
                },
                subtask_count: {
                    title: '子任务数',
                    type: 'number',
                    column: {
                        width: '90px',
                        align: 'center'
                    },
                    form: { show: false }
                },
                source_area: {
                    title: '流量起点属地',
                    search: { show: true },
                    type: "dict-tree",
                    dict: dict({
                        data: compute(() => areaStore.areaTreeData),
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: compute(() => areaStore.areaTreeData),
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code',
                            }
                        }
                    },
                    column: {
                        width: '110px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.source_area_name || ''
                        }
                    }
                },
                target_area: {
                    title: '流量终点属地',
                    search: { show: true },
                    type: "dict-tree",
                    dict: dict({
                        data: compute(() => areaStore.areaTreeData),
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: compute(() => areaStore.areaTreeData),
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code'
                            }
                        }
                    },
                    column: {
                        width: '110px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.target_area_name || ''
                        }
                    }
                },
                direction: {
                    title: '流量方向',
                    type: 'dict-switch',
                    dict: dict({
                        data: [
                            { value: 'upload', label: '上行' },
                            { value: 'download', label: '下行' }
                        ]
                    }),
                    form: {
                        component: {
                            name: 'el-switch',
                            props: {
                                activeValue: 'upload',
                                inactiveValue: 'download',
                                activeText: '上行',
                                inactiveText: '下行',
                                inlinePrompt: false,
                                width: 200
                            }                            
                        },
                        helper: '下行:终点发流到起点；上行:起点发流到终点'
                    },
                    column: { width: '90px', align: 'center' }
                },
                task_type: {
                    title: '任务设定类型',
                    type: 'dict-switch',
                    dict: dict({
                        data: [
                            { value: 'flowrate', label: '流速' },
                            { value: 'traffic', label: '流量' }
                        ]
                    }),
                    column: { show: true, width: 120 },
                    form: {
                        helper: '切换任务设定方式',
                        value: 'flowrate',
                        component: {
                            props: {
                                activeValue: 'flowrate',
                                inactiveValue: 'traffic',
                                activeText: '流速',
                                inactiveText: '流量',
                                inlinePrompt: false,
                                width: 200
                            }
                        }
                    }
                },
                target_flowrate: {
                    title: '目标流速(Mbit/s)',
                    type: 'number',
                    form: {
                        component: {
                            props: {
                                min: 1,
                                placeholder: '请输入目标流速'
                            }
                        },
                        helper: '设置目标流速',
                        show: compute(({ form }) => form.task_type === 'flowrate')
                    },
                    column: { width: '140px', align: 'center' }
                },
                target_duration: {
                    title: '目标持续时间(秒)',
                    type: 'number',
                    form: {
                        component: {
                            props: {
                                min: 1,
                                placeholder: '请输入目标持续时间'
                            }
                        },
                        helper: '设置目标流速的持续时间',
                        show: compute(({ form }) => form.task_type === 'flowrate')
                    },
                    column: { width: '140px', align: 'center' }
                },
                target_traffic: {
                    title: '目标流量(MB)',
                    type: 'number',
                    form: {
                        component: {
                            props: {
                                min: 1,
                                placeholder: '请输入目标流量'
                            }
                        },
                        helper: '设置需要传输的总流量',
                        show: compute(({ form }) => form.task_type === 'traffic')
                    },
                    column: { width: '120px', align: 'center' }
                },
                schedule_type: {
                    title: '调度方式',
                    type: 'dict-radio',
                    dict: dict({
                        data: [
                            { value: 'once', label: '一次性调度' },
                            { value: 'periodic', label: '周期性调度' }
                        ]
                    }),
                    form: {
                        value: 'once',
                        helper: '选择任务调度方式'
                    },
                    column: { width: '100px', align: 'center' }
                },
                period_type: {
                    title: '周期类型',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'hourly', label: '每小时' },
                            { value: 'daily', label: '每天' },
                            { value: 'weekly', label: '每周' },
                            { value: 'monthly', label: '每月' }
                        ]
                    }),
                    form: {
                        show: compute(({ form }) => form.schedule_type === 'periodic'),
                        helper: '选择任务执行周期',
                    },
                    column: { width: '100px', align: 'center' }
                },
                period_minute: {
                    title: '执行分钟',
                    type: 'number',
                    form: {
                        show: compute(({ form }) =>
                            form.schedule_type === 'periodic' && form.period_type === 'hourly'
                        ),
                        component: {
                            props: {
                                min: 0,
                                max: 59,
                                placeholder: '请输入每小时的第几分钟执行(0-59)'
                            }
                        },
                        helper: '设置每小时的第几分钟执行'
                    },
                    column: { width: '100px', align: 'center' }
                },
                period_time: {
                    title: '执行时间',
                    type: 'text',
                    column: {
                        width: 100,
                        align: 'center'                        
                    },
                    form: {
                        show: compute(({ form }) =>
                            form.schedule_type === 'periodic' && form.period_type !== 'hourly'
                        ),
                        component: {
                            name: 'el-time-picker',
                            props: {
                                placeholder: '请选择执行时间',
                                valueFormat: 'HH:mm:ss'
                            }
                        },
                        helper: '设置任务的执行时间点'
                    }
                },
                period_days: {
                    title: '执行日期',
                    type: 'dict-select',
                    cache: false,
                    dict: compute(({ form }) => {
                        const type = form.period_type;

                        if (type === 'monthly') {
                            return dict({
                                data: Array.from({ length: 31 }, (_, i) => ({
                                    value: String(i + 1),
                                    label: `${i + 1}日`
                                }))
                            });
                        } else if (type === 'weekly') {
                            return dict({
                                data: [
                                    { value: '1', label: '星期一' },
                                    { value: '2', label: '星期二' },
                                    { value: '3', label: '星期三' },
                                    { value: '4', label: '星期四' },
                                    { value: '5', label: '星期五' },
                                    { value: '6', label: '星期六' },
                                    { value: '7', label: '星期日' }
                                ]
                            });
                        }
                        return dict({ data: [] });
                    }),
                    form: {
                        component: {
                            props: {
                                key: compute(({ form }) => `period_days_${form.period_type}`),
                                multiple: true,
                                clearable: true,
                                placeholder: compute(({ form }) =>
                                    form.period_type === 'weekly'
                                        ? '请选择星期几执行'
                                        : '请选择执行日期（可多选）'
                                )
                            }
                        },
                        show: compute(({ form }) => {
                            return form.schedule_type === 'periodic' &&
                                ['weekly', 'monthly'].includes(form.period_type);
                        })
                    },
                    column: { show: false }
                },
                target_start_time: {
                    title: '计划开始时间',
                    type: 'datetime',
                    form: {
                        show: compute(({ form }) => form.schedule_type === 'once'),
                        component: {
                            props: {
                                placeholder: '请选择计划开始时间',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss'
                            }
                        }
                    }
                },
                status: {
                    title: '任务状态',
                    type: 'dict-select',
                    dict: dict({
                        data: [
                            { value: 'pending', label: '等待中' },
                            { value: 'scheduling', label: '调度中' },
                            { value: 'running', label: '进行中' },
                            { value: 'completed', label: '已完成' },
                            { value: 'failed', label: '失败' }
                        ]
                    }),
                    column: { width: '90px', align: 'center' },
                    form: { show: false }
                },
                is_active: {
                    title: '是否启用',
                    type: 'dict-switch',
                    dict: dict({
                        data: [
                            { value: true, label: '启用' },
                            { value: false, label: '禁用' }
                        ]
                    }),
                    column: { width: '90px', align: 'center' },
                    form: { 
                        show: true,
                        value: true // 设置默认值为启用
                    }
                },
                task_description: {
                    title: '任务描述',
                    type: 'textarea',
                    column: {
                        show: true, // 默认列表不显示
                        width: 300
                    },
                    form: {
                        component: {
                            props: {
                                placeholder: '请输入任务描述',
                                type: 'textarea',
                                rows: 3,
                                showWordLimit: true,
                                maxlength: 200
                            }
                        }
                    }
                },
                _helper: {
                    title: '',
                    editForm: { show: false },
                    addForm: {
                        component: {
                            render: () => {
                                return (
                                    <div style={{
                                        color: '#909399',
                                        fontSize: '12px',
                                        backgroundColor: '#f5f7fa',
                                        borderRadius: '4px'
                                    }}>
                                        提示：任务生成后，在任务列表中点击展开查看子任务
                                    </div>
                                )
                            }
                        },
                        order: 999
                    },
                    column: { show: false }
                }
            }
        }
    }

    return {
        crudOptions
    }
}

function has31Days(): boolean {
    return true;
} 