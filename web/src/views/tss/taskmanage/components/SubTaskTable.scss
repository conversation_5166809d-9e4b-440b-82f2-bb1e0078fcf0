.sub-task-table {
    padding: 10px 0;
    background: #f5f7fa;
    position: relative;
    
    .table-scroll-container {
        overflow-x: overlay;  // 使用 overlay 替代 auto，实现更好的滚动条行为
        width: 100%;  // 确保容器占满父元素宽度
        
        &::-webkit-scrollbar {
            height: 12px;
            background-color: transparent;  // 透明背景
        }
        
        &::-webkit-scrollbar-thumb {
            background-color: rgba(144, 147, 153, 0.5);  // 增加不透明度
            border-radius: 6px;
            border: 2px solid transparent;
            background-clip: padding-box;  // 确保边框透明
            
            &:hover {
                background-color: rgba(144, 147, 153, 0.7);  // 悬停时更深的颜色
            }
        }
        
        &::-webkit-scrollbar-track {
            background-color: transparent;
            border-radius: 6px;
        }
    }
    
    .el-table {
        margin: 0;
        border: 1px solid #dcdfe6;
        
        th.el-table__cell {
            background-color: #f5f7fa;
            font-size: 14px;
        }

        td.el-table__cell {
            font-size: 14px;
        }
        
        &::before {
            display: none;
        }
        
        .el-table__expanded-cell {
            padding: 10px !important;
            background: transparent !important;
        }

        .el-table__fixed-right {
            height: 100% !important;
            box-shadow: -2px 0 8px rgba(0,0,0,.15);
            position: absolute;
            right: 0;
            z-index: 3;
        }
    }

    :deep(.el-table) {
        font-size: 15px;
        
        th {
            font-size: 15px;
            font-weight: 500;
        }
        
        td {
            font-size: 15px;
        }

        .el-button {
            font-size: 14px;
        }

        .el-table__fixed-right {
            height: 100% !important;
            box-shadow: -2px 0 8px rgba(0,0,0,.15);
            position: absolute;
            right: 0;
            z-index: 3;
        }
        
        .el-table__fixed-left {
            height: 100% !important;
            box-shadow: 2px 0 8px rgba(0,0,0,.15);
            position: absolute;
            left: 0;
            z-index: 3;
        }

        .el-table__fixed-header-wrapper {
            z-index: 4;
        }

        .operation-column {
            background-color: #fff;  // 确保固定列背景色正确
        }

        .el-table__fixed-right::before {
            content: '';
            position: absolute;
            left: -10px;
            top: 0;
            bottom: 0;
            width: 10px;
            background: linear-gradient(to right, rgba(0,0,0,0), rgba(0,0,0,0.1));
        }

        .el-table__fixed-header-wrapper {
            background-color: #f5f7fa;
            z-index: 4;
        }
    }

    .operation-buttons {
        display: flex;
        justify-content: center;
        gap: 8px;  // 按钮之间的间距
    }

    :deep(.el-table) {
        .el-button {
            padding: 6px 12px;
            
            &.el-button--danger {
                margin-right: 8px;
            }
        }
    }

    :deep(.el-dialog__wrapper) {
        z-index: 3000 !important; // 使用更高的 z-index 并添加 !important
    }

    :deep(.v-modal) {
        z-index: 2999 !important; // 遮罩层也需要调高 z-index
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #dcdfe6;
}

.el-form-item {
    margin-bottom: 20px;
    
    &:last-child {
        margin-bottom: 0;
    }
} 