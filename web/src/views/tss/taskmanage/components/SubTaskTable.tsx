import './SubTaskTable.scss';
import { defineComponent, onBeforeMount, onMounted, ref, watch, onBeforeUnmount } from 'vue'
import { ElTable, ElTableColumn } from 'element-plus'
import { request } from '/@/utils/service'
import type { App } from 'vue'
import { EditSubTaskDialog } from './EditSubTaskDialog'
import { createVNode, render } from 'vue';
import { ElMessage } from 'element-plus';

interface Device {
    id: number;
    name: string;   
}

interface SubTask {
    id: number;
    source_device: Device;
    target_device: Device;
    source_device_name?: string;
    // source_device_ip?: string;
    target_device_name?: string;
    // target_device_ip?: string;
    direction: string;       // 流量方向 (上行/下行)
    target_flowrate: number;      // 目标流速
    target_duration: number;             // 目标持续时间    
    target_traffic: number;       // 目标流量    
    current_flowrate?: number;    // 当前流速
    total_traffic?: number;       // 已产生流量
    actual_duration?: number;     // 实际持续时间
    actual_start_time?: string;      // 实际开始时间
    actual_end_time?: string;        // 实际结束时间
    status: string;              // 任务状态
}

export const SubTaskTable = defineComponent({
    name: 'SubTaskTable',
    props: {
        mainTaskId: {
            type: [Number, String],
            required: true
        },
        sourceAreaId: {  // 主任务流量起点属地
            type: [Number, String],
            required: true
        },
        targetAreaId: {  // 主任务流量终点属地
            type: [Number, String],
            required: true
        },
        mainTaskConfig: {
            type: Object as PropType<{
                target_flowrate?: number;
                target_duration?: number;
                target_traffic?: number;
            }>,
            required: true
        }
    },
    setup(props) {
        if (!props.mainTaskId) {
            return () => <div>Error: No mainTaskId provided</div>;
        }
        
        const subTasks = ref<SubTask[]>([]);
        const loading = ref(false);
        const tableWidth = ref('500px');  // 默认宽度
        
        const editDialogVisible = ref(false);
        const currentEditTask = ref<SubTask | null>(null);
        
        // 获取子任务数据
        const loadSubTasks = async () => {
            try {
                loading.value = true;                
                const res = await request({
                    url: `/api/tss/task_details/`,
                    method: 'get',
                    params: {
                        parent: props.mainTaskId
                    }
                });

                if (res.data) {
                    subTasks.value = res.data;
                } else {
                    console.warn('[SubTask] 子任务数据为空');
                    subTasks.value = [];
                }
            } catch (error) {
                console.error('[SubTask] 加载子任务数据失败:', error);
                subTasks.value = [];
            } finally {
                loading.value = false;
            }
        };

        // 子任务终止处理函数
        const handleStopSubTask = async (taskId: number) => {
            try {
                console.log('[SubTask] 开始终止子任务:', taskId);
                await request({
                    url: `/api/tss/task_details/${taskId}/stop/`,
                    method: 'post'
                });
                console.log('[SubTask] 子任务终止成功');
                // 刷新数据
                await loadSubTasks();
            } catch (error) {
                console.error('[SubTask] 终止子任务失败:', error);
            }
        };

        const showEditDialog = (task: SubTask) => {
            const container = document.getElementById('global-dialog-container');
            if (!container) return;

            // 设置当前编辑的任务
            currentEditTask.value = task;
            editDialogVisible.value = true;

            const vnode = createVNode(EditSubTaskDialog, {
                visible: true,
                subTask: task,  // 传入完整的子任务对象
                sourceAreaId: props.sourceAreaId,
                targetAreaId: props.targetAreaId,
                mainTaskConfig: props.mainTaskConfig,
                onConfirm: handleEditConfirm,
                'onUpdate:visible': (val: boolean) => {
                    if (!val) {
                        // 关闭时清理
                        render(null, container);
                        currentEditTask.value = null;
                        editDialogVisible.value = false;
                    }
                }
            });

            render(vnode, container);
        };

        const handleEditSubTask = (row: SubTask) => {
            showEditDialog(row);
        };

        const handleEditConfirm = async (formData: any) => {
            try {
                if (!currentEditTask.value) return;
                
                await request({
                    url: `/api/tss/task_details/${currentEditTask.value.id}/`,
                    method: 'patch',
                    data: formData
                });                
                await loadSubTasks();
            } catch (error) {
                console.error('[SubTask] 编辑子任务失败:', error);
            }
        };

        // 监听主任务ID变化
        watch(() => props.mainTaskId, () => {
            loadSubTasks();
        });

        // 监听主任务配置的变化
        watch(() => props.mainTaskConfig, () => {
            loadSubTasks();
        }, { deep: true });

        // 获取主任务表格宽度
        const updateTableWidth = () => {
            // 获取主任务表格元素
            const mainTable = document.querySelector('.fs-crud-table .el-table__inner-wrapper');
            if (mainTable) {
                tableWidth.value = `${mainTable.clientWidth}px`;
            }
        };

        onMounted(() => {
            loadSubTasks();
            // 等待主表格渲染完成后获取宽度
            setTimeout(updateTableWidth, 0);

            // 监听窗口大小变化
            window.addEventListener('resize', updateTableWidth);
        });

        // 组件销毁时移除事件监听
        onBeforeUnmount(() => {
            window.removeEventListener('resize', updateTableWidth);
        });

        return () => (
            <div class="sub-task-table">
                <div class="table-scroll-container">
                    <ElTable 
                        data={subTasks.value} 
                        size="small" 
                        border
                        v-loading={loading.value}
                        style={{ width: tableWidth.value }}
                        fit={false}
                    >
                        {/* 1. 源终端 */}
                        <ElTableColumn 
                            label="源终端" 
                            prop="source_device_name"
                            width="170"
                        />

                        {/* 2. 目标终端 */}
                        <ElTableColumn 
                            label="目标终端" 
                            prop="target_device_name"
                            width="170"                            
                        />

                        {/* 3. 流量方向 */}
                        <ElTableColumn 
                            label="流量方向" 
                            prop="direction"
                            width="100" 
                            align="center"
                        />

                        {/* 4-5. 目标流速和持续时间，只在主任务为速度类型时显示 */}
                        {props.mainTaskConfig.target_flowrate != null && (
                            <>
                                <ElTableColumn 
                                    label="目标流速(Mbit/s)" 
                                    prop="target_flowrate" 
                                    width="140" 
                                    align="center" 
                                />
                                <ElTableColumn 
                                    label="目标持续时间(秒)" 
                                    prop="target_duration"
                                    width="140" 
                                    align="center" 
                                />
                            </>
                        )}

                        {/* 6. 目标流量，只在主任务为流量类型时显示 */}
                        {props.mainTaskConfig.target_traffic != null && (
                            <ElTableColumn 
                                label="目标流量(MB)" 
                                prop="target_traffic" 
                                width="120" 
                                align="center" 
                            />
                        )}
                        {/* 源终端网卡 */}
                        <ElTableColumn 
                            label="源终端网卡" 
                            prop="source_network_interface"
                            width="120"
                            align="center"
                        />

                        {/* 目标终端网卡 */}
                        <ElTableColumn 
                            label="目标终端网卡" 
                            prop="target_network_interface"
                            width="120"
                            align="center"
                        />
                        {/* 7. 任务状态 */}
                        <ElTableColumn 
                            label="任务状态" 
                            prop="status" 
                            width="100" 
                            align="center"
                        >
                            {{
                                default: ({row}: {row: SubTask}) => {
                                    const statusMap = {
                                        'pending': '等待中',
                                        'running': '进行中',
                                        'completed': '已完成',
                                        'failed': '失败'
                                    };
                                    return statusMap[row.status] || row.status;
                                }
                            }}
                        </ElTableColumn>
                        {/* 是否启用 */}
                        <ElTableColumn 
                            label="是否启用" 
                            prop="is_active"
                            width="90"
                            align="center"
                        >
                            {{
                                default: ({row}: {row: SubTask}) => (
                                    <el-tag type={row.is_active ? 'success' : 'danger'}>
                                        {row.is_active ? '启用' : '禁用'}
                                    </el-tag>
                                )
                            }}
                        </ElTableColumn>
                        {/* 8. 失败原因 */}
                        <ElTableColumn 
                            label="失败原因" 
                            prop="failure_reason"
                            width="250" 
                            align="center" 
                        />
                        {/* 9. 任务描述 */}
                        <ElTableColumn 
                            label="任务描述" 
                            prop="description"
                            width="250" 
                            align="center"       
                        />
                        {/* 10. 操作列 */}
                        <ElTableColumn 
                            label="操作" 
                            width="200"
                            align="center"
                            fixed="right"
                            class-name="operation-column"
                        >
                            {{
                                default: ({row}: {row: SubTask}) => (
                                    <div class="operation-buttons">
                                        {row.status === 'running' && (
                                            <el-button 
                                                type="danger" 
                                                size="small"
                                                onClick={() => handleStopSubTask(row.id)}
                                            >
                                                停止
                                            </el-button>
                                        )}
                                            <el-button 
                                                type="primary" 
                                                size="small"
                                                onClick={() => handleEditSubTask(row)}
                                            >
                                                编辑
                                            </el-button>
                                    </div>
                                )
                            }}
                        </ElTableColumn>
                    </ElTable>
                </div>
                
                {currentEditTask.value && (
                    <EditSubTaskDialog
                        v-model:visible={editDialogVisible.value}
                        subTask={currentEditTask.value}
                        sourceAreaId={props.sourceAreaId}
                        targetAreaId={props.targetAreaId}
                        mainTaskConfig={props.mainTaskConfig}
                        onConfirm={handleEditConfirm}
                    />
                )}
            </div>
        );
    }
});

// 添加install方法
SubTaskTable.install = (app: App) => {
    app.component('SubTaskTable', SubTaskTable);
};

export default SubTaskTable; 