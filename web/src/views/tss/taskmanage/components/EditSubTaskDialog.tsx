import { defineComponent, ref, PropType, onMounted, computed } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElMessage, ElSwitch } from 'element-plus'
import { request } from '/@/utils/service'
import type { SubTask, Device } from './types'

export const EditSubTaskDialog = defineComponent({
    name: 'EditSubTaskDialog',
    props: {
        visible: Boolean,
        subTask: {
            type: Object as PropType<SubTask>,
            required: true
        },
        sourceAreaId: {  // 主任务流量起点属地
            type: [Number, String],
            required: true
        },
        targetAreaId: {  // 主任务流量终点属地
            type: [Number, String],
            required: true
        },
        mainTaskConfig: {
            type: Object as PropType<{
                target_flowrate?: number;
                target_duration?: number;
                target_traffic?: number;
            }>,
            required: true
        }
    },
    emits: ['update:visible', 'confirm', 'cancel'],
    setup(props, { emit }) {
        const formRef = ref();
        const deviceList = ref<Device[]>([]); // 所有终端列表        
        const formData = ref({
            source_device: props.subTask.source_device_name,
            target_device: props.subTask.target_device_name,
            source_device_id: props.subTask.source_device,
            target_device_id: props.subTask.target_device,
            direction: props.subTask.direction,
            description: props.subTask.description,
            is_active: props.subTask.is_active,
            ...(props.mainTaskConfig.target_flowrate != null ? {
                target_flowrate: props.subTask.target_flowrate,
                target_duration: props.subTask.target_duration
            } : {
                target_traffic: props.subTask.target_traffic
            })
        });
        // 计算源终端列表 - 只显示源属地的终端
        const sourceDeviceList = computed(() => {
            return deviceList.value.filter(device => device.location === props.sourceAreaId);
        });

        // 计算目标终端列表 - 只显示目标属地的终端
        const targetDeviceList = computed(() => {
            return deviceList.value.filter(device => device.location === props.targetAreaId);
        });

        // 加载所有终端列表
        const loadDevices = async () => {
            try {
                const res = await request({
                    url: '/api/tss/devices/',
                    method: 'get'
                });

                deviceList.value = res.data || [];
                
                // 强制更新设备ID显示
                // formData.value = {
                //     ...formData.value,
                //     source_device: props.subTask.source_device_name,
                //     target_device: props.subTask.target_device_name
                // };
            } catch (error) {
                console.error('加载终端列表失败:', error);
                ElMessage.error('加载终端列表失败');
            }
        };

        const handleConfirm = async () => {
            if (!formRef.value) return;
            
            try {
                await formRef.value.validate();
                
                // 使用本地数据验证
                validateDeviceFlowrate(formData.value.source_device_id, formData.value.target_device_id, formData.value.target_flowrate);
                
                // 创建新的提交数据，包含parent字段
                const submitData = {
                    ...formData.value,
                    source_device: formData.value.source_device_id,
                    target_device: formData.value.target_device_id,
                    parent: props.subTask.parent, // 添加parent字段
                    // 移除多余的字段
                    source_device_id: undefined,
                    target_device_id: undefined
                };
                            
                emit('confirm', submitData);
                emit('update:visible', false);
            } catch (error) {
                console.error('表单验证失败:', error);
            }
        };

        const validateDeviceFlowrate = (source_device_id: number,target_device_id: number, requiredFlowrate: number) => {
            if (requiredFlowrate == null) {
                return true;
            }
            const source_device = deviceList.value.find(d => d.id === source_device_id);
            if (!source_device) {
                ElMessage.error('源终端设备不存在');
                return false;
            }
            const target_device = deviceList.value.find(d => d.id === target_device_id);
            if (!target_device) {
                ElMessage.error('终端设备不存在');
                return false;
            }            
            // 根据任务方向判断可用带宽
            const source_availableFlowrate = formData.value.direction === 'upload' 
                ? source_device.download_bandwidth
                : source_device.upload_bandwidth ;
            
            const target_availableFlowrate = formData.value.direction === 'upload' 
                ? target_device.upload_bandwidth
                : target_device.download_bandwidth ;
            
            if (source_availableFlowrate < (requiredFlowrate || 0)) {
                ElMessage.error('源终端设备带宽不足');
                return false;
            }
            if (target_availableFlowrate < (requiredFlowrate || 0)) {
                ElMessage.error('目标终端设备带宽不足');
                return false;
            }
            return true;
        };

        onMounted(() => {
            loadDevices();  // 使用合并后的加载方法
        });

        return () => (
            <ElDialog
                modelValue={props.visible}
                title="编辑子任务"
                width="500px"
                onClose={() => emit('update:visible', false)}
                closeOnClickModal={false}
            >
                <ElForm
                    ref={formRef}
                    model={formData.value}
                    labelWidth="140px"
                    rules={{
                        source_device: [{ required: true, message: '请选择源终端' }],
                        target_device: [{ required: true, message: '请选择目标终端' }],
                        direction: [{ required: true, message: '请选择流量方向' }],
                        ...(props.mainTaskConfig.target_flowrate != null ? {
                            target_flowrate: [{ required: true, message: '请输入目标流速' }],
                            target_duration: [{ required: true, message: '请输入目标持续时间' }]
                        } : {
                            target_traffic: [{ required: true, message: '请输入目标流量' }]
                        })
                    }}
                >
                    <ElFormItem
                        label="源终端"
                        prop="source_device"
                    >
                        <ElSelect
                            v-model={formData.value.source_device}
                            placeholder="请选择源终端"
                            clearable
                        >
                            {sourceDeviceList.value.map(device => (
                                <ElOption 
                                    key={device.id} 
                                    label={device.name} 
                                    value={device.id} 
                                />
                            ))}
                        </ElSelect>
                    </ElFormItem>

                    <ElFormItem
                        label="目标终端"
                        prop="target_device"
                    >
                        <ElSelect
                            v-model={formData.value.target_device}
                            placeholder="请选择目标终端"
                            clearable
                        >
                            {targetDeviceList.value.map(device => (
                                <ElOption 
                                    key={device.id} 
                                    label={device.name} 
                                    value={device.id} 
                                />
                            ))}
                        </ElSelect>
                    </ElFormItem>

                    <ElFormItem
                        label="流量方向"
                        prop="direction"
                        rules={[{ required: true, message: '请选择流量方向' }]}
                    >
                        <ElSelect
                            v-model={formData.value.direction}
                            placeholder="请选择流量方向"
                        >
                            <ElOption label="上行" value="up" />
                            <ElOption label="下行" value="down" />
                        </ElSelect>
                    </ElFormItem>

                    {props.mainTaskConfig.target_flowrate != null ? (
                        <>
                            <ElFormItem
                                label="目标流速(Mbit/s)"
                                prop="target_flowrate"
                            >
                                <ElInput
                                    v-model={formData.value.target_flowrate}
                                    type="number"
                                    min={0}
                                />
                            </ElFormItem>

                            <ElFormItem
                                label="目标持续时间(秒)"
                                prop="target_duration"
                            >
                                <ElInput
                                    v-model={formData.value.target_duration}
                                    type="number"
                                    min={0}
                                />
                            </ElFormItem>
                        </>
                    ) : (
                        <ElFormItem
                            label="目标流量(MB)"
                            prop="target_traffic"
                        >
                            <ElInput
                                v-model={formData.value.target_traffic}
                                type="number"
                                min={0}
                            />
                        </ElFormItem>
                    )}
                    <ElFormItem
                        label="是否启用"
                        prop="is_active"
                    >
                        <ElSwitch
                            v-model={formData.value.is_active}
                            activeValue={true}
                            inactiveValue={false}
                            activeText="启用"
                            inactiveText="禁用"
                        />
                    </ElFormItem>

                    <ElFormItem
                        label="描述"
                        prop="description"
                    >
                        <ElInput
                            v-model={formData.value.description}
                            type="textarea"
                            rows={3}
                            placeholder="请输入任务描述"
                        />
                    </ElFormItem>
                </ElForm>
                <div class="dialog-footer">
                    <ElButton onClick={() => emit('update:visible', false)}>取消</ElButton>
                    <ElButton type="primary" onClick={handleConfirm}>确定</ElButton>
                </div>
            </ElDialog>
        );
    }
}); 