import { request } from '/@/utils/service'
import { PageQuery, AddReq, DelReq, EditReq } from '@fast-crud/fast-crud'

export const apiPrefix = '/api/tss/device_monitor/'

export function GetList(query: PageQuery) {
    return request({
        url: apiPrefix,
        method: 'get',
        params: query
    })
}

export function GetObj(id: string) {
    return request({
        url: apiPrefix + id + '/',
        method: 'get'
    })
}

export function getTrafficData(deviceId: number, timeRange: string) {
    return request({
        url: apiPrefix + 'traffic_data/',
        method: 'get',
        params: {
            device_id: deviceId,
            time_range: timeRange
        }
    })
} 