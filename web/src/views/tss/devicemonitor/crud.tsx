import { useI18n } from 'vue-i18n'
import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    compute,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
    useFs
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { SystemConfigStore } from "/@/stores/systemConfig";
import { storeToRefs } from "pinia";
import { computed, toRaw } from "vue";
import { useAreaDictStore } from '/@/stores/areaDict';
import TrafficDataViewer from './components/TrafficDataViewer.vue'
import { column } from 'element-plus/es/components/table-v2/src/common';

interface CrudOptionsContext {
    crudExpose: any;
    trafficViewerRef: any;
}

export function createCrudOptions({ crudExpose, trafficViewerRef }: CrudOptionsContext): CreateCrudOptionsRet {
    const areaStore = useAreaDictStore();

    if (!areaStore.areaTreeData.length) {
        areaStore.getAreaDict();
    }

    const pageRequest = async (query: UserPageQuery) => {
        return await api.GetList(query);
    };

    const systemConfigStore = SystemConfigStore()
    const { systemConfig } = storeToRefs(systemConfigStore)
    const getSystemConfig = computed(() => {
        return systemConfig.value
    })

    const { crudOptions } = {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 },
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            rowHandle: {
                width: 230,
                buttons: {
                    view: {
                        show: auth('devicemonitor:Retrieve'),
                        text: '查看',
                        type: 'success',
                        order: 1
                    },
                    viewtraffic: {
                        show: auth('devicemonitor:Retrieve'),
                        text: '流量数据可视化',
                        type: 'success',
                        order: 2,
                        click: ({ row }: { row: any }) => {
                            if (trafficViewerRef?.value) {
                                trafficViewerRef.value.show(row.device)
                            }
                        }
                    },
                    edit: {
                        show: false
                    },
                    remove: {
                        show: false
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center',
                        show: true
                    }
                },
                device_name: {
                    title: '设备名称',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入设备名称',
                                clearable: true
                            }
                        }
                    },
                    column: {                        
                        width: 150,
                        align: 'center'
                    }
                },
                location: {
                    title: '属地',
                    search: { show: true },
                    type: "dict-tree",
                    dict: dict({
                        data: compute(() => areaStore.areaTreeData),
                        value: 'code',
                        label: 'name',
                        children: 'children'
                    }),
                    form: {
                        component: {
                            name: 'el-tree-select',
                            props: {
                                data: compute(() => areaStore.areaTreeData),
                                clearable: true,
                                filterable: true,
                                checkStrictly: true,
                                props: {
                                    value: 'code',
                                    label: 'name',
                                    children: 'children',
                                },
                                nodeKey: 'code',
                            }
                        }
                    },
                    column: {
                        width: '110px',
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.area_name || ''
                        }
                    }
                },
                create_datetime_range: {
                    title: '创建时间',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'datetimerange',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss',  // 后端接收的格式
                                format: 'YYYY-MM-DD HH:mm:ss',       // 显示的格式
                                defaultTime: [                        // 默认时间数组
                                    new Date(2000, 1, 1, 0, 0, 0),   // 开始时间
                                    new Date(2000, 1, 1, 23, 59, 59) // 结束时间
                                ],
                                editable: true,                      // 允许手动输入
                                disabledDate: (time: Date) => false, // 不禁用任何日期
                                disabledHours: () => [],            // 不禁用任何小时
                                disabledMinutes: () => [],          // 不禁用任何分钟
                                disabledSeconds: () => [],          // 不禁用任何秒钟
                                startPlaceholder: '开始时间',
                                endPlaceholder: '结束时间',
                                clearable: true,
                                shortcuts: [
                                    {
                                        text: '最近一小时',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000)
                                            return [start, end]
                                        }
                                    },
                                    {
                                        text: '最近24小时',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000 * 24)
                                            return [start, end]
                                        }
                                    },
                                    {
                                        text: '最近一周',
                                        value: () => {
                                            const end = new Date()
                                            const start = new Date()
                                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                                            return [start, end]
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    column: {
                        show: false
                    }
                },
                cpu_usage: {
                    title: 'CPU使用率(%)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                memory_usage: {
                    title: '内存使用率(%)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                disk_usage: {
                    title: '磁盘使用率(%)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                recv_rate: {
                    title: '接收速率(Kbps)',
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                sent_rate: {
                    title: '发送速率(Kbps)',
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                total_rate: {
                    title: '总速率(Kbps)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                create_datetime: {
                    title: '创建时间',
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                }
            }
        }
    }

    return {
        crudOptions
    }
} 