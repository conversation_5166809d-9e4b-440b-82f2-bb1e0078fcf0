<template>
  <el-dialog
    v-model="visible"
    title="流量数据可视化"
    width="800px"
    destroy-on-close
    @opened="initChart"
    @closed="cleanupChart"
  >
    <div class="mb-4">
      <el-select v-model="timeRange" @change="loadData">
        <el-option label="最近24小时" value="day" />
        <el-option label="最近一周" value="week" />
        <el-option label="最近一月" value="month" />
        <el-option label="最近一年" value="year" />
      </el-select>
    </div>
    <div ref="chartRef" style="width: 100%; height: 400px;" />
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { request } from '/@/utils/service'
import { ElMessage } from 'element-plus'

const visible = ref(false)
const timeRange = ref('day')
const deviceId = ref<number>()
const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null

const initChart = () => {
  if (chartRef.value && !chart) {
    chart = echarts.init(chartRef.value)
    window.addEventListener('resize', handleResize)
    loadData()  // 初始化图表后立即加载数据
  }
}

const cleanupChart = () => {
  if (chart) {
    window.removeEventListener('resize', handleResize)
    chart.dispose()
    chart = null
  }
}

const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

const show = (id: number) => {
//   console.log('Show called with id:', id)  // 添加调试日志
  deviceId.value = id
  visible.value = true
  // 在dialog打开后会触发initChart
}

const loadData = async () => {
  try {
    const res = await request({
      url: '/api/tss/device_monitor/traffic_data/',
      method: 'get',
      params: {
        device_id: deviceId.value,
        time_range: timeRange.value
      }
    })

    // console.log('API Response:', res)  // 添加调试日志

    if (res.code === 2000) {
    //   console.log('Data for chart:', res.data)  // 添加调试日志
      updateChart(res.data)
    } else {
      ElMessage.error(res.msg || '加载数据失败')
    }
  } catch (error: any) {
    console.error('Load data error:', error)  // 添加详细错误日志
    ElMessage.error(error.message || '加载数据失败')
  }
}

const updateChart = (data: any) => {
  if (!chart) {
    console.error('Chart not initialized')
    return
  }

//   console.log('Updating chart with data:', data)  // 添加调试日志

  const option = {
    title: {
      text: '流量趋势图'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = params[0].axisValue + '<br/>'
        params.forEach((item: any) => {
          result += item.seriesName + ': ' + item.value + ' Kbps<br/>'
        })
        return result
      }
    },
    legend: {
      data: ['接收速率', '发送速率', '总速率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.timestamps,
      axisLabel: {
        formatter: (value: string) => {
          return value.split(' ')[1] // 只显示时间部分
        }
      }
    },
    yAxis: {
      type: 'value',
      name: 'Kbps',
      axisLabel: {
        formatter: '{value} Kbps'
      }
    },
    series: [
      {
        name: '接收速率',
        type: 'line',
        data: data.recv_rates,
        smooth: true
      },
      {
        name: '发送速率',
        type: 'line',
        data: data.sent_rates,
        smooth: true
      },
      {
        name: '总速率',
        type: 'line',
        data: data.total_rates,
        smooth: true
      }
    ]
  }

  try {
    chart.setOption(option)
    // console.log('Chart updated successfully')  // 添加成功日志
  } catch (error) {
    console.error('Error updating chart:', error)  // 添加错误日志
  }
}

onBeforeUnmount(() => {
  cleanupChart()
})

// 暴露方法给父组件
defineExpose({
  show
})
</script>

<style lang="scss" scoped>
.mb-4 {
  margin-bottom: 1rem;
}
</style> 