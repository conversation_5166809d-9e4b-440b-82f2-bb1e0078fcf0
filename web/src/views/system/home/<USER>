<template>
	<div class="home-container">
		<el-row :gutter="15" class="home-card-one mb15">
			<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="(v, k) in homeOne" :key="k"
				:class="{ 'home-media home-media-lg': k > 1, 'home-media-sm': k === 1 }">
				<div class="home-card-item flex">
					<div class="flex-margin flex w100" :class="` home-one-animation${k}`">
						<div class="flex-auto">
							<span class="font30">{{ v.num1 }}</span>
							<span class="ml5 font16" :style="{ color: v.color1 }">{{ v.num2 }}</span>
							<div class="mt10">{{ v.num3 }}</div>
						</div>
						<div class="home-card-item-icon flex" :style="{ background: `var(${v.color2})` }">
							<i class="flex-margin font32" :class="v.num4" :style="{ color: `var(${v.color3})` }"></i>
						</div>
					</div>
				</div>
			</el-col>
		</el-row>
		<el-row :gutter="15" class="home-card-two" style="height: calc(100vh - 250px)">
			<el-col :xs="24" :sm="14" :md="14" :lg="16" :xl="16" style="height: 100%">
				<div class="home-card-item" style="height: 100%">
					<div style="height: 100%" ref="homeLineRef"></div>
				</div>
			</el-col>
			<el-col :xs="24" :sm="10" :md="10" :lg="8" :xl="8" style="height: 100%">
				<div class="home-card-item" style="height: 100%">
					<div class="home-card-item-title">属地流量统计</div>
					<div class="home-monitor" style="height: calc(100% - 35px); display: flex; align-items: center;">
						<div class="flex-warp" style="width: 100%;">
							<div class="area-grid">
								<div class="area-item" v-for="(item, k) in areaStats" :key="k">
									<div class="flex-warp-item-box" :class="`home-animation${k}`">
										<div class="flex-margin">
											<div class="area-name">{{ formatAreaName(item.name) }}</div>
											<div class="traffic-info">
												<div class="traffic-row">
													<div class="traffic-left">
														<div class="traffic-label" style="color:cadetblue">固网</div>
														<div class="traffic-value" style="color:cadetblue">{{
															item.fixed_traffic }}GB</div>
													</div>
												</div>
												<div class="traffic-row">
													<div class="traffic-left">
														<div class="traffic-label" style="color: #6690F9">移动</div>
														<div class="traffic-value" style="color: #6690F9">{{
															item.mobile_traffic }}GB</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</el-col>
		</el-row>

	</div>
</template>

<script lang="ts">
import { toRefs, reactive, defineComponent, onMounted, ref, watch, nextTick, onActivated } from 'vue';
import * as echarts from 'echarts';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { getDashboardInfo } from '/@/api/system/dashboard';

let global: any = {
	homeChartOne: null,
	homeChartTwo: null,
	homeCharThree: null,
	dispose: [null, '', undefined],
};

export default defineComponent({
	name: 'home',
	setup() {
		const homeLineRef = ref();
		const homePieRef = ref();
		const homeBarRef = ref();
		const storesTagsViewRoutes = useTagsViewRoutes();
		const storesThemeConfig = useThemeConfig();
		const { themeConfig } = storeToRefs(storesThemeConfig);
		const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);
		const state = reactive({
			homeOne: [
				{
					num1: '0',
					num2: 'GB',
					num3: '累计上行流量',
					num4: 'el-icon-upload',
					color1: '#6690F9',
					color2: '--next-color-primary-lighter',
					color3: '--el-color-primary',
				},
				{
					num1: '0',
					num2: 'GB',
					num3: '累计下行流量',
					num4: 'el-icon-download',
					color1: '#6690F9',
					color2: '--next-color-success-lighter',
					color3: '--el-color-success',
				},
				{
					num1: '0',
					num2: 'GB',
					num3: '累计总流量',
					num4: 'el-icon-data-line',
					color1: '#6690F9',
					color2: '--next-color-warning-lighter',
					color3: '--el-color-warning',
				},
				{
					num1: '0',
					num2: '个',
					num3: '终端数量',
					num4: 'el-icon-monitor',
					color1: '#6690F9',
					color2: '--next-color-danger-lighter',
					color3: '--el-color-danger',
				},
			],
			areaStats: [],
			myCharts: [],
			charts: {
				theme: '',
				bgColor: '',
				color: '#303133',
			},
			total_upload: 0,
			total_download: 0,
			total_traffic: 0,
			device_count: 0,
			chartData: {
				dates: [],
				fixed_data: [],
				mobile_data: [],
				pie_data: []
			}
		});
		// 折线图
		const initLineChart = () => {
			if (!global.dispose.some((b: any) => b === global.homeChartOne)) global.homeChartOne.dispose();
			global.homeChartOne = <any>echarts.init(homeLineRef.value, state.charts.theme);
			const option = {
				backgroundColor: state.charts.bgColor,
				title: {
					text: '全省固网/移动网流量趋势(GB)',
					x: 'left',
					textStyle: { fontSize: '15', color: state.charts.color },
				},
				grid: {
					top: 60,
					right: 30,
					bottom: 20,
					left: 60
				},
				tooltip: {
					trigger: 'axis',
					formatter: function (params: any) {
						let result = params[0].axisValue + '<br/>';
						params.forEach((item: any) => {
							result += item.seriesName + ': ' + item.value + ' GB<br/>';
						});
						return result;
					}
				},
				legend: {
					data: ['固网流量', '移动网流量'],
					right: 0
				},
				xAxis: {
					data: state.chartData.dates || [],
					axisLabel: {
						rotate: 0,
						formatter: function (value: string) {
							return value.split('-')[1] + '日';
						},
						interval: 0,
						padding: [2, 0, 0, 3]
					}
				},
				yAxis: [
					{
						type: 'value',
						name: '流量(GB)',
						nameGap: 13,
						axisLabel: {
							formatter: '{value} GB',
							margin: 0
						}
					}
				],
				series: [
					{
						name: '固网流量',
						type: 'line',
						data: state.chartData.fixed_data || [],
						smooth: true,
						areaStyle: {
							opacity: 0.3
						},
						itemStyle: {
							color: '#FF6462'
						}
					},
					{
						name: '移动网流量',
						type: 'line',
						data: state.chartData.mobile_data || [],
						smooth: true,
						areaStyle: {
							opacity: 0.3
						},
						itemStyle: {
							color: '#6690F9'
						}
					}
				]
			};
			global.homeChartOne.setOption(option);
		};

		// 页面加载时
		onMounted(() => {
			getHomeData();
		});
		// 由于页面缓存原因，keep-alive

		// 监听 vuex 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等

		// 监听 vuex 中是否开启深色主题
		watch(
			() => themeConfig.value.isIsDark,
			(isIsDark) => {
				nextTick(() => {
					state.charts.theme = isIsDark ? 'dark' : '';
					state.charts.bgColor = isIsDark ? 'transparent' : '';
					state.charts.color = isIsDark ? '#dadada' : '#303133';
					setTimeout(() => {
						initLineChart();
					}, 500);
					setTimeout(() => {
					}, 700);
					setTimeout(() => {
					}, 1000);
				});
			},
			{
				deep: true,
				immediate: true,
			}
		);
		const getHomeData = async () => {
			try {
				const res = await getDashboardInfo();
				if (res.code === 2000) {
					state.homeOne[0].num1 = res.data.total_upload.toString();
					state.homeOne[1].num1 = res.data.total_download.toString();
					state.homeOne[2].num1 = res.data.total_traffic.toString();
					state.homeOne[3].num1 = res.data.device_count.toString();

					// 更新图表数据
					state.chartData = res.data.chart_data;
					initLineChart(); // 重新渲染图表

					// 更新快捷导航数据
					state.areaStats = res.data.area_stats;
				}
			} catch (error) {
				console.error('获取首页数据失败:', error);
			}
		};
		// 修改属地名称格式化方法
		const formatAreaName = (name: string) => {
			if (name === '西双版纳傣族自治州') {
				return '西双版纳';  // 显示完整的西双版纳
			}
			return name.substring(0, 2);  // 其他属地显示前两个字
		};
		return {
			homeLineRef,
			homePieRef,
			homeBarRef,
			...toRefs(state),
			formatAreaName,
		};
	},
});
</script>

<style scoped lang="scss">
$homeNavLengh: 8;

.home-container {
	height: 100%;

	.home-card-one,
	.home-card-two,
	.home-card-three {
		.home-card-item {
			width: 100%;
			height: 130px;
			border-radius: 4px;
			transition: all ease 0.3s;
			padding: 20px;
			overflow: hidden;
			background: var(--el-color-white);
			color: var(--el-text-color-primary);
			border: 1px solid var(--next-border-color-light);

			&:hover {
				box-shadow: 0 2px 12px var(--next-color-dark-hover);
				transition: all ease 0.3s;
			}

			&-icon {
				width: 70px;
				height: 70px;
				border-radius: 100%;
				flex-shrink: 1;

				i {
					color: var(--el-text-color-placeholder);
				}
			}

			&-title {
				font-size: 15px;
				font-weight: bold;
				height: 30px;
			}
		}
	}

	.home-card-one {
		@for $i from 0 through 3 {
			.home-one-animation#{$i} {
				opacity: 0;
				animation-name: error-num;
				animation-duration: 0.5s;
				animation-fill-mode: forwards;
				animation-delay: calc($i/10) + s;
			}
		}
	}

	.home-card-two,
	.home-card-three {
		.home-card-item {
			height: 400px;
			width: 100%;
			overflow: hidden;

			.home-monitor {
				height: 100%;

				.flex-warp {
					width: 100%;
					padding: 0;
					margin-bottom: 0;

					.area-grid {
						display: grid;
						grid-template-columns: repeat(4, 1px);
						grid-template-rows: repeat(4, minmax(80px, 1fr));
						gap: 12px;
						height: 100%;
						padding: 0 8px;

						.area-item {
							.flex-warp-item-box {
								height: 100%;
								padding: 10px;
								display: flex;
								flex-direction: column;
								justify-content: center;
								border: 1px solid var(--el-border-color-lighter);
								border-radius: 4px;
								background: var(--next-bg-color);
								transition: all 0.3s ease;

								&:hover {
									border-color: var(--el-border-color);
									box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
								}

								.area-name {
									font-size: 16px;
									margin-bottom: 10px;
								}

								.traffic-info {
									width: 100%;

									.traffic-row {
										margin-bottom: 8px;

										.traffic-left {
											display: flex;
											justify-content: flex-start;
											align-items: center;
											width: 100%;
											padding: 0 1px;

											.traffic-label {
												font-size: 13px;
												min-width: 26px;
											}

											.traffic-value {
												font-size: 13px;
												margin-left: 2px;
												font-weight: 500;
											}
										}

										&:last-child {
											margin-bottom: 0;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.home-card-two {
		margin-bottom: 0;

		.home-card-item {
			// padding: 10px 10px 10px 20px; // 移除左右内边距，只保留上下内边距
			align-items: center;

			.home-card-item-title {
				text-align: center;
				margin-bottom: 10px;
			}

			.home-monitor {
				.flex-warp {
					width: 100%;

					.area-grid {
						display: grid;
						grid-template-columns: repeat(4, 1fr); // 增加每列的宽度分配
						grid-template-rows: repeat(4, 1fr);
						gap: 25px; // 增加行间距
						height: 100%;
						padding: 0 10px; // 添加小的左右内边距，避免贴边

						.area-item {
							.flex-warp-item-box {
								height: 100%;
								padding: 8px;
								display: flex;
								flex-direction: column;
								justify-content: center;
								border: 1px solid var(--el-border-color-lighter);
								border-radius: 4px;
								background: var(--next-bg-color);
								transition: all 0.3s ease;

								&:hover {
									border-color: var(--el-border-color);
									box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
								}

								.area-name {
									font-size: 16px; // 稍微减小字体
									font-weight: bold;
									margin-bottom: 8px; // 减小底部间距
									text-align: center;
									width: 100%;
									white-space: nowrap; // 防止文字换行
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.traffic-info {
									width: 100%; // 确保宽度100%

									.traffic-row {
										margin-bottom: 6px;

										.traffic-left {
											display: flex;
											justify-content: flex-start; // 改为左对齐
											align-items: center;
											width: 100%;
											padding: 0 1px; // 减小左右内边距

											.traffic-label {
												font-size: 13px;
												min-width: 26px; // 减小标签宽度
											}

											.traffic-value {
												font-size: 13px;
												margin-left: 2px; // 减小标签和数值之间的间距
												font-weight: 500;
											}
										}

										&:last-child {
											margin-bottom: 0;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
