import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';

export function createCrudOptions(): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getBrasList(query);
    };

    const editRequest = async ({ form, row }: EditReq) => {
        if (form.id) {
            return await api.updateBras(form.id, form);
        }
    };

    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteBras(row.id);
    };

    const addRequest = async ({ form }: AddReq) => {
        return await api.createBras(form);
    };

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('vpdn:bras:create'),
                        text: '新增设备',
                        type: 'primary'
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 }
            },
            rowHandle: {
                width: 200,
                buttons: {
                    view: {
                        show: auth('vpdn:bras:view'),
                        text: '查看',
                        type: 'success'
                    },
                    edit: {
                        show: auth('vpdn:bras:update'),
                        text: '编辑',
                        type: 'primary'
                    },
                    remove: {
                        show: auth('vpdn:bras:delete'),
                        text: '删除',
                        type: 'danger'
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                bras_name: {
                    title: '设备名称',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '设备名称不能为空' }]
                    },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                bras_ip: {
                    title: '设备IP',
                    search: { show: true },
                    form: {
                        rules: [
                            { required: true, message: '设备IP不能为空' },
                            { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' }
                        ]
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                bras_type: {
                    title: '设备型号',
                    search: { show: true },
                    form: {
                        rules: [{ required: true, message: '设备型号不能为空' }]
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                bras_vendor: {
                    title: '厂商',
                    search: { show: true },
                    form: {
                        component: {
                            name: 'el-select',
                            props: {
                                placeholder: '请选择厂商'
                            }
                        },
                        options: [
                            { value: '华为', label: '华为' },
                            { value: '思科', label: '思科' },
                            { value: '中兴', label: '中兴' },
                            { value: '烽火', label: '烽火' }
                        ]
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                },
                bras_area: {
                    title: '区域编号',
                    search: { show: true },
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域编号'
                            }
                        }
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                },
                radius_secret: {
                    title: 'RADIUS共享密钥',
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                type: 'password',
                                placeholder: '请输入RADIUS共享密钥',
                                showPassword: true
                            }
                        }
                    },
                    column: {
                        width: 150,
                        align: 'center',
                        formatter: () => '******'
                    }
                },
                bras_description: {
                    title: '描述',
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                type: 'textarea',
                                rows: 3,
                                placeholder: '请输入设备描述'
                            }
                        }
                    },
                    column: {
                        width: 200,
                        align: 'left',
                        showOverflowTooltip: true
                    }
                },
                create_datetime: {
                    title: '创建时间',
                    form: { show: false },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                }
            }
        }
    };
}
