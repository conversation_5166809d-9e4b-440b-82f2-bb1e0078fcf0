<template>
  <div class="bras-container">
    <h1>BRAS设备管理</h1>
    <p>这里是BRAS设备管理页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="bras_name" label="设备名称" width="150"></el-table-column>
      <el-table-column prop="bras_ip" label="设备IP" width="130"></el-table-column>
      <el-table-column prop="bras_type" label="设备型号" width="120"></el-table-column>
      <el-table-column prop="bras_vendor" label="厂商" width="100"></el-table-column>
      <el-table-column prop="bras_area" label="区域编号" width="100"></el-table-column>
      <el-table-column prop="bras_description" label="描述"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnBrasSimple'
});

const mockData = reactive([
  {
    bras_name: 'BRAS-001',
    bras_ip: '*************',
    bras_type: 'ME60-X16',
    bras_vendor: '华为',
    bras_area: '001',
    bras_description: '核心BRAS设备'
  },
  {
    bras_name: 'BRAS-002',
    bras_ip: '*************',
    bras_type: 'ASR9000',
    bras_vendor: '思科',
    bras_area: '002',
    bras_description: '备用BRAS设备'
  }
]);
</script>

<style scoped lang="scss">
.bras-container {
  padding: 20px;
}
</style>
