<template>
  <div class="bras-container">
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </fs-crud>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';

defineOptions({
  name: 'VpdnBras'
});

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  const headers = ['设备名称', '设备IP', '设备型号', '厂商', '区域编号', '描述', '创建时间'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.bras_name || '',
      row.bras_ip || '',
      row.bras_type || '',
      row.bras_vendor || '',
      row.bras_area || '',
      (row.bras_description || '').replace(/,/g, '，'),
      row.create_datetime || ''
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `BRAS设备_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};
</script>

<style scoped lang="scss">
.bras-container {
  padding: 20px;
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
  }
}
</style>
