<template>
  <div class="user-container">
    <!-- 统计卡片 -->
    <el-row :gutter="15" class="stats-row mb15">
      <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" v-for="stat in userStats" :key="stat.key">
        <div class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
          <div class="stat-icon" :style="{ color: stat.color }">
            <i :class="stat.icon"></i>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- CRUD表格 -->
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <el-button type="warning" @click="showBatchDialog">
          <el-icon><Operation /></el-icon>
          批量操作
        </el-button>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
        <el-button type="info" @click="refreshStats">
          <el-icon><Refresh /></el-icon>
          刷新统计
        </el-button>
      </template>
    </fs-crud>

    <!-- 批量操作对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量操作" width="500px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="操作类型">
          <el-select v-model="batchForm.operation" placeholder="请选择操作类型">
            <el-option label="批量暂停" value="suspend"></el-option>
            <el-option label="批量恢复" value="resume"></el-option>
            <el-option label="批量注销" value="cancel"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选中用户">
          <div class="selected-users">
            已选择 {{ selectedUsers.length }} 个用户
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executeBatchOperation">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Operation, Download, Refresh } from '@element-plus/icons-vue';
import * as api from './api';

// 页面标题
defineOptions({
  name: 'VpdnUser'
});

const { crudRef, crudBinding, crudExpose } = useFs({ 
  createCrudOptions: () => createCrudOptions({ crudExpose }) 
});

// 用户统计数据
const userStats = ref([
  { key: 'total', label: '总用户数', value: '0', icon: 'el-icon-user', color: '#409EFF' },
  { key: 'active', label: '活跃用户', value: '0', icon: 'el-icon-check', color: '#67C23A' },
  { key: 'suspended', label: '暂停用户', value: '0', icon: 'el-icon-warning', color: '#E6A23C' },
  { key: 'cancelled', label: '注销用户', value: '0', icon: 'el-icon-close', color: '#F56C6C' }
]);

// 批量操作相关
const batchDialogVisible = ref(false);
const selectedUsers = ref([]);
const batchForm = reactive({
  operation: ''
});

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    const res = await api.getUserStatistics();
    if (res.code === 2000) {
      const stats = res.data.user_stats;
      userStats.value[0].value = stats.total_users.toString();
      userStats.value[1].value = stats.active_users.toString();
      userStats.value[2].value = stats.suspended_users.toString();
      userStats.value[3].value = stats.cancelled_users.toString();
    }
  } catch (error) {
    console.error('加载用户统计失败:', error);
  }
};

// 显示批量操作对话框
const showBatchDialog = () => {
  const selection = crudExpose.getSelectedRows();
  if (!selection || selection.length === 0) {
    ElMessage.warning('请先选择要操作的用户');
    return;
  }
  selectedUsers.value = selection;
  batchDialogVisible.value = true;
};

// 执行批量操作
const executeBatchOperation = async () => {
  if (!batchForm.operation) {
    ElMessage.warning('请选择操作类型');
    return;
  }

  try {
    const userIds = selectedUsers.value.map((user: any) => user.id);
    await api.batchOperation({
      user_ids: userIds,
      operation: batchForm.operation
    });
    
    ElMessage.success('批量操作成功');
    batchDialogVisible.value = false;
    batchForm.operation = '';
    crudExpose.doRefresh();
    loadUserStats();
  } catch (error) {
    ElMessage.error('批量操作失败');
  }
};

// 导出数据
const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  const headers = ['用户名', '用户域名', '业务类型', '用户状态', '区域', '上行带宽', '下行带宽', '流量限制', '创建时间'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.user_name || '',
      row.user_domain || '',
      row.user_business_type || '',
      row.user_status || '',
      row.user_area || '',
      row.user_bandwidth_up || '',
      row.user_bandwidth_down || '',
      row.user_traffic_limit || '',
      row.create_datetime || ''
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `用户管理_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};

// 刷新统计
const refreshStats = () => {
  loadUserStats();
  ElMessage.success('统计数据已刷新');
};

onMounted(() => {
  loadUserStats();
});
</script>

<style scoped lang="scss">
.user-container {
  padding: 20px;
  
  .stats-row {
    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
      
      .stat-content {
        flex: 1;
        
        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #606266;
          margin-top: 4px;
        }
      }
      
      .stat-icon {
        font-size: 32px;
        margin-left: 15px;
      }
    }
  }
  
  .selected-users {
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
    color: #606266;
  }
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
    
    .el-table {
      .el-table__header {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>
