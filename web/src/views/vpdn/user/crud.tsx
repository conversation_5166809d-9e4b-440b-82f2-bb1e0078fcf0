import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';

interface CrudOptionsContext {
    crudExpose: any;
}

export function createCrudOptions({ crudExpose }: CrudOptionsContext): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getUserList(query);
    };

    const editRequest = async ({ form, row }: EditReq) => {
        if (form.id) {
            return await api.updateUser(form.id, form);
        }
    };

    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteUser(row.id);
    };

    const addRequest = async ({ form }: AddReq) => {
        return await api.createUser(form);
    };

    // 用户状态字典
    const userStatusDict = dict({
        data: [
            { value: 0, label: '正常', color: 'success' },
            { value: 1, label: '暂停', color: 'warning' },
            { value: 2, label: '锁定', color: 'danger' },
            { value: 3, label: '注销', color: 'info' }
        ]
    });

    // 业务类型字典
    const businessTypeDict = dict({
        data: [
            { value: 1, label: 'ADSL' },
            { value: 2, label: 'LAN' },
            { value: 3, label: 'VLAN' },
            { value: 4, label: 'PPPoE' }
        ]
    });

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('vpdn:user:create'),
                        text: '新增用户',
                        type: 'primary'
                    },
                    batchOp: {
                        text: '批量操作',
                        type: 'warning',
                        click: () => {
                            const selection = crudExpose.getSelectedRows();
                            if (!selection || selection.length === 0) {
                                ElMessage.warning('请先选择要操作的用户');
                                return;
                            }
                            showBatchOperationDialog(selection);
                        }
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 },
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            table: {
                rowSelection: {
                    enabled: true
                }
            },
            rowHandle: {
                width: 300,
                buttons: {
                    view: {
                        show: auth('vpdn:user:view'),
                        text: '查看',
                        type: 'success'
                    },
                    edit: {
                        show: auth('vpdn:user:update'),
                        text: '编辑',
                        type: 'primary'
                    },
                    changePassword: {
                        show: auth('vpdn:user:update'),
                        text: '改密',
                        type: 'warning',
                        click: ({ row }: { row: any }) => {
                            showChangePasswordDialog(row);
                        }
                    },
                    suspend: {
                        show: auth('vpdn:user:update'),
                        text: '暂停',
                        type: 'warning',
                        click: ({ row }: { row: any }) => {
                            handleUserOperation(row, 'suspend');
                        }
                    },
                    resume: {
                        show: auth('vpdn:user:update'),
                        text: '恢复',
                        type: 'success',
                        click: ({ row }: { row: any }) => {
                            handleUserOperation(row, 'resume');
                        }
                    },
                    remove: {
                        show: auth('vpdn:user:delete'),
                        text: '删除',
                        type: 'danger'
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                user_name: {
                    title: '用户名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入用户名',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '用户名不能为空' },
                            { min: 3, max: 40, message: '用户名长度在3到40个字符' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入用户名'
                            }
                        }
                    },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                user_domain: {
                    title: '用户域名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '用户域名不能为空' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入用户域名'
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                user_business_type: {
                    title: '业务类型',
                    type: 'dict-select',
                    dict: businessTypeDict,
                    search: { show: true },
                    form: {
                        rules: [
                            { required: true, message: '请选择业务类型' }
                        ]
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                },
                user_status: {
                    title: '用户状态',
                    type: 'dict-select',
                    dict: userStatusDict,
                    search: { show: true },
                    form: {
                        value: 0
                    },
                    column: {
                        width: 100,
                        align: 'center',
                        component: {
                            name: 'fs-dict-tag'
                        }
                    }
                },
                user_area: {
                    title: '区域',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域'
                            }
                        }
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                },
                user_bandwidth_up: {
                    title: '上行带宽(Kbps)',
                    form: {
                        component: {
                            name: 'el-input-number',
                            props: {
                                min: 0,
                                placeholder: '请输入上行带宽'
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_bandwidth_down: {
                    title: '下行带宽(Kbps)',
                    form: {
                        component: {
                            name: 'el-input-number',
                            props: {
                                min: 0,
                                placeholder: '请输入下行带宽'
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_traffic_limit: {
                    title: '流量限制(MB)',
                    form: {
                        component: {
                            name: 'el-input-number',
                            props: {
                                min: 0,
                                placeholder: '请输入流量限制'
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.user_traffic_limit ? `${(row.user_traffic_limit / 1024 / 1024).toFixed(2)} MB` : '-';
                        }
                    }
                },
                create_datetime: {
                    title: '创建时间',
                    form: { show: false },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                }
            }
        }
    };

    // 批量操作对话框
    function showBatchOperationDialog(selection: any[]) {
        ElMessageBox.confirm(
            `确定要对选中的 ${selection.length} 个用户执行批量操作吗？`,
            '批量操作确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            // 这里可以添加批量操作的具体逻辑
            ElMessage.success('批量操作成功');
            crudExpose.doRefresh();
        });
    }

    // 修改密码对话框
    function showChangePasswordDialog(row: any) {
        ElMessageBox.prompt('请输入新密码', '修改密码', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'password',
            inputValidator: (value: string) => {
                if (!value || value.length < 6) {
                    return '密码长度不能少于6位';
                }
                return true;
            }
        }).then(async ({ value }) => {
            await api.changePassword(row.id, { new_password: value });
            ElMessage.success('密码修改成功');
        });
    }

    // 用户操作处理
    async function handleUserOperation(row: any, operation: string) {
        const operationMap: any = {
            suspend: { text: '暂停', api: api.suspendUser },
            resume: { text: '恢复', api: api.resumeUser },
            cancel: { text: '注销', api: api.cancelUser }
        };

        const op = operationMap[operation];
        if (!op) return;

        try {
            await ElMessageBox.confirm(
                `确定要${op.text}用户 ${row.user_name} 吗？`,
                `${op.text}用户确认`,
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            );

            await op.api(row.id);
            ElMessage.success(`用户${op.text}成功`);
            crudExpose.doRefresh();
        } catch (error) {
            // 用户取消操作
        }
    }
}
