<template>
  <div class="user-container">
    <h1>用户管理</h1>
    <p>这里是VPDN用户管理页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
      <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
      <el-table-column prop="user_business_type" label="业务类型" width="100"></el-table-column>
      <el-table-column prop="user_status" label="用户状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.user_status)">
            {{ getStatusText(row.user_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="user_area" label="区域" width="100"></el-table-column>
      <el-table-column prop="create_datetime" label="创建时间" width="180"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnUserSimple'
});

const mockData = reactive([
  {
    user_name: 'testuser001',
    user_domain: 'test.vpdn.com',
    user_business_type: 'ADSL',
    user_status: 0,
    user_area: '001',
    create_datetime: '2024-01-01 10:00:00'
  },
  {
    user_name: 'testuser002',
    user_domain: 'demo.vpdn.com',
    user_business_type: 'LAN',
    user_status: 1,
    user_area: '002',
    create_datetime: '2024-01-02 11:00:00'
  }
]);

const getStatusType = (status: number) => {
  const types: any = { 0: 'success', 1: 'warning', 2: 'danger', 3: 'info' };
  return types[status] || 'info';
};

const getStatusText = (status: number) => {
  const texts: any = { 0: '正常', 1: '暂停', 2: '锁定', 3: '注销' };
  return texts[status] || '未知';
};
</script>

<style scoped lang="scss">
.user-container {
  padding: 20px;
}
</style>
