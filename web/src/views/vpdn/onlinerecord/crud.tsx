import * as api from './api';
import {
    dict,
    UserPageQuery,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';

interface CrudOptionsContext {
    crudExpose: any;
}

export function createCrudOptions({ crudExpose }: CrudOptionsContext): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getOnlineRecordList(query);
    };

    return {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                buttons: {
                    add: {
                        show: false
                    },
                    traceQuery: {
                        text: '溯源查询',
                        type: 'primary',
                        click: () => {
                            // 触发溯源查询对话框
                            crudExpose.emit('showTraceDialog');
                        }
                    },
                    realTimeMonitor: {
                        text: '实时监控',
                        type: 'success',
                        click: () => {
                            // 触发实时监控
                            crudExpose.emit('showMonitorDialog');
                        }
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 },
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            rowHandle: {
                width: 200,
                buttons: {
                    view: {
                        show: auth('vpdn:online:view'),
                        text: '查看',
                        type: 'success'
                    },
                    forceOffline: {
                        show: auth('vpdn:online:offline'),
                        text: '强制下线',
                        type: 'danger',
                        click: ({ row }: { row: any }) => {
                            handleForceOffline(row);
                        }
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                user_name: {
                    title: '用户名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入用户名',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                user_domain: {
                    title: '用户域名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                user_business_type: {
                    title: '业务类型',
                    search: {
                        show: true,
                        component: {
                            name: 'el-select',
                            props: {
                                placeholder: '请选择业务类型',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                },
                online_time: {
                    title: '上线时间',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'datetimerange',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                                format: 'YYYY-MM-DD HH:mm:ss',
                                startPlaceholder: '开始时间',
                                endPlaceholder: '结束时间',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                bras_ip: {
                    title: 'BRAS IP',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入BRAS IP',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_framedip: {
                    title: '用户IP',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入用户IP',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_framedipv6: {
                    title: '用户IPv6',
                    column: {
                        width: 200,
                        align: 'center',
                        showOverflowTooltip: true
                    }
                },
                user_mac: {
                    title: 'MAC地址',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入MAC地址',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                session_timeout: {
                    title: '会话超时(秒)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                idle_timeout: {
                    title: '空闲超时(秒)',
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                user_area: {
                    title: '区域',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域',
                                clearable: true
                            }
                        }
                    },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                }
            }
        }
    };

    // 强制下线处理
    async function handleForceOffline(row: any) {
        try {
            await ElMessageBox.confirm(
                `确定要强制下线用户 ${row.user_name}@${row.user_domain} 吗？`,
                '强制下线确认',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                }
            );

            await api.forceOffline({
                user_name: row.user_name,
                user_domain: row.user_domain,
                reason: '管理员强制下线'
            });
            
            ElMessage.success('用户强制下线成功');
            crudExpose.doRefresh();
        } catch (error) {
            // 用户取消操作
        }
    }
}
