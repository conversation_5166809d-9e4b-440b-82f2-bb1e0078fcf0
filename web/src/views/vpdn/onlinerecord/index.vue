<template>
  <div class="online-record-container">
    <!-- 实时监控卡片 -->
    <el-row :gutter="15" class="monitor-row mb15">
      <el-col :xs="24" :sm="6" :md="6" :lg="6" :xl="6" v-for="stat in monitorStats" :key="stat.key">
        <div class="monitor-card">
          <div class="monitor-content">
            <div class="monitor-number">{{ stat.value }}</div>
            <div class="monitor-label">{{ stat.label }}</div>
          </div>
          <div class="monitor-icon" :style="{ color: stat.color }">
            <i :class="stat.icon"></i>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- CRUD表格 -->
    <fs-crud ref="crudRef" v-bind="crudBinding" @showTraceDialog="showTraceDialog" @showMonitorDialog="showMonitorDialog">
      <template #actionbar-right>
        <el-button type="primary" @click="showTraceDialog">
          <el-icon><Search /></el-icon>
          溯源查询
        </el-button>
        <el-button type="success" @click="refreshMonitor">
          <el-icon><Refresh /></el-icon>
          刷新监控
        </el-button>
        <el-button type="info" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </fs-crud>

    <!-- 溯源查询对话框 -->
    <el-dialog v-model="traceDialogVisible" title="溯源查询" width="600px">
      <el-form :model="traceForm" :rules="traceRules" ref="traceFormRef" label-width="100px">
        <el-form-item label="溯源类型" prop="trace_type">
          <el-select v-model="traceForm.trace_type" placeholder="请选择溯源类型">
            <el-option label="IP地址" value="ip"></el-option>
            <el-option label="IPv6地址" value="ipv6"></el-option>
            <el-option label="MAC地址" value="mac"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="溯源值" prop="trace_value">
          <el-input v-model="traceForm.trace_value" placeholder="请输入要溯源的值"></el-input>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="traceTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="LNS过滤">
          <el-input v-model="traceForm.lns_filter" placeholder="请输入LNS设备IP（可选）"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="traceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="executeTraceQuery" :loading="traceLoading">查询</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 溯源结果对话框 -->
    <el-dialog v-model="traceResultVisible" title="溯源查询结果" width="80%">
      <div v-if="traceResults.length === 0" class="no-data">
        <el-empty description="未找到相关记录"></el-empty>
      </div>
      <div v-else>
        <el-table :data="traceResults" border stripe>
          <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
          <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
          <el-table-column prop="online_time" label="上线时间" width="180"></el-table-column>
          <el-table-column prop="bras_ip" label="BRAS IP" width="130"></el-table-column>
          <el-table-column prop="user_framedip" label="用户IP" width="130"></el-table-column>
          <el-table-column prop="user_mac" label="MAC地址" width="150"></el-table-column>
          <el-table-column prop="user_area" label="区域" width="100"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 实时监控对话框 -->
    <el-dialog v-model="monitorDialogVisible" title="实时监控面板" width="80%">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="monitor-panel">
            <h4>BRAS设备统计</h4>
            <el-table :data="brasStats" size="small">
              <el-table-column prop="bras_ip" label="BRAS IP"></el-table-column>
              <el-table-column prop="online_count" label="在线用户数"></el-table-column>
            </el-table>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="monitor-panel">
            <h4>域名统计</h4>
            <el-table :data="domainStats" size="small">
              <el-table-column prop="user_domain" label="域名"></el-table-column>
              <el-table-column prop="online_count" label="在线用户数"></el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
      <div class="monitor-panel mt20">
        <h4>最近上线用户</h4>
        <el-table :data="recentOnline" size="small">
          <el-table-column prop="user_name" label="用户名"></el-table-column>
          <el-table-column prop="user_domain" label="域名"></el-table-column>
          <el-table-column prop="online_time" label="上线时间"></el-table-column>
          <el-table-column prop="nas_ip" label="NAS IP"></el-table-column>
          <el-table-column prop="framed_ip" label="用户IP"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage } from 'element-plus';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import * as api from './api';

// 页面标题
defineOptions({
  name: 'VpdnOnlineRecord'
});

const { crudRef, crudBinding, crudExpose } = useFs({ 
  createCrudOptions: () => createCrudOptions({ crudExpose }) 
});

// 监控统计数据
const monitorStats = ref([
  { key: 'total', label: '总在线数', value: '0', icon: 'el-icon-connection', color: '#409EFF' },
  { key: 'ipv4', label: 'IPv4用户', value: '0', icon: 'el-icon-network', color: '#67C23A' },
  { key: 'ipv6', label: 'IPv6用户', value: '0', icon: 'el-icon-share', color: '#E6A23C' },
  { key: 'recent', label: '最近上线', value: '0', icon: 'el-icon-time', color: '#F56C6C' }
]);

// 溯源查询相关
const traceDialogVisible = ref(false);
const traceResultVisible = ref(false);
const traceLoading = ref(false);
const traceFormRef = ref();
const traceTimeRange = ref([]);
const traceResults = ref([]);

const traceForm = reactive({
  trace_type: '',
  trace_value: '',
  lns_filter: ''
});

const traceRules = {
  trace_type: [{ required: true, message: '请选择溯源类型', trigger: 'change' }],
  trace_value: [{ required: true, message: '请输入溯源值', trigger: 'blur' }]
};

// 实时监控相关
const monitorDialogVisible = ref(false);
const brasStats = ref([]);
const domainStats = ref([]);
const recentOnline = ref([]);

// 加载实时监控数据
const loadMonitorData = async () => {
  try {
    const res = await api.getRealTimeMonitor();
    if (res.code === 2000) {
      const data = res.data;
      monitorStats.value[0].value = data.online_stats.total_online.toString();
      monitorStats.value[1].value = data.online_stats.total_ipv4_users.toString();
      monitorStats.value[2].value = data.online_stats.total_ipv6_users.toString();
      
      brasStats.value = data.bras_stats || [];
      domainStats.value = data.domain_stats || [];
      recentOnline.value = data.recent_online || [];
    }
  } catch (error) {
    console.error('加载监控数据失败:', error);
  }
};

// 显示溯源查询对话框
const showTraceDialog = () => {
  traceDialogVisible.value = true;
};

// 显示监控对话框
const showMonitorDialog = () => {
  loadMonitorData();
  monitorDialogVisible.value = true;
};

// 执行溯源查询
const executeTraceQuery = async () => {
  if (!traceFormRef.value) return;
  
  try {
    await traceFormRef.value.validate();
    traceLoading.value = true;
    
    const queryData: any = {
      trace_type: traceForm.trace_type,
      trace_value: traceForm.trace_value
    };
    
    if (traceTimeRange.value && traceTimeRange.value.length === 2) {
      queryData.start_time = traceTimeRange.value[0];
      queryData.end_time = traceTimeRange.value[1];
    }
    
    if (traceForm.lns_filter) {
      queryData.lns_filter = traceForm.lns_filter;
    }
    
    const res = await api.traceQuery(queryData);
    if (res.code === 2000) {
      traceResults.value = res.data.online_records || [];
      traceDialogVisible.value = false;
      traceResultVisible.value = true;
      
      if (traceResults.value.length === 0) {
        ElMessage.info('未找到相关记录');
      } else {
        ElMessage.success(`找到 ${traceResults.value.length} 条相关记录`);
      }
    }
  } catch (error) {
    ElMessage.error('溯源查询失败');
  } finally {
    traceLoading.value = false;
  }
};

// 刷新监控数据
const refreshMonitor = () => {
  loadMonitorData();
  crudExpose.doRefresh();
  ElMessage.success('监控数据已刷新');
};

// 导出数据
const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  const headers = ['用户名', '用户域名', '业务类型', '上线时间', 'BRAS IP', '用户IP', '用户IPv6', 'MAC地址', '区域'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.user_name || '',
      row.user_domain || '',
      row.user_business_type || '',
      row.online_time || '',
      row.bras_ip || '',
      row.user_framedip || '',
      row.user_framedipv6 || '',
      row.user_mac || '',
      row.user_area || ''
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `在线记录_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};

onMounted(() => {
  loadMonitorData();
});
</script>

<style scoped lang="scss">
.online-record-container {
  padding: 20px;
  
  .monitor-row {
    .monitor-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
      
      .monitor-content {
        flex: 1;
        
        .monitor-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .monitor-label {
          font-size: 14px;
          color: #606266;
          margin-top: 4px;
        }
      }
      
      .monitor-icon {
        font-size: 32px;
        margin-left: 15px;
      }
    }
  }
  
  .monitor-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
    }
  }
  
  .no-data {
    text-align: center;
    padding: 40px;
  }
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
    
    .el-table {
      .el-table__header {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>
