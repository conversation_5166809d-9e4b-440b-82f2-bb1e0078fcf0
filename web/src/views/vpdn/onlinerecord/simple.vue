<template>
  <div class="onlinerecord-container">
    <h1>在线查询</h1>
    <p>这里是实时在线用户查询和监控页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
      <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
      <el-table-column prop="user_business_type" label="业务类型" width="100"></el-table-column>
      <el-table-column prop="online_time" label="上线时间" width="180"></el-table-column>
      <el-table-column prop="bras_ip" label="BRAS IP" width="130"></el-table-column>
      <el-table-column prop="user_framedip" label="用户IP" width="130"></el-table-column>
      <el-table-column prop="user_mac" label="MAC地址" width="150"></el-table-column>
      <el-table-column prop="user_area" label="区域" width="100"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnOnlineRecordSimple'
});

const mockData = reactive([
  {
    user_name: 'testuser001',
    user_domain: 'test.vpdn.com',
    user_business_type: 'ADSL',
    online_time: '2024-01-01 10:00:00',
    bras_ip: '*************',
    user_framedip: '**********',
    user_mac: '00:11:22:33:44:55',
    user_area: '001'
  },
  {
    user_name: 'testuser002',
    user_domain: 'demo.vpdn.com',
    user_business_type: 'LAN',
    online_time: '2024-01-01 11:00:00',
    bras_ip: '*************',
    user_framedip: '**********',
    user_mac: '00:11:22:33:44:66',
    user_area: '002'
  }
]);
</script>

<style scoped lang="scss">
.onlinerecord-container {
  padding: 20px;
}
</style>
