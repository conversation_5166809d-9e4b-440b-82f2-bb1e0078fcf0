<template>
  <div class="authrecord-container">
    <h1>认证记录</h1>
    <p>这里是认证记录查询页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
      <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
      <el-table-column prop="auth_time" label="认证时间" width="180"></el-table-column>
      <el-table-column prop="auth_result" label="认证结果" width="100">
        <template #default="{ row }">
          <el-tag :type="row.auth_result === 0 ? 'success' : 'danger'">
            {{ row.auth_result === 0 ? '成功' : '失败' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="bras_ip" label="BRAS IP" width="130"></el-table-column>
      <el-table-column prop="user_ip" label="用户IP" width="130"></el-table-column>
      <el-table-column prop="auth_reason" label="认证原因"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnAuthRecordSimple'
});

const mockData = reactive([
  {
    user_name: 'testuser001',
    user_domain: 'test.vpdn.com',
    auth_time: '2024-01-01 10:00:00',
    auth_result: 0,
    bras_ip: '*************',
    user_ip: '**********',
    auth_reason: '认证成功'
  },
  {
    user_name: 'testuser002',
    user_domain: 'demo.vpdn.com',
    auth_time: '2024-01-01 10:05:00',
    auth_result: 1,
    bras_ip: '*************',
    user_ip: '**********',
    auth_reason: '密码错误'
  }
]);
</script>

<style scoped lang="scss">
.authrecord-container {
  padding: 20px;
}
</style>
