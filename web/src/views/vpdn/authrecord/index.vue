<template>
  <div class="auth-record-container">
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <el-button type="primary" @click="showStatistics">
          <el-icon><DataAnalysis /></el-icon>
          认证统计
        </el-button>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </fs-crud>

    <!-- 认证统计对话框 -->
    <el-dialog v-model="statisticsVisible" title="认证统计" width="60%">
      <div v-loading="statisticsLoading">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="stat-card">
              <h4>认证成功率</h4>
              <div class="stat-number success">{{ successRate }}%</div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="stat-card">
              <h4>总认证次数</h4>
              <div class="stat-number">{{ totalAuth }}</div>
            </div>
          </el-col>
        </el-row>
        <div class="chart-container mt20">
          <div ref="statisticsChartRef" style="height: 300px;"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage } from 'element-plus';
import { DataAnalysis, Download } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import * as api from './api';

defineOptions({
  name: 'VpdnAuthRecord'
});

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

const statisticsVisible = ref(false);
const statisticsLoading = ref(false);
const statisticsChartRef = ref();
const successRate = ref(0);
const totalAuth = ref(0);

let statisticsChart: any = null;

const showStatistics = async () => {
  statisticsVisible.value = true;
  statisticsLoading.value = true;
  
  try {
    const res = await api.getAuthStatistics();
    if (res.code === 2000) {
      const data = res.data;
      successRate.value = data.success_rate || 0;
      totalAuth.value = data.total_auth || 0;
      
      // 初始化图表
      initStatisticsChart(data);
    }
  } catch (error) {
    ElMessage.error('获取统计数据失败');
  } finally {
    statisticsLoading.value = false;
  }
};

const initStatisticsChart = (data: any) => {
  if (statisticsChart) statisticsChart.dispose();
  statisticsChart = echarts.init(statisticsChartRef.value);
  
  const option = {
    title: {
      text: '认证结果分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '认证结果',
        type: 'pie',
        radius: '50%',
        data: [
          { value: data.success_count || 0, name: '认证成功', itemStyle: { color: '#67C23A' } },
          { value: data.failed_count || 0, name: '认证失败', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  statisticsChart.setOption(option);
};

const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  const headers = ['用户名', '用户域名', '认证时间', '认证结果', 'BRAS IP', '用户IP', '认证原因', '区域'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.user_name || '',
      row.user_domain || '',
      row.auth_time || '',
      row.auth_result === 0 ? '成功' : '失败',
      row.bras_ip || '',
      row.user_ip || '',
      (row.auth_reason || '').replace(/,/g, '，'),
      row.user_area || ''
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `认证记录_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};
</script>

<style scoped lang="scss">
.auth-record-container {
  padding: 20px;
  
  .stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    
    h4 {
      margin: 0 0 15px 0;
      color: #606266;
    }
    
    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #303133;
      
      &.success {
        color: #67C23A;
      }
    }
  }
  
  .chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
  }
}
</style>
