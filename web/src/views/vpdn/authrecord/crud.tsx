import * as api from './api';
import {
    dict,
    UserPageQuery,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';

export function createCrudOptions(): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getAuthRecordList(query);
    };

    // 认证结果字典
    const authResultDict = dict({
        data: [
            { value: 0, label: '成功', color: 'success' },
            { value: 1, label: '失败', color: 'danger' }
        ]
    });

    return {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                buttons: {
                    add: { show: false },
                    statistics: {
                        text: '认证统计',
                        type: 'primary',
                        click: () => {
                            // 触发统计对话框
                        }
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 }
            },
            rowHandle: {
                width: 120,
                buttons: {
                    view: {
                        text: '查看',
                        type: 'success'
                    },
                    edit: { show: false },
                    remove: { show: false }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                user_name: {
                    title: '用户名',
                    search: { show: true },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                user_domain: {
                    title: '用户域名',
                    search: { show: true },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                auth_time: {
                    title: '认证时间',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'datetimerange',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                                format: 'YYYY-MM-DD HH:mm:ss',
                                startPlaceholder: '开始时间',
                                endPlaceholder: '结束时间'
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                auth_result: {
                    title: '认证结果',
                    type: 'dict-select',
                    dict: authResultDict,
                    search: { show: true },
                    column: {
                        width: 100,
                        align: 'center',
                        component: {
                            name: 'fs-dict-tag'
                        }
                    }
                },
                bras_ip: {
                    title: 'BRAS IP',
                    search: { show: true },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_ip: {
                    title: '用户IP',
                    search: { show: true },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                auth_reason: {
                    title: '认证原因',
                    column: {
                        width: 200,
                        align: 'left',
                        showOverflowTooltip: true
                    }
                },
                user_area: {
                    title: '区域',
                    search: { show: true },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                }
            }
        }
    };
}
