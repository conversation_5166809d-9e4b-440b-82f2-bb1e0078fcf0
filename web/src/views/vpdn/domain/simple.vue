<template>
  <div class="domain-container">
    <h1>域名管理</h1>
    <p>这里是VPDN域名管理页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="domain_name" label="域名" width="180"></el-table-column>
      <el-table-column prop="domain_contact" label="联系人" width="120"></el-table-column>
      <el-table-column prop="domain_phone" label="联系电话" width="150"></el-table-column>
      <el-table-column prop="domain_email" label="联系邮箱" width="200"></el-table-column>
      <el-table-column prop="domain_area" label="区域编号" width="100"></el-table-column>
      <el-table-column prop="domain_description" label="描述"></el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnDomainSimple'
});

const mockData = reactive([
  {
    domain_name: 'test.vpdn.com',
    domain_contact: '张三',
    domain_phone: '13800138000',
    domain_email: 'zhang<PERSON>@test.com',
    domain_area: '001',
    domain_description: '测试域名'
  },
  {
    domain_name: 'demo.vpdn.com',
    domain_contact: '李四',
    domain_phone: '13900139000',
    domain_email: '<EMAIL>',
    domain_area: '002',
    domain_description: '演示域名'
  }
]);
</script>

<style scoped lang="scss">
.domain-container {
  padding: 20px;
}
</style>
