<template>
  <div class="domain-container">
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <el-button type="success" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </fs-crud>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';

// 页面标题
defineOptions({
  name: 'VpdnDomain'
});

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

// 导出数据
const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  // 简单的CSV导出
  const headers = ['域名', '联系人', '联系电话', '联系邮箱', '区域编号', '描述'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.vpdn_domain || '',
      row.contect_man_name || '',
      row.contect_man_phone || '',
      row.contect_man_email || '',
      row.vpdn_areano || '',
      (row.description || '').replace(/,/g, '，') // 替换逗号避免CSV格式问题
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `域名管理_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};

onMounted(() => {
  // 页面加载完成后的初始化操作
});
</script>

<style scoped lang="scss">
.domain-container {
  padding: 20px;
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
    
    .el-table {
      .el-table__header {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>
