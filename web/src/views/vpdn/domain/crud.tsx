import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';

export function createCrudOptions(): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        console.log('域名管理 - 发起分页请求:', query);
        try {
            // 转换分页参数以适配DRF
            const params = {
                page: query.page || 1,
                limit: query.pageSize || 20,
                search: query.search || '',
                ...query
            };

            const result = await api.getDomainList(params);
            console.log('域名管理 - 请求结果:', result);

            // 转换DRF分页格式为Fast-CRUD期望的格式
            if (result && result.code === 2000) {
                return {
                    records: result.data || [],
                    total: result.total || 0,
                    current: result.page || 1,
                    size: result.limit || 20
                };
            }

            return {
                records: [],
                total: 0,
                current: 1,
                size: 20
            };
        } catch (error) {
            console.error('域名管理 - 请求失败:', error);
            throw error;
        }
    };

    const editRequest = async ({ form, row }: EditReq) => {
        if (form.id) {
            return await api.updateDomain(form.id, form);
        }
    };

    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteDomain(row.id);
    };

    const addRequest = async ({ form }: AddReq) => {
        return await api.createDomain(form);
    };

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            pagination: {
                show: true,
                pageSize: 20
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('vpdn:domain:create'),
                        text: '新增域名',
                        type: 'primary'
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 },
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            rowHandle: {
                width: 200,
                buttons: {
                    view: {
                        show: auth('vpdn:domain:view'),
                        text: '查看',
                        type: 'success'
                    },
                    edit: {
                        show: auth('vpdn:domain:update'),
                        text: '编辑',
                        type: 'primary'
                    },
                    remove: {
                        show: auth('vpdn:domain:delete'),
                        text: '删除',
                        type: 'danger'
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                vpdn_domain: {
                    title: '域名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '域名不能为空' },
                            { pattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的域名格式' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名，如：example.com'
                            }
                        }
                    },
                    column: {
                        width: 200,
                        align: 'center'
                    }
                },
                contect_man_name: {
                    title: '联系人',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系人',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '联系人不能为空' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系人姓名'
                            }
                        }
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                contect_man_phone: {
                    title: '联系电话',
                    form: {
                        rules: [
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系电话'
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                contect_man_email: {
                    title: '联系邮箱',
                    form: {
                        rules: [
                            { type: 'email', message: '请输入有效的邮箱地址' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系邮箱'
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                vpdn_areano: {
                    title: '区域编号',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域编号',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域编号'
                            }
                        }
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                description: {
                    title: '描述',
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                type: 'textarea',
                                rows: 3,
                                placeholder: '请输入域名描述'
                            }
                        }
                    },
                    column: {
                        width: 200,
                        align: 'left',
                        showOverflowTooltip: true
                    }
                }
            }
        }
    };
}
