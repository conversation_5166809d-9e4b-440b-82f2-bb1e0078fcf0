import * as api from './api';
import {
    dict,
    UserPageQuery,
    AddReq,
    DelReq,
    EditReq,
    CreateCrudOptionsProps,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';
import { auth } from '/@/utils/authFunction';

export function createCrudOptions(): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getDomainList(query);
    };

    const editRequest = async ({ form, row }: EditReq) => {
        if (form.id) {
            return await api.updateDomain(form.id, form);
        }
    };

    const delRequest = async ({ row }: DelReq) => {
        return await api.deleteDomain(row.id);
    };

    const addRequest = async ({ form }: AddReq) => {
        return await api.createDomain(form);
    };

    return {
        crudOptions: {
            request: {
                pageRequest,
                addRequest,
                editRequest,
                delRequest,
            },
            actionbar: {
                buttons: {
                    add: {
                        show: auth('vpdn:domain:create'),
                        text: '新增域名',
                        type: 'primary'
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 },
                buttons: {
                    search: {
                        text: '查询',
                        icon: 'search'
                    },
                    reset: {
                        text: '重置',
                        icon: 'refresh'
                    }
                }
            },
            rowHandle: {
                width: 200,
                buttons: {
                    view: {
                        show: auth('vpdn:domain:view'),
                        text: '查看',
                        type: 'success'
                    },
                    edit: {
                        show: auth('vpdn:domain:update'),
                        text: '编辑',
                        type: 'primary'
                    },
                    remove: {
                        show: auth('vpdn:domain:delete'),
                        text: '删除',
                        type: 'danger'
                    }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                domain_name: {
                    title: '域名',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '域名不能为空' },
                            { pattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的域名格式' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入域名，如：example.com'
                            }
                        }
                    },
                    column: {
                        width: 200,
                        align: 'center'
                    }
                },
                domain_contact: {
                    title: '联系人',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系人',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        rules: [
                            { required: true, message: '联系人不能为空' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系人姓名'
                            }
                        }
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                domain_phone: {
                    title: '联系电话',
                    form: {
                        rules: [
                            { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系电话'
                            }
                        }
                    },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                domain_email: {
                    title: '联系邮箱',
                    form: {
                        rules: [
                            { type: 'email', message: '请输入有效的邮箱地址' }
                        ],
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入联系邮箱'
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                domain_area: {
                    title: '区域编号',
                    search: {
                        show: true,
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域编号',
                                clearable: true
                            }
                        }
                    },
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                placeholder: '请输入区域编号'
                            }
                        }
                    },
                    column: {
                        width: 120,
                        align: 'center'
                    }
                },
                domain_description: {
                    title: '描述',
                    form: {
                        component: {
                            name: 'el-input',
                            props: {
                                type: 'textarea',
                                rows: 3,
                                placeholder: '请输入域名描述'
                            }
                        }
                    },
                    column: {
                        width: 200,
                        align: 'left',
                        showOverflowTooltip: true
                    }
                },
                create_datetime: {
                    title: '创建时间',
                    form: { show: false },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                update_datetime: {
                    title: '更新时间',
                    form: { show: false },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                }
            }
        }
    };
}
