<template>
  <div class="vpdn-dashboard">
    <el-row :gutter="15" class="dashboard-cards mb15">
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="(item, index) in statsCards" :key="index">
        <div class="stats-card">
          <div class="stats-content">
            <div class="stats-number">
              <span class="main-number">{{ item.value }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
            <div class="stats-title">{{ item.title }}</div>
          </div>
          <div class="stats-icon" :style="{ background: item.bgColor }">
            <i :class="item.icon" :style="{ color: item.iconColor }"></i>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="15" class="dashboard-charts">
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <div class="chart-card">
          <div class="chart-title">用户流量趋势</div>
          <div ref="trafficChartRef" class="chart-container"></div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <div class="chart-card">
          <div class="chart-title">用户状态分布</div>
          <div ref="statusChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="15" class="mt15">
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="chart-card">
          <div class="chart-title">域名用户分布 TOP10</div>
          <div ref="domainChartRef" class="chart-container"></div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
        <div class="chart-card">
          <div class="chart-title">在线用户监控</div>
          <div class="monitor-content">
            <div class="monitor-item" v-for="item in onlineStats" :key="item.label">
              <div class="monitor-label">{{ item.label }}</div>
              <div class="monitor-value" :style="{ color: item.color }">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import { getUserStatistics, getRealTimeMonitor, getTrafficStatistics } from '/@/api/vpdn/index';

const trafficChartRef = ref();
const statusChartRef = ref();
const domainChartRef = ref();

const state = reactive({
  statsCards: [
    {
      title: '总用户数',
      value: '0',
      unit: '个',
      icon: 'el-icon-user',
      bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      iconColor: '#fff'
    },
    {
      title: '在线用户',
      value: '0',
      unit: '个',
      icon: 'el-icon-connection',
      bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      iconColor: '#fff'
    },
    {
      title: '活跃用户',
      value: '0',
      unit: '个',
      icon: 'el-icon-check',
      bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      iconColor: '#fff'
    },
    {
      title: '暂停用户',
      value: '0',
      unit: '个',
      icon: 'el-icon-warning',
      bgColor: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      iconColor: '#fff'
    }
  ],
  onlineStats: [
    { label: '总在线数', value: '0', color: '#409EFF' },
    { label: 'IPv4用户', value: '0', color: '#67C23A' },
    { label: 'IPv6用户', value: '0', color: '#E6A23C' },
    { label: '最近上线', value: '0', color: '#F56C6C' }
  ]
});

let trafficChart: any = null;
let statusChart: any = null;
let domainChart: any = null;

// 初始化流量趋势图
const initTrafficChart = (data: any) => {
  if (trafficChart) trafficChart.dispose();
  trafficChart = echarts.init(trafficChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['上行流量', '下行流量']
    },
    xAxis: {
      type: 'category',
      data: data.dates || []
    },
    yAxis: {
      type: 'value',
      name: '流量(GB)',
      axisLabel: {
        formatter: '{value} GB'
      }
    },
    series: [
      {
        name: '上行流量',
        type: 'line',
        data: data.upload || [],
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '下行流量',
        type: 'line',
        data: data.download || [],
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  };
  
  trafficChart.setOption(option);
};

// 初始化用户状态饼图
const initStatusChart = (data: any) => {
  if (statusChart) statusChart.dispose();
  statusChart = echarts.init(statusChartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '用户状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: data.active_users || 0, name: '活跃用户', itemStyle: { color: '#67C23A' } },
          { value: data.suspended_users || 0, name: '暂停用户', itemStyle: { color: '#E6A23C' } },
          { value: data.cancelled_users || 0, name: '注销用户', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  statusChart.setOption(option);
};

// 初始化域名分布柱状图
const initDomainChart = (data: any) => {
  if (domainChart) domainChart.dispose();
  domainChart = echarts.init(domainChartRef.value);
  
  const domains = data.map((item: any) => item.user_domain);
  const counts = data.map((item: any) => item.count);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: domains,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '用户数'
    },
    series: [
      {
        name: '用户数',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  };
  
  domainChart.setOption(option);
};

// 加载数据
const loadData = async () => {
  try {
    // 加载用户统计
    const userStatsRes = await getUserStatistics();
    if (userStatsRes.code === 2000) {
      const stats = userStatsRes.data.user_stats;
      state.statsCards[0].value = stats.total_users.toString();
      state.statsCards[2].value = stats.active_users.toString();
      state.statsCards[3].value = stats.suspended_users.toString();
      
      // 初始化状态饼图
      initStatusChart(stats);
      
      // 初始化域名分布图
      if (userStatsRes.data.domain_stats) {
        initDomainChart(userStatsRes.data.domain_stats);
      }
    }
    
    // 加载在线监控数据
    const onlineRes = await getRealTimeMonitor();
    if (onlineRes.code === 2000) {
      const onlineData = onlineRes.data.online_stats;
      state.statsCards[1].value = onlineData.total_online.toString();
      state.onlineStats[0].value = onlineData.total_online.toString();
      state.onlineStats[1].value = onlineData.total_ipv4_users.toString();
      state.onlineStats[2].value = onlineData.total_ipv6_users.toString();
    }
    
    // 加载流量统计
    const trafficRes = await getTrafficStatistics();
    if (trafficRes.code === 2000) {
      // 模拟流量趋势数据
      const mockData = {
        dates: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07'],
        upload: [120, 132, 101, 134, 90, 230, 210],
        download: [220, 182, 191, 234, 290, 330, 310]
      };
      initTrafficChart(mockData);
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error);
  }
};

onMounted(() => {
  nextTick(() => {
    loadData();
  });
});
</script>

<style scoped lang="scss">
.vpdn-dashboard {
  padding: 20px;
  
  .stats-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .stats-content {
      flex: 1;
      
      .stats-number {
        .main-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
        }
        
        .unit {
          font-size: 14px;
          color: #909399;
          margin-left: 4px;
        }
      }
      
      .stats-title {
        font-size: 14px;
        color: #606266;
        margin-top: 8px;
      }
    }
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        font-size: 24px;
      }
    }
  }
  
  .chart-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    height: 400px;
    
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .chart-container {
      height: calc(100% - 50px);
    }
    
    .monitor-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      height: calc(100% - 50px);
      
      .monitor-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        
        .monitor-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }
        
        .monitor-value {
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
