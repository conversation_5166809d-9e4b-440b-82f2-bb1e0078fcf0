<template>
  <div class="vpdn-dashboard">
    <h1>VPDN管理仪表板</h1>
    <p>欢迎使用VPDN管理系统！</p>

    <el-row :gutter="15" class="dashboard-cards mb15">
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="(item, index) in statsCards" :key="index">
        <div class="stats-card">
          <div class="stats-content">
            <div class="stats-number">
              <span class="main-number">{{ item.value }}</span>
              <span class="unit">{{ item.unit }}</span>
            </div>
            <div class="stats-title">{{ item.title }}</div>
          </div>
          <div class="stats-icon" :style="{ background: item.bgColor }">
            <i :class="item.icon" :style="{ color: item.iconColor }"></i>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';

const statsCards = reactive([
  {
    title: '总用户数',
    value: '1,234',
    unit: '个',
    icon: 'el-icon-user',
    bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    iconColor: '#fff'
  },
  {
    title: '在线用户',
    value: '856',
    unit: '个',
    icon: 'el-icon-connection',
    bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    iconColor: '#fff'
  },
  {
    title: '活跃用户',
    value: '742',
    unit: '个',
    icon: 'el-icon-check',
    bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    iconColor: '#fff'
  },
  {
    title: '暂停用户',
    value: '23',
    unit: '个',
    icon: 'el-icon-warning',
    bgColor: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    iconColor: '#fff'
  }
]);

onMounted(() => {
  console.log('VPDN仪表板组件已加载');
});
</script>

<style scoped lang="scss">
.vpdn-dashboard {
  padding: 20px;
  
  .stats-card {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .stats-content {
      flex: 1;
      
      .stats-number {
        .main-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
        }
        
        .unit {
          font-size: 14px;
          color: #909399;
          margin-left: 4px;
        }
      }
      
      .stats-title {
        font-size: 14px;
        color: #606266;
        margin-top: 8px;
      }
    }
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      i {
        font-size: 24px;
      }
    }
  }
  
  .chart-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    height: 400px;
    
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .chart-container {
      height: calc(100% - 50px);
    }
    
    .monitor-content {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      height: calc(100% - 50px);
      
      .monitor-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        
        .monitor-label {
          font-size: 14px;
          color: #606266;
          margin-bottom: 8px;
        }
        
        .monitor-value {
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
