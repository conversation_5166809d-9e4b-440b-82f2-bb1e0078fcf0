<template>
  <div class="detail-container">
    <h1>清单查询</h1>
    <p>这里是用户会话记录和账单查询页面</p>
    
    <el-table :data="mockData" border style="width: 100%">
      <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
      <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
      <el-table-column prop="online_time" label="上线时间" width="180"></el-table-column>
      <el-table-column prop="offline_time" label="下线时间" width="180"></el-table-column>
      <el-table-column prop="session_time" label="会话时长" width="120">
        <template #default="{ row }">
          {{ formatDuration(row.session_time) }}
        </template>
      </el-table-column>
      <el-table-column prop="input_octets" label="上行流量(MB)" width="130">
        <template #default="{ row }">
          {{ formatTraffic(row.input_octets) }}
        </template>
      </el-table-column>
      <el-table-column prop="output_octets" label="下行流量(MB)" width="130">
        <template #default="{ row }">
          {{ formatTraffic(row.output_octets) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from 'vue';

defineOptions({
  name: 'VpdnDetailSimple'
});

const mockData = reactive([
  {
    user_name: 'testuser001',
    user_domain: 'test.vpdn.com',
    online_time: '2024-01-01 10:00:00',
    offline_time: '2024-01-01 12:00:00',
    session_time: 7200,
    input_octets: 104857600,
    output_octets: 524288000
  },
  {
    user_name: 'testuser002',
    user_domain: 'demo.vpdn.com',
    online_time: '2024-01-01 14:00:00',
    offline_time: '2024-01-01 15:30:00',
    session_time: 5400,
    input_octets: 52428800,
    output_octets: 262144000
  }
]);

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${hours}h${minutes}m`;
};

const formatTraffic = (bytes: number) => {
  return (bytes / 1024 / 1024).toFixed(2);
};
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;
}
</style>
