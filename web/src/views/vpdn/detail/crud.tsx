import * as api from './api';
import {
    dict,
    UserPageQuery,
    CreateCrudOptionsRet,
} from '@fast-crud/fast-crud';

export function createCrudOptions(): CreateCrudOptionsRet {
    const pageRequest = async (query: UserPageQuery) => {
        return await api.getDetailList(query);
    };

    return {
        crudOptions: {
            request: {
                pageRequest
            },
            actionbar: {
                buttons: {
                    add: { show: false },
                    billQuery: {
                        text: '账单查询',
                        type: 'primary'
                    },
                    trafficStats: {
                        text: '流量统计',
                        type: 'success'
                    }
                }
            },
            search: {
                show: true,
                col: { span: 6 }
            },
            rowHandle: {
                width: 120,
                buttons: {
                    view: {
                        text: '查看',
                        type: 'success'
                    },
                    edit: { show: false },
                    remove: { show: false }
                }
            },
            columns: {
                _index: {
                    title: '序号',
                    form: { show: false },
                    column: {
                        type: 'index',
                        width: 60,
                        align: 'center'
                    }
                },
                user_name: {
                    title: '用户名',
                    search: { show: true },
                    column: {
                        width: 150,
                        align: 'center'
                    }
                },
                user_domain: {
                    title: '用户域名',
                    search: { show: true },
                    column: {
                        width: 180,
                        align: 'center'
                    }
                },
                online_time: {
                    title: '上线时间',
                    search: {
                        show: true,
                        component: {
                            name: 'el-date-picker',
                            props: {
                                type: 'datetimerange',
                                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                                format: 'YYYY-MM-DD HH:mm:ss',
                                startPlaceholder: '开始时间',
                                endPlaceholder: '结束时间'
                            }
                        }
                    },
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                offline_time: {
                    title: '下线时间',
                    column: {
                        width: 180,
                        align: 'center',
                        sortable: true
                    }
                },
                session_time: {
                    title: '会话时长(秒)',
                    column: {
                        width: 120,
                        align: 'center',
                        formatter: ({ row }: any) => {
                            if (!row.session_time) return '-';
                            const hours = Math.floor(row.session_time / 3600);
                            const minutes = Math.floor((row.session_time % 3600) / 60);
                            return `${hours}h${minutes}m`;
                        }
                    }
                },
                input_octets: {
                    title: '上行流量(MB)',
                    column: {
                        width: 130,
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.input_octets ? (row.input_octets / 1024 / 1024).toFixed(2) : '0';
                        }
                    }
                },
                output_octets: {
                    title: '下行流量(MB)',
                    column: {
                        width: 130,
                        align: 'center',
                        formatter: ({ row }: any) => {
                            return row.output_octets ? (row.output_octets / 1024 / 1024).toFixed(2) : '0';
                        }
                    }
                },
                nas_ip: {
                    title: 'NAS IP',
                    search: { show: true },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                framed_ip: {
                    title: '用户IP',
                    search: { show: true },
                    column: {
                        width: 130,
                        align: 'center'
                    }
                },
                user_area: {
                    title: '区域',
                    search: { show: true },
                    column: {
                        width: 100,
                        align: 'center'
                    }
                }
            }
        }
    };
}
