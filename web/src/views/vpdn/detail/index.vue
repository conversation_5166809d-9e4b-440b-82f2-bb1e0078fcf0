<template>
  <div class="detail-container">
    <fs-crud ref="crudRef" v-bind="crudBinding">
      <template #actionbar-right>
        <el-button type="primary" @click="showBillQuery">
          <el-icon><Tickets /></el-icon>
          账单查询
        </el-button>
        <el-button type="success" @click="showTrafficStats">
          <el-icon><TrendCharts /></el-icon>
          流量统计
        </el-button>
        <el-button type="info" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>
    </fs-crud>

    <!-- 账单查询对话框 -->
    <el-dialog v-model="billQueryVisible" title="账单查询" width="50%">
      <el-form :model="billForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="billForm.user_name" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="查询月份">
          <el-date-picker
            v-model="billForm.query_month"
            type="month"
            placeholder="选择月份"
            value-format="YYYY-MM"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="billQueryVisible = false">取消</el-button>
          <el-button type="primary" @click="executeBillQuery" :loading="billLoading">查询</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 账单结果对话框 -->
    <el-dialog v-model="billResultVisible" title="账单查询结果" width="70%">
      <div v-if="billResults.length === 0" class="no-data">
        <el-empty description="未找到账单记录"></el-empty>
      </div>
      <div v-else>
        <el-table :data="billResults" border stripe>
          <el-table-column prop="user_name" label="用户名" width="150"></el-table-column>
          <el-table-column prop="user_domain" label="用户域名" width="180"></el-table-column>
          <el-table-column prop="total_sessions" label="总会话数" width="100"></el-table-column>
          <el-table-column prop="total_duration" label="总时长(小时)" width="120">
            <template #default="{ row }">
              {{ (row.total_duration / 3600).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_input_mb" label="上行流量(MB)" width="130"></el-table-column>
          <el-table-column prop="total_output_mb" label="下行流量(MB)" width="130"></el-table-column>
          <el-table-column prop="total_traffic_mb" label="总流量(MB)" width="120"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 流量统计对话框 -->
    <el-dialog v-model="trafficStatsVisible" title="流量统计" width="80%">
      <div v-loading="trafficLoading">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="chart-panel">
              <h4>每日流量趋势</h4>
              <div ref="dailyChartRef" style="height: 300px;"></div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="chart-panel">
              <h4>域名流量分布</h4>
              <div ref="domainChartRef" style="height: 300px;"></div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { createCrudOptions } from './crud';
import { useFs } from '@fast-crud/fast-crud';
import { ElMessage } from 'element-plus';
import { Tickets, TrendCharts, Download } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import * as api from './api';

defineOptions({
  name: 'VpdnDetail'
});

const { crudRef, crudBinding, crudExpose } = useFs({ createCrudOptions });

// 账单查询相关
const billQueryVisible = ref(false);
const billResultVisible = ref(false);
const billLoading = ref(false);
const billResults = ref([]);

const billForm = reactive({
  user_name: '',
  query_month: ''
});

// 流量统计相关
const trafficStatsVisible = ref(false);
const trafficLoading = ref(false);
const dailyChartRef = ref();
const domainChartRef = ref();

let dailyChart: any = null;
let domainChart: any = null;

const showBillQuery = () => {
  billQueryVisible.value = true;
};

const executeBillQuery = async () => {
  if (!billForm.query_month) {
    ElMessage.warning('请选择查询月份');
    return;
  }
  
  billLoading.value = true;
  try {
    const res = await api.billQuery(billForm);
    if (res.code === 2000) {
      billResults.value = res.data || [];
      billQueryVisible.value = false;
      billResultVisible.value = true;
      
      if (billResults.value.length === 0) {
        ElMessage.info('未找到账单记录');
      } else {
        ElMessage.success(`找到 ${billResults.value.length} 条账单记录`);
      }
    }
  } catch (error) {
    ElMessage.error('账单查询失败');
  } finally {
    billLoading.value = false;
  }
};

const showTrafficStats = async () => {
  trafficStatsVisible.value = true;
  trafficLoading.value = true;
  
  try {
    const res = await api.getTrafficStatistics();
    if (res.code === 2000) {
      const data = res.data;
      initDailyChart(data.daily_traffic || []);
      initDomainChart(data.domain_traffic || []);
    }
  } catch (error) {
    ElMessage.error('获取流量统计失败');
  } finally {
    trafficLoading.value = false;
  }
};

const initDailyChart = (data: any[]) => {
  if (dailyChart) dailyChart.dispose();
  dailyChart = echarts.init(dailyChartRef.value);
  
  const dates = data.map(item => item.date);
  const inputData = data.map(item => (item.total_input_octets / 1024 / 1024 / 1024).toFixed(2));
  const outputData = data.map(item => (item.total_output_octets / 1024 / 1024 / 1024).toFixed(2));
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['上行流量', '下行流量']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value',
      name: '流量(GB)'
    },
    series: [
      {
        name: '上行流量',
        type: 'line',
        data: inputData,
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '下行流量',
        type: 'line',
        data: outputData,
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  };
  
  dailyChart.setOption(option);
};

const initDomainChart = (data: any[]) => {
  if (domainChart) domainChart.dispose();
  domainChart = echarts.init(domainChartRef.value);
  
  const domains = data.map(item => item.user_domain);
  const traffic = data.map(item => ((item.total_input_octets + item.total_output_octets) / 1024 / 1024 / 1024).toFixed(2));
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: domains,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '流量(GB)'
    },
    series: [
      {
        name: '总流量',
        type: 'bar',
        data: traffic,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  };
  
  domainChart.setOption(option);
};

const exportData = () => {
  const data = crudExpose.getTableData();
  if (!data || data.length === 0) {
    ElMessage.warning('暂无数据可导出');
    return;
  }
  
  const headers = ['用户名', '用户域名', '上线时间', '下线时间', '会话时长', '上行流量(MB)', '下行流量(MB)', 'NAS IP', '用户IP', '区域'];
  const csvContent = [
    headers.join(','),
    ...data.map((row: any) => [
      row.user_name || '',
      row.user_domain || '',
      row.online_time || '',
      row.offline_time || '',
      row.session_time || '',
      row.input_octets ? (row.input_octets / 1024 / 1024).toFixed(2) : '0',
      row.output_octets ? (row.output_octets / 1024 / 1024).toFixed(2) : '0',
      row.nas_ip || '',
      row.framed_ip || '',
      row.user_area || ''
    ].join(','))
  ].join('\n');
  
  const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `清单记录_${new Date().toISOString().slice(0, 10)}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  ElMessage.success('数据导出成功');
};
</script>

<style scoped lang="scss">
.detail-container {
  padding: 20px;
  
  .chart-panel {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      text-align: center;
    }
  }
  
  .no-data {
    text-align: center;
    padding: 40px;
  }
  
  :deep(.fs-crud) {
    .el-card {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
    }
  }
}
</style>
