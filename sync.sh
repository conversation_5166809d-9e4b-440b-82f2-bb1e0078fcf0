#!/bin/bash

# 定义源目录和目标主机
SOURCE_CLIENT="/home/<USER>/tss/client/"
SOURCE_WEB="/home/<USER>/tss/web/"
SOURCE_SCHEDULER="/home/<USER>/tss/scheduler/"

TARGET_HOST1="ls@**************"
TARGET_HOST2="ipv6tss@[240e:34c:564:3bf0:2ab1:33ff:fe05:606f]"
TARGET_HOST3="ipv6tss@*************"

# 同步函数
sync_changes() {
    local source=$1
    local target_host=$2
    local target_path=$3
    
    inotifywait -mrq --format '%w%f' -e modify,create,delete,attrib,move "$source" | while read file; do
        # 获取相对路径
        relative_path=${file#$source}
        # 获取文件所在目录
        dir_path=$(dirname "$file")
        
        # 检查是否为 config.py
        if [[ "$file" == *"config.py" ]]; then
            echo "跳过同步 config.py"
            continue
        fi
        
        if [ -e "$file" ]; then
            # 文件存在，同步单个文件
            rsync -az --exclude="config.py" "$file" "$target_host:$target_path$relative_path"
            echo "已同步: $relative_path 到 $target_host"
        else
            # 文件被删除，同步整个目录以确保删除操作同步
            rsync -az --delete --exclude="config.py" "$dir_path/" "$target_host:$target_path${dir_path#$source}"
            echo "已同步删除操作: $relative_path 从 $target_host"
        fi
    done
}

# 启动客户端同步
sync_changes "$SOURCE_CLIENT" "$TARGET_HOST1" "/home/<USER>/tss/client/" &
sync_changes "$SOURCE_CLIENT" "$TARGET_HOST2" "/home/<USER>/tss/client/" &

# 启动web同步
sync_changes "$SOURCE_WEB" "$TARGET_HOST3" "/home/<USER>/tss/web/" &

# 启动scheduler同步
sync_changes "$SOURCE_SCHEDULER" "$TARGET_HOST3" "/home/<USER>/tss/scheduler/" &

# 等待所有后台进程
wait