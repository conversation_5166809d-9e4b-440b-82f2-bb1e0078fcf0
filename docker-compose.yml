version: "3"
services:
  ynyb-web:
    container_name: ynyb-web
    ports:
      - "8080:8080"
    build:
      context: ./
      dockerfile: ./docker_env/web/Dockerfile
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./docker_env/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker_env/nginx/my.conf:/etc/nginx/conf.d/default.conf
      - ./backend/media:/backend/media
      - ./web:/web
    expose:
      - "8080"
    restart: always
    networks:
      aaa-net:
        ipv4_address: **********

  ynyb-backend:
    build:
      context: .
      dockerfile: ./docker_env/backend/Dockerfile
    container_name: ynyb-backend
    working_dir: /backend
    # depends_on:
    #   - ynyb-mysql 
    environment:
      PYTHONUNBUFFERED: 1
      DATABASE_HOST: ynyb-mysql
      TZ: Asia/Shanghai
    volumes:
      - ./logs/log:/var/log
      - ./backend:/backend
    ports:
      - "8000:8000"
    expose:
      - "8000"
    restart: always
    networks:
      aaa-net:
        ipv4_address: **********

  # ynyb-mysql:
  #   image: linshui/smartaaadb:v0.1
  #   container_name: smartaaadb
  #   restart: always
  #   networks:
  #     aaa-net:
  #       ipv4_address: **********
    
networks:
  aaa-net:
    external: true
    name: aaa-net

