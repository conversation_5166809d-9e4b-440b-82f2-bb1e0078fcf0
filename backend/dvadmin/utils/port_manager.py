import socket
from typing import Optional
from django.conf import settings

# SSH端口范围配置
SSH_PORT_START = getattr(settings, 'SSH_PORT_START', 40000)
SSH_PORT_END = getattr(settings, 'SSH_PORT_END', 41000)

def is_port_available(port: int) -> bool:
    """检查端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.bind(('', port))
        sock.close()
        return True
    except:
        return False

def get_available_port() -> Optional[int]:
    """获取一个可用的SSH端口"""
    from dvadmin.tss.models import Device
    
    # 获取已使用的端口
    used_ports = set(Device.objects.values_list('ssh_reverse_port', flat=True))
    
    # 在端口范围内查找未使用的端口
    for port in range(SSH_PORT_START, SSH_PORT_END + 1):
        if port not in used_ports and is_port_available(port):
            return port
            
    return None 