from rest_framework.decorators import action, permission_classes
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.json_response import DetailResponse, ErrorResponse
from dvadmin.utils.port_manager import get_available_port
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import get_user_model, authenticate
import hashlib
from django.db import transaction
from dvadmin.tss.models import Device, TrafficTask , TrafficTaskDetail, TaskResult, DeviceMonitor, TrafficStatistics
from dvadmin.tss.serializers import DeviceSerializer, DeviceRegisterSerializer, DeviceMonitorSerializer, TrafficTaskSerializer , TrafficTaskDetailSerializer, TaskResultSerializer, TrafficStatisticsSerializer
from dvadmin.system.models import Area  # 添加 Area 模型的导入
import logging  # 使用Django内置的logging
from django.db.models import Avg
from datetime import timedelta

logger = logging.getLogger(__name__)  # 使用模块名作为logger名称

class DeviceViewSet(CustomModelViewSet):
    """
    终端设备管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = Device.objects.all()
    serializer_class = DeviceSerializer
    permission_classes = [IsAuthenticated]  # 默认需要认证
    extra_filter_backends = []
    
    def get_permissions(self):
        """根据不同的action设置权限"""
        if self.action == 'api_login':
            # 登录接口不需要认证
            permission_classes = [AllowAny]
        else:
            # 其他所有接口都需要认证
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        queryset = super().get_queryset()
        # 添加搜索和过滤
        location = self.request.query_params.get('location', None)
        if location:
            queryset = queryset.filter(location__icontains=location)
            
        heartbeat_status = self.request.query_params.get('heartbeat_status', None)
        if heartbeat_status:
            queryset = queryset.filter(heartbeat_status=heartbeat_status)
            
        enabled = self.request.query_params.get('enabled', None)
        if enabled is not None:
            queryset = queryset.filter(enabled=enabled)
            
        return queryset

    @action(detail=False, methods=['get'])
    def check_registration(self, request):
        """检查设备是否已注册"""
        serial_number = request.query_params.get('serial_number')
        if not serial_number:
            return ErrorResponse(msg="序列号不能为空")

        try:
            device = Device.objects.get(serial_number=serial_number)
            serializer = self.get_serializer(device)
            return DetailResponse(data={'ssh_reverse_port': device.ssh_reverse_port}, msg="设备已注册")
        except Device.DoesNotExist:
            return DetailResponse(data=None, msg="设备未注册")

    @action(detail=False, methods=['post'])
    def register(self, request):
        """设备注册"""
        # 获取客户端IP地址
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0]
        else:
            client_ip = request.META.get('REMOTE_ADDR')

        # 将客户端IP地址添加到请求数据中
        request_data = request.data.copy()
        request_data['ipv4_public'] = client_ip
        
        logger.info(f"设备注册请求数据: {request_data}")
        serializer = DeviceRegisterSerializer(data=request_data)
        if not serializer.is_valid():
            logger.error(f"设备注册数据验证失败: {serializer.errors}")
            return ErrorResponse(msg=serializer.errors)

        # 获取可用的SSH端口
        ssh_reverse_port = get_available_port()
        if not ssh_reverse_port:
            return ErrorResponse(msg="无可用的SSH端口")

        # 设置SSH反向端口
        serializer.validated_data['ssh_reverse_port'] = ssh_reverse_port
        
        try:
            # 创建设备实例
            device = serializer.save(
                creator=request.user,
                create_datetime=timezone.now()
            )
            logger.info(f"设备注册成功: {device.serial_number}")
            return DetailResponse(data={'ssh_reverse_port': device.ssh_reverse_port}, msg="注册成功")
        except Exception as e:
            logger.error(f"设备注册失败: {str(e)}")
            return ErrorResponse(msg=f"注册失败: {str(e)}")

    @action(detail=False, methods=['post'])
    def heartbeat(self, request):
        """处理设备心跳"""
        serial_number = request.data.get('serial_number')
        if not serial_number:
            return ErrorResponse(msg="序列号不能为空")

        try:
            device = Device.objects.get(serial_number=serial_number)
            
            # 更新设备IP信息
            # 获取客户端IP地址
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                client_ip = x_forwarded_for.split(',')[0]
            else:
                client_ip = request.META.get('REMOTE_ADDR')
                
            # 检查并更新设备IP信息
            needs_update = False
            update_fields = ['heartbeat_status', 'last_heartbeat']  # 这两个字段总是需要更新
            
            # 检查私网IPv4地址
            if 'ipv4_private' in request.data and device.ipv4_private != request.data['ipv4_private']:
                device.ipv4_private = request.data['ipv4_private']
                update_fields.append('ipv4_private')
                needs_update = True
                
            # 检查IPv6地址
            # 检查固网IPv6地址
            if 'fixed_network_ipv6_address' in request.data and device.fixed_network_ipv6_address != request.data['fixed_network_ipv6_address']:
                device.fixed_network_ipv6_address = request.data['fixed_network_ipv6_address']
                update_fields.append('fixed_network_ipv6_address')
                needs_update = True

            # 检查移动网络IPv6地址 
            if 'mobile_network_ipv6_address' in request.data and device.mobile_network_ipv6_address != request.data['mobile_network_ipv6_address']:
                device.mobile_network_ipv6_address = request.data['mobile_network_ipv6_address']
                update_fields.append('mobile_network_ipv6_address')
                needs_update = True
                
            # 检查公网IPv4地址
            if device.ipv4_public != client_ip:
                device.ipv4_public = client_ip
                update_fields.append('ipv4_public')
                needs_update = True
            
            # 更新心跳状态和时间
            device.heartbeat_status = 'normal'
            device.last_heartbeat = timezone.now()
            
            # 一次性保存所有变更的字段
            device.save(update_fields=update_fields)

            # 保存监控数据
            monitor_data = {
                'device': device.id,
                'cpu_usage': request.data.get('cpu_usage', 0),
                'memory_usage': request.data.get('memory_usage', 0),
                'sent_rate': request.data.get('sent_rate', 0),
                'recv_rate': request.data.get('recv_rate', 0),
                'total_rate': request.data.get('total_rate', 0),
                'disk_io': request.data.get('disk_io', 0),
                'disk_usage': request.data.get('disk_usage', 0)
            }
            monitor_serializer = DeviceMonitorSerializer(data=monitor_data)
            if monitor_serializer.is_valid():
                monitor_serializer.save()

            return DetailResponse(msg="心跳更新成功")
        except Device.DoesNotExist:
            return ErrorResponse(msg="设备未注册")

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def api_login(self, request):
        """API客户端登录"""
        username = request.data.get('username')
        password = request.data.get('password')  # 客户端已经进行了MD5加密
        
        # 使用Django的认证系统验证用户
        user = authenticate(
            request,
            username=username,
            password=password,  # 密码已经是MD5加密的
        )
        
        if user:
            # 生成token
            refresh = RefreshToken.for_user(user)
            return DetailResponse(data={
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            })
        else:
            return ErrorResponse(msg="用户名或密码错误")

    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换终端启用状态"""
        device = self.get_object()
        device.enabled = not device.enabled
        device.save()
        return DetailResponse(data=DeviceSerializer(device).data, msg="状态更新成功")

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取终端统计信息"""
        total_count = Device.objects.count()
        online_count = Device.objects.filter(heartbeat_status='normal').count()
        offline_count = Device.objects.filter(heartbeat_status='interrupted').count()
        
        return DetailResponse(data={
            'total': total_count,
            'online': online_count,
            'offline': offline_count
        })

    def perform_destroy(self, instance):
        """删除时的额外处理"""
        # 可以在这里添加删除前的检查,比如检查是否有关联的任务
        super().perform_destroy(instance)

class TrafficTaskViewSet(CustomModelViewSet):
    """
    流量调度任务管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TrafficTask.objects.all()
    serializer_class = TrafficTaskSerializer
    permission_classes = [IsAuthenticated]
    ordering = ['-create_datetime']  # 按创建时间倒序排序
    
    def create(self, request, *args, **kwargs):
        with transaction.atomic():  # 使用事务确保数据一致性
            # 1. 验证请求数据
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            # 修改：根据请求数据中 task_type 判断任务设定类型，并验证对应必填字段
            task_type = request.data.get('task_type', 'flowrate')
            if task_type == "flowrate":
                if not request.data.get("target_duration"):
                    return ErrorResponse(msg="目标持续时间必填")
            elif task_type == "traffic":
                if not request.data.get("target_traffic"):
                    return ErrorResponse(msg="目标流量必填")
            else:
                return ErrorResponse(msg="无效的任务设定类型")
            
            # 2. 根据属地查找可用终端
            source_area = serializer.validated_data['source_area']
            target_area = serializer.validated_data['target_area']
            
            # 获取属地名称
            source_area_name = Area.objects.filter(code=source_area).values_list('name', flat=True).first() or source_area
            target_area_name = Area.objects.filter(code=target_area).values_list('name', flat=True).first() or target_area
            
            source_devices = Device.objects.filter(
                location=source_area,
                is_active=True,
                heartbeat_status='normal'
            )
            target_devices = Device.objects.filter(
                location=target_area,
                is_active=True,
                heartbeat_status='normal'
            )
            
            # 调度任务创建逻辑：主任务需要属地下有可用终端才能创建
            if not source_devices.exists():
                return ErrorResponse(msg=f"流量起点属地 {source_area_name} 下没有可用终端,无法创建任务")
                
            if not target_devices.exists():
                return ErrorResponse(msg=f"流量终点属地 {target_area_name} 下没有可用终端,无法创建任务")
            
            try:
                # 3. 创建主任务（不关联终端信息）
                task = serializer.save(
                    status='pending',
                    creator=request.user
                )
                
                # 4. 生成子任务：在子任务生成时判断终端设备能否满足主任务要求的流速
                if task.target_flowrate:
                    # 根据任务流向和网络类型确定使用哪项带宽指标和网卡
                    if task.network_type == 'fixed':
                        # 固网任务
                        if task.direction == "upload":
                            src_capacity_attr = "fixed_network_download_bandwidth"
                            tgt_capacity_attr = "fixed_network_upload_bandwidth"
                            src_interface = "fixed_network_interface"
                            tgt_interface = "fixed_network_interface"
                        else:
                            src_capacity_attr = "fixed_network_upload_bandwidth"
                            tgt_capacity_attr = "fixed_network_download_bandwidth"
                            src_interface = "fixed_network_interface"
                            tgt_interface = "fixed_network_interface"
                    else:
                        # 移动网任务
                        if task.direction == "upload":
                            src_capacity_attr = "mobile_network_download_bandwidth"
                            tgt_capacity_attr = "mobile_network_upload_bandwidth"
                            src_interface = "mobile_network_interface"
                            tgt_interface = "mobile_network_interface"
                        else:
                            src_capacity_attr = "mobile_network_upload_bandwidth"
                            tgt_capacity_attr = "mobile_network_download_bandwidth"
                            src_interface = "mobile_network_interface"
                            tgt_interface = "mobile_network_interface"
                    
                    # 获取终端列表（转换为 list 并过滤掉带宽值为空或非正的终端）
                    source_list = [d for d in list(source_devices) if getattr(d, src_capacity_attr) and getattr(d, src_capacity_attr) > 0]
                    target_list = [d for d in list(target_devices) if getattr(d, tgt_capacity_attr) and getattr(d, tgt_capacity_attr) > 0]
                    
                    # 按照带宽降序排序
                    source_list.sort(key=lambda d: getattr(d, src_capacity_attr), reverse=True)
                    target_list.sort(key=lambda d: getattr(d, tgt_capacity_attr), reverse=True)
                    
                    remaining_flow = task.target_flowrate
                    src_index = 0
                    tgt_index = 0
                    
                    # 采用贪心算法分配：每对终端分配的流速为当前两台设备各自带宽与剩余需求的最小值
                    while remaining_flow > 0 and src_index < len(source_list) and tgt_index < len(target_list):
                        src_dev = source_list[src_index]
                        tgt_dev = target_list[tgt_index]
                        src_cap = getattr(src_dev, src_capacity_attr)
                        tgt_cap = getattr(tgt_dev, tgt_capacity_attr)
                        allocated_flow = min(src_cap, tgt_cap, remaining_flow)
                        if allocated_flow <= 0:
                            if src_cap <= 0:
                                src_index += 1
                            if tgt_cap <= 0:
                                tgt_index += 1
                            continue
                        
                        TrafficTaskDetail.objects.create(
                            parent=task,
                            source_device=src_dev,
                            target_device=tgt_dev,
                            target_flowrate=allocated_flow,
                            target_duration=task.target_duration,
                            target_traffic=task.target_traffic,
                            direction=task.direction,
                            status='pending',
                            source_network_interface=getattr(src_dev, src_interface),
                            target_network_interface=getattr(tgt_dev, tgt_interface),
                            is_active=True
                        )
                        remaining_flow -= allocated_flow
                        src_index += 1
                        tgt_index += 1
                    
                    if remaining_flow > 0:
                        # 删除已创建的主任务和子任务
                        TrafficTaskDetail.objects.filter(parent=task).delete()
                        task.delete()
                        return ErrorResponse(msg=f"终端设备{task.network_type}网络总带宽不足以满足任务流速要求")
                else:
                    # 若未指定目标流速（或为 traffic 类型），则依旧使用列表中首个终端生成子任务
                    src_dev = source_devices.first()
                    tgt_dev = target_devices.first()
                    # 根据网络类型选择网卡
                    src_interface = "fixed_network_interface" if task.network_type == 'fixed' else "mobile_network_interface"
                    tgt_interface = "fixed_network_interface" if task.network_type == 'fixed' else "mobile_network_interface"
                    
                    TrafficTaskDetail.objects.create(
                        parent=task,
                        source_device=src_dev,
                        target_device=tgt_dev,
                        target_flowrate=task.target_flowrate,
                        target_duration=task.target_duration,
                        target_traffic=task.target_traffic,
                        direction=task.direction,
                        status='pending',
                        source_network_interface=getattr(src_dev, src_interface),
                        target_network_interface=getattr(tgt_dev, tgt_interface),
                        is_active=request.data.get('is_active', True)
                    )
                
                return Response(serializer.data)
                
            except Exception as e:
                # 发生任何异常时，删除已创建的主任务和子任务
                if 'task' in locals():
                    TrafficTaskDetail.objects.filter(parent=task).delete()
                    task.delete()
                raise e

    @action(detail=True, methods=['post'])
    def stop(self, request, pk=None):
        task = self.get_object()
        if task.status == 'running':
            # 发送停止命令到终端
            from .tasks import stop_traffic_task
            stop_traffic_task.delay(task.id)
            task.status = 'stopping'
            task.save()
        return Response({'status': 'stopping'})

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        
        try:
            task = serializer.save()
            
            # 删除现有子任务前先保存副本
            old_subtasks = list(TrafficTaskDetail.objects.filter(parent=task))
            
                       
            # 获取可用终端设备
            source_devices = Device.objects.filter(
                location=task.source_area,
                is_active=True,
                heartbeat_status='normal'
            )
            target_devices = Device.objects.filter(
                location=task.target_area,
                is_active=True,
                heartbeat_status='normal'
            )
            
            # 检查可用终端
            if not source_devices.exists():
                return ErrorResponse(msg=f"流量起点属地下没有可用终端,无法更新任务")
            
            if not target_devices.exists():
                return ErrorResponse(msg=f"流量终点属地下没有可用终端,无法更新任务")
            
            # 保存现有子任务副本
            old_subtasks = list(TrafficTaskDetail.objects.filter(parent=task))
            # 删除现有子任务
            TrafficTaskDetail.objects.filter(parent=task).delete()
            
            # 4. 重新生成子任务
            if task.target_flowrate:
                # 根据任务流向和网络类型确定使用哪项带宽指标和网卡
                if task.network_type == 'fixed':
                    # 固网任务
                    if task.direction == "upload":
                        src_capacity_attr = "fixed_network_download_bandwidth"
                        tgt_capacity_attr = "fixed_network_upload_bandwidth"
                        src_interface = "fixed_network_interface"
                        tgt_interface = "fixed_network_interface"
                    else:
                        src_capacity_attr = "fixed_network_upload_bandwidth"
                        tgt_capacity_attr = "fixed_network_download_bandwidth"
                        src_interface = "fixed_network_interface"
                        tgt_interface = "fixed_network_interface"
                else:
                    # 移动网任务
                    if task.direction == "upload":
                        src_capacity_attr = "mobile_network_download_bandwidth"
                        tgt_capacity_attr = "mobile_network_upload_bandwidth"
                        src_interface = "mobile_network_interface"
                        tgt_interface = "mobile_network_interface"
                    else:
                        src_capacity_attr = "mobile_network_upload_bandwidth"
                        tgt_capacity_attr = "mobile_network_download_bandwidth"
                        src_interface = "mobile_network_interface"
                        tgt_interface = "mobile_network_interface"
                
                # 获取终端列表并过滤
                source_list = [d for d in list(source_devices) if getattr(d, src_capacity_attr) and getattr(d, src_capacity_attr) > 0]
                target_list = [d for d in list(target_devices) if getattr(d, tgt_capacity_attr) and getattr(d, tgt_capacity_attr) > 0]
                
                # 按照带宽降序排序
                source_list.sort(key=lambda d: getattr(d, src_capacity_attr), reverse=True)
                target_list.sort(key=lambda d: getattr(d, tgt_capacity_attr), reverse=True)
                
                remaining_flow = task.target_flowrate
                src_index = 0
                tgt_index = 0
                
                # 采用贪心算法分配
                while remaining_flow > 0 and src_index < len(source_list) and tgt_index < len(target_list):
                    src_dev = source_list[src_index]
                    tgt_dev = target_list[tgt_index]
                    src_cap = getattr(src_dev, src_capacity_attr)
                    tgt_cap = getattr(tgt_dev, tgt_capacity_attr)
                    allocated_flow = min(src_cap, tgt_cap, remaining_flow)
                    
                    if allocated_flow <= 0:
                        if src_cap <= 0:
                            src_index += 1
                        if tgt_cap <= 0:
                            tgt_index += 1
                        continue
                    
                    TrafficTaskDetail.objects.create(
                        parent=task,
                        source_device=src_dev,
                        target_device=tgt_dev,
                        target_flowrate=allocated_flow,
                        target_duration=task.target_duration,
                        target_traffic=task.target_traffic,
                        direction=task.direction,
                        status='pending',
                        source_network_interface=getattr(src_dev, src_interface),
                        target_network_interface=getattr(tgt_dev, tgt_interface),
                        is_active=task.is_active
                    )
                    remaining_flow -= allocated_flow
                    src_index += 1
                    tgt_index += 1
                
                if remaining_flow > 0:
                    # 删除新建的子任务
                    TrafficTaskDetail.objects.filter(parent=task).delete()
                    
                    # 恢复原来的子任务
                    for subtask in old_subtasks:
                        TrafficTaskDetail.objects.create(
                            parent=task,  # 使用当前任务作为父任务
                            source_device=subtask.source_device,
                            target_device=subtask.target_device,
                            target_flowrate=subtask.target_flowrate,
                            target_duration=subtask.target_duration,
                            target_traffic=subtask.target_traffic,
                            direction=subtask.direction,
                            status=subtask.status,
                            source_network_interface=subtask.source_network_interface,
                            target_network_interface=subtask.target_network_interface,
                            is_active=subtask.is_active,
                            description=subtask.description
                        )
                    
                    return ErrorResponse(msg=f"终端设备{task.network_type}网络总带宽不足以满足任务流速要求")
            else:
                # 若未指定目标流速，使用首个终端生成子任务
                src_dev = source_devices.first()
                tgt_dev = target_devices.first()
                # 根据网络类型选择网卡
                src_interface = "fixed_network_interface" if task.network_type == 'fixed' else "mobile_network_interface"
                tgt_interface = "fixed_network_interface" if task.network_type == 'fixed' else "mobile_network_interface"
                
                TrafficTaskDetail.objects.create(
                    parent=task,
                    source_device=src_dev,
                    target_device=tgt_dev,
                    target_flowrate=task.target_flowrate,
                    target_duration=task.target_duration,
                    target_traffic=task.target_traffic,
                    direction=task.direction,
                    status='pending',
                    source_network_interface=getattr(src_dev, src_interface),
                    target_network_interface=getattr(tgt_dev, tgt_interface),
                    is_active=True
                )
            
            return Response(serializer.data)
            
        except Exception as e:
            # 发生任何异常时回滚
            if 'task' in locals():
                TrafficTaskDetail.objects.filter(parent=task).delete()
                # 恢复原来的子任务
                for subtask in old_subtasks:
                    TrafficTaskDetail.objects.create(
                        parent=task,  # 使用当前任务作为父任务
                        source_device=subtask.source_device,
                        target_device=subtask.target_device,
                        target_flowrate=subtask.target_flowrate,
                        target_duration=subtask.target_duration,
                        target_traffic=subtask.target_traffic,
                        direction=subtask.direction,
                        status=subtask.status,
                        source_network_interface=subtask.source_network_interface,
                        target_network_interface=subtask.target_network_interface,
                        is_active=subtask.is_active,
                        description=subtask.description
                    )
            raise e

    def perform_destroy(self, instance):
        """删除时的额外处理"""
        # 可以在这里添加删除前的检查,比如检查是否有关联的任务
        super().perform_destroy(instance) 

class TrafficTaskDetailViewSet(CustomModelViewSet):
    """
    流量任务详情管理接口
    """
    queryset = TrafficTaskDetail.objects.all()
    serializer_class = TrafficTaskDetailSerializer
    permission_classes = [IsAuthenticated]
    
    # 明确指定允许的方法
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        # 支持按父任务ID过滤
        parent = self.request.query_params.get('parent', None)
        if parent:
            queryset = queryset.filter(parent_id=parent)
        return queryset
    
    @action(detail=True, methods=['post'])
    def stop(self, request, pk=None):
        """终止子任务"""
        task = self.get_object()
        if task.status == 'running':
            # 发送停止命令到终端
            from .tasks import stop_traffic_subtask
            stop_traffic_subtask.delay(task.id)
            task.status = 'stopping'
            task.save()
            return DetailResponse(msg="正在终止任务")
        elif task.status == 'stopping':
            return DetailResponse(msg="任务正在终止中")
        else:
            return ErrorResponse(msg=f"当前状态({task.status})下无法终止任务")

    def partial_update(self, request, *args, **kwargs):
        """
        处理 PATCH 请求，实现部分更新
        """
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs) 

class TaskResultViewSet(CustomModelViewSet):
    """
    任务结果管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = TaskResult.objects.all().order_by('-actual_end_time')
    serializer_class = TaskResultSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 获取所有过滤条件
        filters = {
            'task_detail_id': self.request.query_params.get('task_detail_id'),
            'task_detail__parent__task_name__icontains': self.request.query_params.get('parent_name'),
            'task_detail__source_device__name__icontains': self.request.query_params.get('source_device_name'),
            'task_detail__target_device__name__icontains': self.request.query_params.get('target_device_name'),
            'result': self.request.query_params.get('result')
        }
        
        # 移除空值条件
        filters = {k: v for k, v in filters.items() if v is not None}
        
        # 如果有过滤条件,应用所有条件
        if filters:
            queryset = queryset.filter(**filters)
            
        return queryset

class OperationLogViewSet(CustomModelViewSet):
    def create(self, request, *args, **kwargs):
        # 检查是否是心跳请求
        if request.data.get('request_path', '').endswith('/api/tss/devices/heartbeat/'):
            # 心跳请求直接返回成功，不记录日志
            return Response(status=status.HTTP_200_OK)
            
        # 其他请求正常记录日志
        return super().create(request, *args, **kwargs) 

class DeviceMonitorViewSet(CustomModelViewSet):
    queryset = DeviceMonitor.objects.all()
    serializer_class = DeviceMonitorSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # 构建过滤条件字典
        filters = {}
        
        # 设备名称搜索
        device_name = self.request.query_params.get('device_name', None)
        if device_name:
            filters['device__name__icontains'] = device_name
            
        # 属地搜索
        location = self.request.query_params.get('location', None)
        if location:
            filters['device__location__code'] = location
            
        # 时间范围搜索
        start_time = self.request.query_params.get('create_datetime_range[0]', None)
        end_time = self.request.query_params.get('create_datetime_range[1]', None)
        if start_time or end_time:
            if start_time:
                filters['create_datetime__gte'] = start_time
            if end_time:
                filters['create_datetime__lte'] = end_time
                
        # 一次性应用所有过滤条件
        if filters:
            queryset = queryset.filter(**filters)
                
        return queryset

    @action(detail=False, methods=['get'])
    def traffic_data(self, request):
        """获取流量数据"""
        device_id = request.query_params.get('device_id')
        time_range = request.query_params.get('time_range', 'day')
        
        if not device_id:
            return ErrorResponse(msg="设备ID不能为空")
            
        try:
            device_id = int(device_id)  # 确保是整数
        except ValueError:
            return ErrorResponse(msg="无效的设备ID")
            
        now = timezone.now()
        
        # 根据时间范围设置查询参数
        if time_range == 'day':
            start_time = now - timedelta(days=1)
            interval = timedelta(hours=1)
            points = 24
        elif time_range == 'week':
            start_time = now - timedelta(weeks=1)
            interval = timedelta(days=1)
            points = 7
        elif time_range == 'month':
            start_time = now - timedelta(days=30)
            interval = timedelta(days=1)
            points = 30
        elif time_range == 'year':
            start_time = now - timedelta(days=365)
            interval = timedelta(days=30)
            points = 12
        else:
            return ErrorResponse(msg="无效的时间范围")
            
        # 查询数据
        data = []
        current_time = start_time
        
        for _ in range(points):
            next_time = current_time + interval
            
            # 获取时间段内的平均值
            avg_data = DeviceMonitor.objects.filter(
                device_id=device_id,
                create_datetime__gte=current_time,
                create_datetime__lt=next_time
            ).aggregate(
                avg_recv=Avg('recv_rate'),
                avg_sent=Avg('sent_rate'),
                avg_total=Avg('total_rate')
            )
            
            data.append({
                'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'recv_rate': round(avg_data['avg_recv'] or 0, 2),
                'sent_rate': round(avg_data['avg_sent'] or 0, 2),
                'total_rate': round(avg_data['avg_total'] or 0, 2)
            })
            
            current_time = next_time
            
        # 格式化返回数据
        result = {
            'timestamps': [item['timestamp'] for item in data],
            'recv_rates': [item['recv_rate'] for item in data],
            'sent_rates': [item['sent_rate'] for item in data],
            'total_rates': [item['total_rate'] for item in data]
        }
        
        return DetailResponse(data=result) 

class TrafficStatisticsModelViewSet(CustomModelViewSet):
    queryset = TrafficStatistics.objects.all()
    serializer_class = TrafficStatisticsSerializer
    # 添加过滤字段
    filter_fields = {
        'device_id': ['exact'],
        'location': ['exact'],
        'network_type': ['exact'],
        'stat_level': ['exact'],
        'stat_time': ['gte', 'lte'],
    }

    def get_queryset(self):
        queryset = super().get_queryset()
        # 获取查询参数
        device_id = self.request.query_params.get('device_id', None)
        
        # 如果有设备ID参数，进行过滤
        if device_id:
            queryset = queryset.filter(device_id=device_id)
            
        return queryset 