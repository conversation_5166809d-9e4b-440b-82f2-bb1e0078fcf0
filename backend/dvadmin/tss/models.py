from django.db import models
from dvadmin.utils.models import CoreModel

# 常量定义
STATUS_CHOICES = (
    ('pending', '等待中'),
    ('scheduling', '调度中'),
    ('running', '进行中'),
    ('completed', '已完成'),
    ('failed', '失败')
)

DIRECTION_CHOICES = (
    ('upload', '上行'),
    ('download', '下行')
)

SCHEDULE_TYPE_CHOICES = (
    ('once', '一次性调度'),
    ('periodic', '周期性调度')
)

PERIOD_TYPE_CHOICES = (
    ('hourly', '每小时'),
    ('daily', '每天'),
    ('weekly', '每周'),
    ('monthly', '每月')
)

class Device(CoreModel):
    """终端设备模型"""
    serial_number = models.CharField(max_length=200, unique=True, verbose_name="序列号")
    mac_address = models.CharField(max_length=200, verbose_name="MAC地址")
    ipv4_private = models.GenericIPAddressField(verbose_name="IPv4私网地址", null=True, blank=True)
    ipv4_public = models.GenericIPAddressField(verbose_name="IPv4公网地址", null=True, blank=True)
    
    # 拆分 IPv6 地址字段
    fixed_network_ipv6_address = models.GenericIPAddressField(
        verbose_name="固网IPv6地址", 
        protocol='IPv6', 
        null=True, 
        blank=True,
        help_text="固网IPv6地址"
    )
    mobile_network_ipv6_address = models.GenericIPAddressField(
        verbose_name="移动网IPv6地址",
        protocol='IPv6',
        null=True,
        blank=True,
        help_text="移动网IPv6地址"
    )
    
    location = models.ForeignKey(
        'system.Area',  # 关联到system.Area模型
        to_field='code',  # 关联到code字段
        on_delete=models.SET_DEFAULT,  # 当区域被删除时，设置为2784
        default=53,  # 默认值为2784
        null=True,
        blank=True,
        verbose_name="属地",
        related_name='devices',  # 反向关联名
        db_column='location'  # 指定数据库列名为location
    )
    is_active = models.BooleanField(default=True, verbose_name="启用状态")
    fixed_network_upload_bandwidth = models.IntegerField(default=0, verbose_name="固网上行带宽(Mbps)")
    fixed_network_download_bandwidth = models.IntegerField(default=0, verbose_name="固网下行带宽(Mbps)")
    mobile_network_upload_bandwidth = models.IntegerField(default=0, verbose_name="移动网上行带宽(Mbps)")
    mobile_network_download_bandwidth = models.IntegerField(default=0, verbose_name="移动网下行带宽(Mbps)")
    heartbeat_status = models.CharField(
        max_length=20, 
        default="normal",
        choices=[("normal", "正常"), ("interrupted", "中断")],
        verbose_name="心跳状态",
        null=True,
        blank=True
    )
    ssh_reverse_port = models.IntegerField(verbose_name="SSH反向端口", null=True, blank=True)
    ssh_port = models.IntegerField(verbose_name="SSH正向端口", default=22, null=True, blank=True)
    ssh_username = models.CharField(max_length=50, verbose_name="SSH用户名", null=True, blank=True)
    ssh_password = models.CharField(max_length=100, verbose_name="SSH密码", null=True, blank=True)
    last_heartbeat = models.DateTimeField(null=True, blank=True, verbose_name="最后心跳时间")
    name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='终端名称',
        help_text='终端自定义名称'
    )

    # 增加网卡字段
    fixed_network_interface = models.CharField(
        max_length=50, 
        null=True,
        blank=True,
        verbose_name="固网网卡",
        help_text="固网网卡名称"
    )
    mobile_network_interface = models.CharField(
        max_length=50,
        null=True,
        blank=True, 
        verbose_name="移动网网卡",
        help_text="移动网网卡名称"
    )

    class Meta:
        db_table = "tss_device"
        verbose_name = "终端设备"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class DeviceMonitor(CoreModel):
    """设备监控数据"""
    device = models.ForeignKey(
        'Device',
        on_delete=models.CASCADE,
        verbose_name="设备"
    )
    cpu_usage = models.FloatField(verbose_name="CPU使用率", null=True, blank=True)
    memory_usage = models.FloatField(verbose_name="内存使用率", null=True, blank=True)
    sent_rate = models.FloatField(verbose_name="发送速率(Kbps)", null=True, blank=True)
    recv_rate = models.FloatField(verbose_name="接收速率(Kbps)", null=True, blank=True)
    total_rate = models.FloatField(verbose_name="总速率(Kbps)", null=True, blank=True)
    disk_io = models.FloatField(verbose_name="磁盘IO使用率", null=True, blank=True)
    disk_usage = models.FloatField(verbose_name="磁盘使用率", null=True, blank=True)

    class Meta:
        db_table = "tss_device_monitor"
        verbose_name = "设备监控数据"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class TrafficTask(CoreModel):
    # 增加网络类型字段
    NETWORK_TYPE_CHOICES = (
        ('fixed', '固网'),
        ('mobile', '移动网')
    )
    network_type = models.CharField(
        max_length=20,
        choices=NETWORK_TYPE_CHOICES,
        default='fixed',
        verbose_name="网络类型",
        help_text="网络类型",
        null=True,
        blank=True
    )
    """流量调度任务"""
    TASK_TYPE_CHOICES = (
        ('flowrate', '流速'),
        ('traffic', '流量'),
    )
    task_type = models.CharField(
        max_length=20,
        choices=TASK_TYPE_CHOICES,
        default='flowrate',
        verbose_name="任务类型",
        help_text="任务类型"
    )
    task_name = models.CharField(
        null=True,
        max_length=100, 
        verbose_name='任务名称',
        help_text='任务名称(100字符限)'
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='任务描述',
        help_text='任务描述(200字符限)'
    )
    source_area = models.CharField(max_length=100, verbose_name='流量起点')
    target_area = models.CharField(max_length=100, verbose_name='流量终点')
    direction = models.CharField(max_length=20, choices=DIRECTION_CHOICES, verbose_name='流量方向')
    target_flowrate = models.IntegerField(verbose_name='目标流速(Mbit/s)', null=True, blank=True,
                                        help_text='指定流速时必填')
    target_traffic = models.IntegerField(verbose_name='目标流量(MB)', null=True, blank=True,
                                       help_text='指定目标流量时必填')
    target_duration = models.IntegerField(verbose_name='目标持续时间(秒)', null=True, blank=True,
                                        help_text='指定目标流速时必填')
    target_start_time = models.DateTimeField(verbose_name='计划开始时间', null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    
    # 添加调度相关字段
    schedule_type = models.CharField(max_length=20, choices=SCHEDULE_TYPE_CHOICES, default='once', 
                                   verbose_name='调度方式')
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPE_CHOICES, null=True, blank=True, 
                                 verbose_name='周期类型')
    period_days = models.CharField(max_length=100, null=True, blank=True, verbose_name='周期日期',
                                 help_text='每周/每月时需要设置具体哪几天执行,多个用逗号分隔')
    period_time = models.TimeField(null=True, blank=True, verbose_name='执行时间',
                                 help_text='周期任务的执行时间点')
    period_minute = models.IntegerField(null=True, blank=True, verbose_name='执行分钟',
                                      help_text='每小时的第几分钟执行,0-59')
    
    creator = models.ForeignKey('system.Users', on_delete=models.SET_NULL, null=True)
    create_datetime = models.DateTimeField(auto_now_add=True)
    update_datetime = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True, verbose_name='是否启用', help_text='是否启用')

    class Meta:
        db_table = "tss_traffictask"
        verbose_name = '流量调度任务'
        verbose_name_plural = verbose_name
        ordering = ['-create_datetime']

class TrafficTaskDetail(CoreModel):
    """流量任务详情"""
    parent = models.ForeignKey(
        TrafficTask,
        on_delete=models.CASCADE,
        verbose_name="父任务",
        help_text="父任务ID",
        null=True,
        blank=True
    )
    source_device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='source_tasks',
        verbose_name="源设备",
        null=True,
        blank=True
    )
    target_device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='target_tasks',
        verbose_name="目标设备",
        null=True,
        blank=True
    )
    target_flowrate = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标流速",
        help_text="目标流速(Mbps)"
    )
    target_traffic = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标流量",
        help_text="目标流量(MB)"
    )
    target_duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标持续时间",
        help_text="目标持续时间(秒)"
    )
    direction = models.CharField(
        max_length=20,
        verbose_name="流量方向",
        help_text="upload/download"
    )
    status = models.CharField(
        max_length=20,
        default='pending',
        verbose_name="状态"
    )
    description = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name="描述"
    )
    source_network_interface = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="源设备网卡",
        help_text="源设备网卡"
    )
    target_network_interface = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="目标设备网卡",
        help_text="目标设备网卡"
    )
    is_active = models.BooleanField(default=True, verbose_name='是否启用', help_text='是否启用')

    class Meta:
        db_table = "tss_traffictask_detail"
        verbose_name = "流量任务详情"
        verbose_name_plural = verbose_name

class TaskResult(CoreModel):
    """任务执行结果表"""
    RESULT_CHOICES = (
        ('success', '成功'),
        ('failed', '失败')
    )
    
    parent = models.ForeignKey(
        TrafficTask,
        on_delete=models.CASCADE,
        verbose_name="主任务",
        help_text="关联的主任务",
        null=True,
        blank=True
    )
    
    task_detail = models.ForeignKey(
        TrafficTaskDetail,
        on_delete=models.CASCADE,
        verbose_name="子任务",
        help_text="关联的子任务"
    )
    
    # 网络类型
    network_type = models.CharField(
        max_length=20,
        choices=(('fixed', '固网'), ('mobile', '移动网')),
        verbose_name="网络类型",
        null=True,
        blank=True
    )
    
    # 区域信息
    source_area = models.CharField(max_length=100, verbose_name='流量起点', null=True, blank=True)
    target_area = models.CharField(max_length=100, verbose_name='流量终点', null=True, blank=True)
    
    # 设备信息
    source_device = models.ForeignKey(
        Device,
        on_delete=models.SET_NULL,
        null=True,
        related_name='source_results',
        verbose_name="源设备"
    )
    target_device = models.ForeignKey(
        Device,
        on_delete=models.SET_NULL,
        null=True,
        related_name='target_results',
        verbose_name="目标设备"
    )
    
    # 网络接口
    source_network_interface = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="源设备网卡"
    )
    target_network_interface = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="目标设备网卡"
    )
    
    # IPv6地址
    source_ipv6 = models.GenericIPAddressField(
        protocol='IPv6',
        null=True,
        blank=True,
        verbose_name="源IPv6地址"
    )
    target_ipv6 = models.GenericIPAddressField(
        protocol='IPv6',
        null=True,
        blank=True,
        verbose_name="目标IPv6地址"
    )
    
    # 任务配置
    direction = models.CharField(
        max_length=20,
        choices=DIRECTION_CHOICES,
        verbose_name="流量方向",
        null=True,
        blank=True
    )
    target_flowrate = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标流速(Mbit/s)"
    )
    target_traffic = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标流量(MB)"
    )
    target_duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="目标持续时间(秒)"
    )
    
    # 周期配置
    schedule_type = models.CharField(
        max_length=20,
        choices=SCHEDULE_TYPE_CHOICES,
        verbose_name="调度方式",
        null=True,
        blank=True
    )
    period_type = models.CharField(
        max_length=20,
        choices=PERIOD_TYPE_CHOICES,
        null=True,
        blank=True,
        verbose_name="周期类型"
    )
    period_days = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="周期日期"
    )
    period_minute = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="执行分钟"
    )
    period_time = models.TimeField(
        null=True,
        blank=True,
        verbose_name="执行时间"
    )
    target_start_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="计划开始时间"
    )
    
    # 执行结果
    result = models.CharField(
        max_length=20,
        choices=RESULT_CHOICES,
        verbose_name="执行结果",
        help_text="success/failed"
    )
    current_flowrate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="当前流速",
        help_text="当前流速(Mbps)"
    )
    total_traffic = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        verbose_name="总流量",
        help_text="总流量(MB)"
    )
    actual_duration = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="实际持续时间",
        help_text="实际持续时间(秒)"
    )
    actual_start_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="实际开始时间"
    )
    actual_end_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="实际结束时间"
    )
    failure_reason = models.TextField(
        null=True,
        blank=True,
        verbose_name="失败原因"
    )
    result_data = models.JSONField(
        null=True,
        blank=True,
        verbose_name="详细结果数据"
    )

    class Meta:
        db_table = "tss_traffictask_result"
        verbose_name = "任务执行结果"
        verbose_name_plural = verbose_name
        ordering = ('-create_datetime',)

class TrafficStatistics(CoreModel):
    """流量统计表"""
    # 终端信息
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='traffic_statistics',
        null=True,
        blank=True,
        verbose_name="终端",
        help_text="终端"
    )
    location = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="终端位置",
        help_text="终端位置"
    )
    device_name = models.CharField(
        null=True,
        blank=True,
        max_length=100,
        verbose_name="终端名称",
        help_text="终端名称"
    )  

    # 任务信息
    task_name = models.CharField(
        max_length=100,
        verbose_name="任务名称",
        help_text="任务名称"
    )
    network_type = models.CharField(
        max_length=20,
        verbose_name="网络类型",
        help_text="网络类型(fixed/mobile)"
    )
    task_type = models.CharField(
        max_length=20,
        verbose_name="任务类型",
        help_text="任务类型(flowrate/traffic)"
    )
    stat_time = models.DateTimeField(
        verbose_name="统计时间",
        help_text="统计时间"
    )

    # 流量数据
    upload_flowrate = models.DecimalField(
        max_digits=10,
        default=0,
        null=True,
        blank=True,
        decimal_places=2,
        verbose_name="上行流速(Mbps)",
        help_text="上行流速(Mbps)"
    )
    download_flowrate = models.DecimalField(
        max_digits=10,
        default=0,
        null=True,
        blank=True,
        decimal_places=2,
        verbose_name="下行流速(Mbps)",
        help_text="下行流速(Mbps)"
    )
    upload_traffic = models.DecimalField(
        max_digits=10,
        default=0,
        null=True,
        blank=True,
        decimal_places=2,
        verbose_name="上行流量(MB)",
        help_text="上行流量(MB)"
    )
    download_traffic = models.DecimalField(
        max_digits=10,
        default=0,
        null=True,
        blank=True,
        decimal_places=2,
        verbose_name="下行流量(MB)",
        help_text="下行流量(MB)"
    )
    upload_duration = models.IntegerField(
        default=0,
        null=True,
        blank=True,
        verbose_name="上行持续时间(秒)",
        help_text="上行持续时间(秒)"
    )
    download_duration = models.IntegerField(
        default=0,
        null=True,
        blank=True,
        verbose_name="下行持续时间(秒)",
        help_text="下行持续时间(秒)"
    )

    # 统计级别
    STAT_LEVEL_CHOICES = (
        # 终端级别
        ('terminal_day_fixed', '终端日级别-固网'),
        ('terminal_day_mobile', '终端日级别-移动网'),
        ('terminal_day_total', '终端日级别-总计'),
        ('terminal_week_fixed', '终端周级别-固网'),
        ('terminal_week_mobile', '终端周级别-移动网'),
        ('terminal_week_total', '终端周级别-总计'),
        ('terminal_month_fixed', '终端月级别-固网'),
        ('terminal_month_mobile', '终端月级别-移动网'),
        ('terminal_month_total', '终端月级别-总计'),
        ('terminal_quarter_fixed', '终端季度级别-固网'),
        ('terminal_quarter_mobile', '终端季度级别-移动网'),
        ('terminal_quarter_total', '终端季度级别-总计'),
        ('terminal_year_fixed', '终端年级别-固网'),
        ('terminal_year_mobile', '终端年级别-移动网'),
        ('terminal_year_total', '终端年级别-总计'),
        
        # 属地级别
        ('area_day_fixed', '属地日级别-固网'),
        ('area_day_mobile', '属地日级别-移动网'),
        ('area_day_total', '属地日级别-总计'),
        ('area_week_fixed', '属地周级别-固网'),
        ('area_week_mobile', '属地周级别-移动网'),
        ('area_week_total', '属地周级别-总计'),
        ('area_month_fixed', '属地月级别-固网'),
        ('area_month_mobile', '属地月级别-移动网'),
        ('area_month_total', '属地月级别-总计'),
        ('area_quarter_fixed', '属地季度级别-固网'),
        ('area_quarter_mobile', '属地季度级别-移动网'),
        ('area_quarter_total', '属地季度级别-总计'),
        ('area_year_fixed', '属地年级别-固网'),
        ('area_year_mobile', '属地年级别-移动网'),
        ('area_year_total', '属地年级别-总计'),
        
        # 全省级别
        ('province_day_fixed', '全省日级别-固网'),
        ('province_day_mobile', '全省日级别-移动网'),
        ('province_day_total', '全省日级别-总计'),
        ('province_week_fixed', '全省周级别-固网'),
        ('province_week_mobile', '全省周级别-移动网'),
        ('province_week_total', '全省周级别-总计'),
        ('province_month_fixed', '全省月级别-固网'),
        ('province_month_mobile', '全省月级别-移动网'),
        ('province_month_total', '全省月级别-总计'),
        ('province_quarter_fixed', '全省季度级别-固网'),
        ('province_quarter_mobile', '全省季度级别-移动网'),
        ('province_quarter_total', '全省季度级别-总计'),
        ('province_year_fixed', '全省年级别-固网'),
        ('province_year_mobile', '全省年级别-移动网'),
        ('province_year_total', '全省年级别-总计'),
    )
    stat_level = models.CharField(
        max_length=30,
        choices=STAT_LEVEL_CHOICES,
        verbose_name="统计级别",
        help_text="统计级别"
    )

    class Meta:
        db_table = "tss_traffic_statistics"
        verbose_name = "流量统计"
        verbose_name_plural = verbose_name
        ordering = ('-stat_time',)
        indexes = [
            models.Index(fields=['device', 'stat_time']),
            models.Index(fields=['location', 'stat_time']),
            models.Index(fields=['stat_level', 'stat_time']),
        ]

