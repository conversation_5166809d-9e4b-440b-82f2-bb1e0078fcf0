from rest_framework import serializers
from dvadmin.utils.serializers import CustomModelSerializer
from .models import Device, DeviceMonitor, TrafficTask, TrafficTaskDetail, TaskResult, TrafficStatistics
from dvadmin.system.models import Area  # 导入Area模型


class DeviceSerializer(CustomModelSerializer):
    """设备序列化器"""
    location_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Device
        fields = "__all__"
        read_only_fields = ["id"]
        extra_kwargs = {
            'ssh_password': {'write_only': True}  # 设置ssh_password为只写字段
        }

    def get_location_name(self, obj):
        """获取属地名称"""
        if obj.location:
            return obj.location.name
        return None

class LocationSerializer(CustomModelSerializer):
    """属地序列化器"""
    class Meta:
        model = Area
        fields = ['id', 'name', 'code', 'pcode']

class DeviceMonitorSerializer(CustomModelSerializer):
    """设备监控序列化器"""
    device_name = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    area_name = serializers.SerializerMethodField()
    
    class Meta:
        model = DeviceMonitor
        fields = "__all__"
        read_only_fields = ["id"]

    def get_device_name(self, obj):
        """获取设备名称"""
        if obj.device:
            return obj.device.name
        return None

    def get_location(self, obj):
        """获取属地"""
        if obj.device:
            return obj.device.location.code
        return None

    def get_area_name(self, obj):
        """获取属地名称"""
        if obj.device and obj.device.location:
            return obj.device.location.name
        return None

class DeviceRegisterSerializer(CustomModelSerializer):
    """设备注册序列化器"""
    class Meta:
        model = Device
        fields = '__all__'
        read_only_fields = [
            'id', 'create_datetime', 'creator'
        ]
        
    def validate(self, attrs):
        """验证数据"""
        # 确保必要字段存在
        required_fields = ['serial_number']
        for field in required_fields:
            if field not in attrs:
                raise serializers.ValidationError(f"{field} is required")
                
        # 移除空值字段，让它们使用模型的默认值
        optional_fields = [
            'ipv4_public', 'fixed_network_ipv6_address', 'mobile_network_ipv6_address',
            'fixed_network_interface', 'mobile_network_interface',  # 添加这两个字段
            'location', 'is_active',            
            'fixed_network_upload_bandwidth', 'fixed_network_download_bandwidth',
            'mobile_network_upload_bandwidth', 'mobile_network_download_bandwidth',
            'heartbeat_status', 'ssh_port', 'ssh_username', 
            'ssh_password', 'last_heartbeat', 'mac_address','ipv4_private'
        ]
        
        # 创建一个新的字典，只包含非空值
        validated_data = {}
        for field, value in attrs.items():
            if field in required_fields or (field in optional_fields and value not in [None, '']):
                validated_data[field] = value
                
        return validated_data

    def create(self, validated_data):
        """创建设备"""
        # 使用模型的默认值处理未提供的字段
        return super().create(validated_data)

class TrafficTaskSerializer(CustomModelSerializer):
    """流量任务序列化器"""
    source_area_name = serializers.SerializerMethodField()
    target_area_name = serializers.SerializerMethodField()
    subtask_count = serializers.SerializerMethodField()
    
    class Meta:
        model = TrafficTask
        fields = "__all__"
        read_only_fields = ["id"]
    
    def get_source_area_name(self, obj):
        """获取流量起点属地名称"""
        return Area.objects.filter(code=obj.source_area).values_list('name', flat=True).first() or obj.source_area
    
    def get_target_area_name(self, obj):
        """获取流量终点属地名称"""
        return Area.objects.filter(code=obj.target_area).values_list('name', flat=True).first() or obj.target_area

    def get_subtask_count(self, obj):
        return obj.traffictaskdetail_set.count()

class TrafficTaskDetailSerializer(CustomModelSerializer):
    """流量任务详情序列化器"""   
    
    class Meta:
        model = TrafficTaskDetail
        fields = "__all__"  # 使用所有字段
        read_only_fields = ['failure_reason']  # 设置为只读字段
    
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # 添加设备信息
        if instance.source_device:
            representation['source_device_name'] = instance.source_device.name
        if instance.target_device:
            representation['target_device_name'] = instance.target_device.name
        # 添加主任务信息
        if instance.parent:
            representation['main_task_name'] = instance.parent.task_name
            representation['main_task_status'] = instance.parent.status
        return representation

class TaskResultSerializer(CustomModelSerializer):
    """任务结果序列化器"""
    class Meta:
        model = TaskResult
        fields = "__all__"
        read_only_fields = ["id"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        # 添加主任务相关字段
        if instance.task_detail:
            representation['task_detail_id'] = instance.task_detail.id
            if instance.task_detail.parent:
                representation['parent_id'] = instance.task_detail.parent.id
                representation['parent_name'] = instance.task_detail.parent.task_name
                # 添加属地名称
                representation['source_area_name'] = Area.objects.filter(code=instance.source_area).values_list('name', flat=True).first() 
                representation['target_area_name'] = Area.objects.filter(code=instance.target_area).values_list('name', flat=True).first() 
            
            # 添加设备相关字段
            if instance.task_detail.source_device:
                representation['source_device_id'] = instance.task_detail.source_device.id
                representation['source_device_name'] = instance.task_detail.source_device.name
                
            if instance.task_detail.target_device:
                representation['target_device_id'] = instance.task_detail.target_device.id
                representation['target_device_name'] = instance.task_detail.target_device.name
                
        return representation

class TrafficStatisticsSerializer(CustomModelSerializer):
    """流量统计序列化器"""
    class Meta:
        model = TrafficStatistics
        fields = "__all__"
        read_only_fields = ["id"]

    def to_representation(self, instance):
        """自定义序列化输出"""
        representation = super().to_representation(instance)
        
        # 添加设备名称
        if instance.device:
            representation['device_name'] = instance.device.name
            
        # 添加属地名称
        if instance.location:
            area = Area.objects.filter(code=instance.location).first()
            if area:
                representation['area_name'] = area.name
                
        return representation