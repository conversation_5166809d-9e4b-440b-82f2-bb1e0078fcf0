import json
import asyncio
import asyncssh
from channels.generic.websocket import AsyncWebsocketConsumer

class TerminalConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.device_id = self.scope['url_route']['kwargs']['device_id']
        
        # 获取设备信息
        from .models import Device
        try:
            self.device = await self.get_device()
            await self.accept()
            # 建立 SSH 连接
            self.ssh_conn = await asyncssh.connect(
                'localhost',
                port=self.device.ssh_reverse_port,
                username=self.device.ssh_username,
                password=self.device.ssh_password,
                known_hosts=None
            )
            self.ssh_process = await self.ssh_conn.create_process()
        except Exception as e:
            await self.send(text_data=f"连接错误: {str(e)}")
            await self.close()

    async def disconnect(self, close_code):
        if hasattr(self, 'ssh_process'):
            self.ssh_process.close()
        if hasattr(self, 'ssh_conn'):
            self.ssh_conn.close()

    async def receive(self, text_data):
        try:
            await self.ssh_process.stdin.write(text_data + '\n')
            response = await self.ssh_process.stdout.readline()
            await self.send(text_data=response)
        except Exception as e:
            await self.send(text_data=f"错误: {str(e)}")

    @database_sync_to_async
    def get_device(self):
        return Device.objects.get(id=self.device_id) 