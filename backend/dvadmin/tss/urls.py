from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import DeviceViewSet, TrafficTaskViewSet,TaskResultViewSet,TrafficTaskDetailViewSet, DeviceMonitorViewSet,TrafficStatisticsModelViewSet

router = DefaultRouter()
router.register(r'devices', DeviceViewSet, basename='device')
router.register(r'traffictask', TrafficTaskViewSet)
router.register(r'task_details', TrafficTaskDetailViewSet)
router.register(r'task_result', TaskResultViewSet)
router.register(r'device_monitor', DeviceMonitorViewSet)
router.register(r'traffic_stats', TrafficStatisticsModelViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 