from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.viewsets import ModelViewSet
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Sum, Count
from datetime import datetime, timedelta
import hashlib

from .models import Domain, <PERSON>ras, VpdnUser, AuthRecord, Detail, OnlineRecord
from .serializers import (
    DomainSerializer, BrasSerializer, VpdnUserSerializer,
    AuthRecordSerializer, DetailSerializer, OnlineRecordSerializer,
    BillQuerySerializer, TraceQuerySerializer
)


class DomainViewSet(ModelViewSet):
    """域名管理视图集"""
    queryset = Domain.objects.all()
    serializer_class = DomainSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(vpdn_domain__icontains=search) |
                Q(vpdn_name__icontains=search) |
                Q(description__icontains=search)
            )
        return queryset


class BrasViewSet(ModelViewSet):
    """LNS设备管理视图集"""
    queryset = Bras.objects.all()
    serializer_class = BrasSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(bras_ip__icontains=search) |
                Q(bras_model__icontains=search) |
                Q(bras_vendor__icontains=search) |
                Q(bras_area__icontains=search)
            )
        return queryset


class VpdnUserViewSet(ModelViewSet):
    """VPDN用户管理视图集"""
    queryset = VpdnUser.objects.all()
    serializer_class = VpdnUserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_area__icontains=search)
            )

        # 过滤条件
        user_status = self.request.query_params.get('user_status')
        if user_status:
            queryset = queryset.filter(user_status=user_status)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """修改用户密码"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response({'error': '新密码不能为空'}, status=400)

        # 这里可以添加密码加密逻辑
        user.user_password = hashlib.md5(new_password.encode()).hexdigest()
        user.save()

        return Response({'message': '密码修改成功'})

    @action(detail=True, methods=['post'])
    def suspend(self, request, pk=None):
        """暂停用户"""
        user = self.get_object()
        user.user_status = 1  # 假设2表示暂停状态
        user.user_pause_datetime = datetime.now()
        user.save()
        return Response({'message': '用户已暂停'})

    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """恢复用户"""
        user = self.get_object()
        user.user_status = 0  # 假设1表示正常状态
        user.user_pause_datetime = None
        user.save()
        return Response({'message': '用户已恢复'})

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """注销用户"""
        user = self.get_object()
        user.user_status = 3  # 假设0表示注销状态
        user.save()
        return Response({'message': '用户已注销'})

    @action(detail=True, methods=['post'])
    def bind_ip(self, request, pk=None):
        """绑定IP地址"""
        user = self.get_object()
        bind_ip = request.data.get('bind_ip')

        if bind_ip:
            user.user_bind_ip = bind_ip
            user.save()

        return Response({'message': 'IP绑定成功'})


class AuthRecordViewSet(ModelViewSet):
    """认证记录查询视图集"""
    queryset = AuthRecord.objects.all()
    serializer_class = AuthRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-auth_date')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 过滤条件
        auth_result = self.request.query_params.get('auth_result')
        if auth_result:
            queryset = queryset.filter(auth_result_code=auth_result)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """认证统计"""
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            queryset = queryset.filter(auth_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(auth_date__lte=end_date)

        stats = queryset.aggregate(
            total_count=Count('id'),
            success_count=Count('id', filter=Q(auth_result_code=0)),  # 假设0表示成功
            failed_count=Count('id', filter=~Q(auth_result_code=0))   # 非0表示失败
        )

        stats['success_rate'] = (
            stats['success_count'] / stats['total_count'] * 100
            if stats['total_count'] > 0 else 0
        )

        return Response(stats)


class DetailViewSet(ModelViewSet):
    """清单查询视图集"""
    queryset = Detail.objects.all()
    serializer_class = DetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 过滤条件
        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=False, methods=['post'])
    def bill_query(self, request):
        """账单查询"""
        serializer = BillQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data
        year = data['year']
        month = data['month']
        username = data.get('username')
        domain = data.get('domain')

        # 计算月份的开始和结束时间
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)

        queryset = Detail.objects.filter(
            online_time__gte=start_date,
            online_time__lt=end_date
        )

        if username:
            queryset = queryset.filter(user_name=username)
        if domain:
            queryset = queryset.filter(user_domain=domain)

        # 按用户聚合统计
        bills = queryset.values('user_name', 'user_domain').annotate(
            total_duration=Sum('duration'),
            total_ipv4_input=Sum('user_ipv4_inoctets'),
            total_ipv4_output=Sum('user_ipv4_outoctets'),
            total_ipv6_input=Sum('user_ipv6_inoctets'),
            total_ipv6_output=Sum('user_ipv6_outoctets'),
            session_count=Count('id')
        )

        # 转换单位
        for bill in bills:
            bill['total_duration_hours'] = (
                round(bill['total_duration'] / 3600, 2)
                if bill['total_duration'] else 0
            )
            bill['total_ipv4_input_mb'] = (
                round((bill['total_ipv4_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv4_output_mb'] = (
                round((bill['total_ipv4_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_input_mb'] = (
                round((bill['total_ipv6_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_output_mb'] = (
                round((bill['total_ipv6_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_traffic_mb'] = (
                bill['total_ipv4_input_mb'] + bill['total_ipv4_output_mb'] +
                bill['total_ipv6_input_mb'] + bill['total_ipv6_output_mb']
            )
            bill['account_month'] = f"{year}-{month:02d}"

        return Response(list(bills))


class OnlineRecordViewSet(ModelViewSet):
    """在线查询视图集"""
    queryset = OnlineRecord.objects.all()
    serializer_class = OnlineRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(user_framedipv6__icontains=search) |
                Q(mac__icontains=search)
            )

        # 过滤条件
        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area=user_area)

        return queryset

    @action(detail=False, methods=['post'])
    def trace_query(self, request):
        """溯源查询"""
        serializer = TraceQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data

        # 构建查询条件
        online_q = Q()
        detail_q = Q()

        # 基础查询条件
        if data.get('username'):
            online_q &= Q(user_name=data['username'])
            detail_q &= Q(user_name=data['username'])

        if data.get('ip_address'):
            online_q &= Q(user_framedip=data['ip_address'])
            detail_q &= Q(user_framedip=data['ip_address'])

        if data.get('ipv6_address'):
            online_q &= Q(user_framedipv6=data['ipv6_address'])
            detail_q &= Q(user_framedipv6=data['ipv6_address'])

        if data.get('mac_address'):
            online_q &= Q(mac=data['mac_address'])
            detail_q &= Q(mac=data['mac_address'])

        # 过滤条件
        if data.get('domain_filter'):
            online_q &= Q(user_domain=data['domain_filter'])
            detail_q &= Q(user_domain=data['domain_filter'])

        if data.get('lns_filter'):
            online_q &= Q(bras_ip=data['lns_filter'])
            detail_q &= Q(bras_ip=data['lns_filter'])

        # 时间条件
        if data.get('trace_time'):
            # 时间点溯源
            trace_time = data['trace_time']
            online_q &= Q(online_time__lte=trace_time)
            detail_q &= Q(online_time__lte=trace_time, offline_time__gte=trace_time)
        else:
            # 时间范围查询
            if data.get('start_time'):
                online_q &= Q(online_time__gte=data['start_time'])
                detail_q &= Q(online_time__gte=data['start_time'])

            if data.get('end_time'):
                online_q &= Q(online_time__lte=data['end_time'])
                detail_q &= Q(online_time__lte=data['end_time'])

        # 查询在线记录
        online_records = OnlineRecord.objects.filter(online_q)
        online_serializer = OnlineRecordSerializer(online_records, many=True)

        # 查询历史记录
        detail_records = Detail.objects.filter(detail_q)
        detail_serializer = DetailSerializer(detail_records, many=True)

        return Response({
            'online_records': online_serializer.data,
            'detail_records': detail_serializer.data,
            'total_online': online_records.count(),
            'total_detail': detail_records.count()
        })
