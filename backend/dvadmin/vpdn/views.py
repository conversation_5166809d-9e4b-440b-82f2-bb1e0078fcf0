from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.viewsets import ModelViewSet
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Sum, Count
from datetime import datetime, timedelta
import hashlib

from .models import Domain, <PERSON>ras, VpdnUser, AuthRecord, Detail, OnlineRecord
from .serializers import (
    DomainSerializer, BrasSerializer, VpdnUserSerializer,
    AuthRecordSerializer, DetailSerializer, OnlineRecordSerializer,
    BillQuerySerializer, TraceQuerySerializer, BatchOperationSerializer,
    ExportDataSerializer, ForceOfflineSerializer
)


class DomainViewSet(ModelViewSet):
    """域名管理视图集"""
    queryset = Domain.objects.all()
    serializer_class = DomainSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(vpdn_domain__icontains=search) |
                Q(vpdn_name__icontains=search) |
                Q(description__icontains=search)
            )
        return queryset


class BrasViewSet(ModelViewSet):
    """LNS设备管理视图集"""
    queryset = Bras.objects.all()
    serializer_class = BrasSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(bras_ip__icontains=search) |
                Q(bras_model__icontains=search) |
                Q(bras_vendor__icontains=search) |
                Q(bras_area__icontains=search)
            )
        return queryset


class VpdnUserViewSet(ModelViewSet):
    """VPDN用户管理视图集"""
    queryset = VpdnUser.objects.all()
    serializer_class = VpdnUserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_area__icontains=search)
            )

        # 过滤条件
        user_status = self.request.query_params.get('user_status')
        if user_status:
            queryset = queryset.filter(user_status=user_status)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=True, methods=['post'])
    def change_password(self, request, pk=None):
        """修改用户密码"""
        user = self.get_object()
        new_password = request.data.get('new_password')

        if not new_password:
            return Response({'error': '新密码不能为空'}, status=400)

        # 这里可以添加密码加密逻辑
        user.user_password = hashlib.md5(new_password.encode()).hexdigest()
        user.save()

        return Response({'message': '密码修改成功'})

    @action(detail=True, methods=['post'])
    def suspend(self, request, pk=None):
        """暂停用户"""
        user = self.get_object()
        user.user_status = 1  # 假设2表示暂停状态
        user.user_pause_datetime = datetime.now()
        user.save()
        return Response({'message': '用户已暂停'})

    @action(detail=True, methods=['post'])
    def resume(self, request, pk=None):
        """恢复用户"""
        user = self.get_object()
        user.user_status = 0  # 假设1表示正常状态
        user.user_pause_datetime = None
        user.save()
        return Response({'message': '用户已恢复'})

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """注销用户"""
        user = self.get_object()
        user.user_status = 3  # 假设0表示注销状态
        user.save()
        return Response({'message': '用户已注销'})

    @action(detail=True, methods=['post'])
    def bind_ip(self, request, pk=None):
        """绑定IP地址"""
        user = self.get_object()
        bind_ip = request.data.get('bind_ip')

        if bind_ip:
            user.user_bind_ip = bind_ip
            user.save()

        return Response({'message': 'IP绑定成功'})

    @action(detail=False, methods=['post'])
    def batch_operation(self, request):
        """批量用户操作"""
        user_ids = request.data.get('user_ids', [])
        operation = request.data.get('operation')  # suspend, resume, cancel

        if not user_ids:
            return Response({'error': '请选择要操作的用户'}, status=400)

        if operation not in ['suspend', 'resume', 'cancel']:
            return Response({'error': '无效的操作类型'}, status=400)

        users = VpdnUser.objects.filter(id__in=user_ids)

        if operation == 'suspend':
            users.update(user_status=1)
            message = f'成功暂停 {users.count()} 个用户'
        elif operation == 'resume':
            users.update(user_status=0)
            message = f'成功恢复 {users.count()} 个用户'
        elif operation == 'cancel':
            users.update(user_status=3)
            message = f'成功注销 {users.count()} 个用户'

        return Response({'message': message})

    @action(detail=False, methods=['get'])
    def user_statistics(self, request):
        """用户统计信息"""
        from django.db.models import Count

        stats = VpdnUser.objects.aggregate(
            total_users=Count('id'),
            active_users=Count('id', filter=Q(user_status=0)),
            suspended_users=Count('id', filter=Q(user_status=1)),
            cancelled_users=Count('id', filter=Q(user_status=3))
        )

        # 按域名统计
        domain_stats = VpdnUser.objects.values('user_domain').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        # 按区域统计
        area_stats = VpdnUser.objects.values('user_area').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        return Response({
            'user_stats': stats,
            'domain_stats': list(domain_stats),
            'area_stats': list(area_stats)
        })


class AuthRecordViewSet(ModelViewSet):
    """认证记录查询视图集"""
    queryset = AuthRecord.objects.all()
    serializer_class = AuthRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-auth_date')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 过滤条件
        auth_result = self.request.query_params.get('auth_result')
        if auth_result:
            queryset = queryset.filter(auth_result_code=auth_result)

        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """认证统计"""
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        queryset = self.get_queryset()

        if start_date:
            queryset = queryset.filter(auth_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(auth_date__lte=end_date)

        stats = queryset.aggregate(
            total_count=Count('id'),
            success_count=Count('id', filter=Q(auth_result_code=0)),  # 假设0表示成功
            failed_count=Count('id', filter=~Q(auth_result_code=0))   # 非0表示失败
        )

        stats['success_rate'] = (
            stats['success_count'] / stats['total_count'] * 100
            if stats['total_count'] > 0 else 0
        )

        return Response(stats)


class DetailViewSet(ModelViewSet):
    """清单查询视图集"""
    queryset = Detail.objects.all()
    serializer_class = DetailSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(bras_ip__icontains=search)
            )

        # 过滤条件
        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        return queryset

    @action(detail=False, methods=['post'])
    def bill_query(self, request):
        """账单查询"""
        serializer = BillQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data
        year = data['year']
        month = data['month']
        username = data.get('username')
        domain = data.get('domain')

        # 计算月份的开始和结束时间
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)

        queryset = Detail.objects.filter(
            online_time__gte=start_date,
            online_time__lt=end_date
        )

        if username:
            queryset = queryset.filter(user_name=username)
        if domain:
            queryset = queryset.filter(user_domain=domain)

        # 按用户聚合统计
        bills = queryset.values('user_name', 'user_domain').annotate(
            total_duration=Sum('duration'),
            total_ipv4_input=Sum('user_ipv4_inoctets'),
            total_ipv4_output=Sum('user_ipv4_outoctets'),
            total_ipv6_input=Sum('user_ipv6_inoctets'),
            total_ipv6_output=Sum('user_ipv6_outoctets'),
            session_count=Count('id')
        )

        # 转换单位
        for bill in bills:
            bill['total_duration_hours'] = (
                round(bill['total_duration'] / 3600, 2)
                if bill['total_duration'] else 0
            )
            bill['total_ipv4_input_mb'] = (
                round((bill['total_ipv4_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv4_output_mb'] = (
                round((bill['total_ipv4_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_input_mb'] = (
                round((bill['total_ipv6_input'] or 0) / (1024 * 1024), 2)
            )
            bill['total_ipv6_output_mb'] = (
                round((bill['total_ipv6_output'] or 0) / (1024 * 1024), 2)
            )
            bill['total_traffic_mb'] = (
                bill['total_ipv4_input_mb'] + bill['total_ipv4_output_mb'] +
                bill['total_ipv6_input_mb'] + bill['total_ipv6_output_mb']
            )
            bill['account_month'] = f"{year}-{month:02d}"

        return Response(list(bills))

    @action(detail=False, methods=['get'])
    def traffic_statistics(self, request):
        """流量统计"""
        # 按日期统计流量
        from django.db.models import Sum
        from django.db.models.functions import TruncDate

        daily_traffic = Detail.objects.annotate(
            date=TruncDate('online_time')
        ).values('date').annotate(
            total_input_octets=Sum('input_octets'),
            total_output_octets=Sum('output_octets'),
            total_sessions=Count('id')
        ).order_by('-date')[:30]

        # 按用户域名统计
        domain_traffic = Detail.objects.values('user_domain').annotate(
            total_input_octets=Sum('input_octets'),
            total_output_octets=Sum('output_octets'),
            total_sessions=Count('id')
        ).order_by('-total_input_octets')[:10]

        return Response({
            'daily_traffic': list(daily_traffic),
            'domain_traffic': list(domain_traffic)
        })

    @action(detail=False, methods=['post'])
    def export_data(self, request):
        """导出数据"""
        start_date = request.data.get('start_date')
        end_date = request.data.get('end_date')
        user_name = request.data.get('user_name')

        queryset = self.get_queryset()

        if start_date:
            queryset = queryset.filter(online_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(online_time__lte=end_date)
        if user_name:
            queryset = queryset.filter(user_name__icontains=user_name)

        # 限制导出数量
        queryset = queryset[:10000]

        data = []
        for record in queryset:
            data.append({
                'user_name': record.user_name,
                'user_domain': record.user_domain,
                'online_time': record.online_time.strftime('%Y-%m-%d %H:%M:%S') if record.online_time else '',
                'offline_time': record.offline_time.strftime('%Y-%m-%d %H:%M:%S') if record.offline_time else '',
                'session_time': record.session_time,
                'input_octets_mb': round(record.input_octets / 1024 / 1024, 2) if record.input_octets else 0,
                'output_octets_mb': round(record.output_octets / 1024 / 1024, 2) if record.output_octets else 0,
                'nas_ip': record.nas_ip,
                'framed_ip': record.framed_ip
            })

        return Response({
            'data': data,
            'total': len(data),
            'message': f'导出 {len(data)} 条记录'
        })


class OnlineRecordViewSet(ModelViewSet):
    """在线查询视图集"""
    queryset = OnlineRecord.objects.all()
    serializer_class = OnlineRecordSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-online_time')
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(user_name__icontains=search) |
                Q(user_domain__icontains=search) |
                Q(user_framedip__icontains=search) |
                Q(user_framedipv6__icontains=search) |
                Q(mac__icontains=search)
            )

        # 过滤条件
        user_domain = self.request.query_params.get('user_domain')
        if user_domain:
            queryset = queryset.filter(user_domain=user_domain)

        user_area = self.request.query_params.get('user_area')
        if user_area:
            queryset = queryset.filter(user_area=user_area)

        return queryset

    @action(detail=False, methods=['post'])
    def trace_query(self, request):
        """溯源查询"""
        serializer = TraceQuerySerializer(data=request.data)
        if not serializer.is_valid():
            return Response({'error': '参数错误', 'details': serializer.errors}, status=400)

        data = serializer.validated_data

        # 构建查询条件
        online_q = Q()
        detail_q = Q()

        # 基础查询条件
        if data.get('username'):
            online_q &= Q(user_name=data['username'])
            detail_q &= Q(user_name=data['username'])

        if data.get('ip_address'):
            online_q &= Q(user_framedip=data['ip_address'])
            detail_q &= Q(user_framedip=data['ip_address'])

        if data.get('ipv6_address'):
            online_q &= Q(user_framedipv6=data['ipv6_address'])
            detail_q &= Q(user_framedipv6=data['ipv6_address'])

        if data.get('mac_address'):
            online_q &= Q(mac=data['mac_address'])
            detail_q &= Q(mac=data['mac_address'])

        # 过滤条件
        if data.get('domain_filter'):
            online_q &= Q(user_domain=data['domain_filter'])
            detail_q &= Q(user_domain=data['domain_filter'])

        if data.get('lns_filter'):
            online_q &= Q(bras_ip=data['lns_filter'])
            detail_q &= Q(bras_ip=data['lns_filter'])

        # 时间条件
        if data.get('trace_time'):
            # 时间点溯源
            trace_time = data['trace_time']
            online_q &= Q(online_time__lte=trace_time)
            detail_q &= Q(online_time__lte=trace_time, offline_time__gte=trace_time)
        else:
            # 时间范围查询
            if data.get('start_time'):
                online_q &= Q(online_time__gte=data['start_time'])
                detail_q &= Q(online_time__gte=data['start_time'])

            if data.get('end_time'):
                online_q &= Q(online_time__lte=data['end_time'])
                detail_q &= Q(online_time__lte=data['end_time'])

        # 查询在线记录
        online_records = OnlineRecord.objects.filter(online_q)
        online_serializer = OnlineRecordSerializer(online_records, many=True)

        # 查询历史记录
        detail_records = Detail.objects.filter(detail_q)
        detail_serializer = DetailSerializer(detail_records, many=True)

        return Response({
            'online_records': online_serializer.data,
            'detail_records': detail_serializer.data,
            'total_online': online_records.count(),
            'total_detail': detail_records.count()
        })

    @action(detail=False, methods=['get'])
    def real_time_monitor(self, request):
        """实时监控"""
        # 在线用户统计
        online_stats = OnlineRecord.objects.aggregate(
            total_online=Count('session_id'),
            total_ipv4_users=Count('session_id', filter=Q(user_framedip__isnull=False)),
            total_ipv6_users=Count('session_id', filter=Q(user_framedipv6__isnull=False))
        )

        # 按BRAS设备统计在线用户
        bras_stats = OnlineRecord.objects.values('bras_ip').annotate(
            online_count=Count('session_id')
        ).order_by('-online_count')

        # 按域名统计在线用户
        domain_stats = OnlineRecord.objects.values('user_domain').annotate(
            online_count=Count('session_id')
        ).order_by('-online_count')[:10]

        # 最近上线的用户
        recent_online = OnlineRecord.objects.order_by('-online_time')[:10]
        recent_data = []
        for record in recent_online:
            recent_data.append({
                'user_name': record.user_name,
                'user_domain': record.user_domain,
                'online_time': record.online_time.strftime('%Y-%m-%d %H:%M:%S') if record.online_time else '',
                'nas_ip': record.bras_ip,
                'framed_ip': record.user_framedip
            })

        return Response({
            'online_stats': online_stats,
            'bras_stats': list(bras_stats),
            'domain_stats': list(domain_stats),
            'recent_online': recent_data
        })

    @action(detail=False, methods=['post'])
    def force_offline(self, request):
        """强制下线用户"""
        user_name = request.data.get('user_name')
        user_domain = request.data.get('user_domain')

        if not user_name or not user_domain:
            return Response({'error': '用户名和域名不能为空'}, status=400)

        # 查找在线记录
        online_records = OnlineRecord.objects.filter(
            user_name=user_name,
            user_domain=user_domain
        )

        if not online_records.exists():
            return Response({'error': '用户不在线'}, status=404)

        # 这里应该调用RADIUS服务器的强制下线接口
        # 暂时只是删除在线记录作为演示
        count = online_records.count()
        online_records.delete()

        return Response({
            'message': f'成功强制下线用户 {user_name}@{user_domain}，共 {count} 个会话'
        })
