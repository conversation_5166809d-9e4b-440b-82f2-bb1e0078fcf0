from django.db import models
from dvadmin.utils.models import CoreModel


class Domain(models.Model):
    """域名管理表 - 对应现有vpdn表"""
    vpdn_domain = models.CharField(max_length=48, primary_key=True, verbose_name="域名", help_text="VPDN域名")
    vpdn_name = models.CharField(max_length=255, null=True, blank=True, verbose_name="VPDN名称", help_text="VPDN名称")
    vpdn_areano = models.CharField(max_length=20, null=True, blank=True, verbose_name="区域编号", help_text="区域编号")
    contect_man_name = models.CharField(max_length=55, null=True, blank=True, verbose_name="联系人姓名", help_text="联系人姓名")
    contect_man_phone = models.CharField(max_length=11, null=True, blank=True, verbose_name="联系人电话", help_text="联系人电话")
    contect_man_addess = models.CharField(max_length=255, null=True, blank=True, verbose_name="联系人地址", help_text="联系人地址")
    contect_man_email = models.Char<PERSON>ield(max_length=55, null=True, blank=True, verbose_name="联系人邮箱", help_text="联系人邮箱")
    description = models.CharField(max_length=255, null=True, blank=True, verbose_name="描述", help_text="描述")

    class Meta:
        db_table = "vpdn"
        verbose_name = "域名管理"
        verbose_name_plural = verbose_name
        managed = False


class Bras(models.Model):
    """LNS设备管理表 - 对应现有bras表"""
    bras_ip = models.CharField(max_length=15, primary_key=True, verbose_name="设备IP地址", help_text="BRAS设备IP地址")
    bras_secret = models.CharField(max_length=15, verbose_name="共享密钥", help_text="RADIUS共享密钥")
    bras_model = models.CharField(max_length=32, verbose_name="设备型号", help_text="设备型号")
    bras_vendor = models.CharField(max_length=32, verbose_name="设备厂商", help_text="设备厂商")
    bras_area = models.CharField(max_length=20, verbose_name="设备区域", help_text="设备所在区域")
    bras_description = models.CharField(max_length=1000, null=True, blank=True, verbose_name="设备描述", help_text="设备描述")

    class Meta:
        db_table = "bras"
        verbose_name = "LNS设备管理"
        verbose_name_plural = verbose_name
        managed = False


class VpdnUser(models.Model):
    """VPDN用户表 - 对应现有user表"""
    id = models.AutoField(primary_key=True, verbose_name="主键", help_text="主键ID")
    user_name = models.CharField(max_length=40, verbose_name="用户名", help_text="VPDN用户名")
    user_domain = models.CharField(max_length=48, verbose_name="用户域名", help_text="用户域名")
    user_business_type = models.IntegerField(verbose_name="业务类型", help_text="业务类型")
    user_password = models.CharField(max_length=64, null=True, blank=True, verbose_name="密码", help_text="用户密码")
    user_password_type = models.CharField(max_length=64, null=True, blank=True, verbose_name="密码类型", help_text="密码类型")
    user_area = models.CharField(max_length=20, verbose_name="用户区域", help_text="用户区域")
    user_line_bind_type = models.IntegerField(null=True, blank=True, verbose_name="线路绑定类型", help_text="线路绑定类型")
    user_line_bind_info = models.CharField(max_length=350, null=True, blank=True, verbose_name="线路绑定信息", help_text="线路绑定信息")
    user_bind_nas = models.CharField(max_length=15, null=True, blank=True, verbose_name="绑定NAS", help_text="绑定NAS")
    user_bind_ip = models.CharField(max_length=64, null=True, blank=True, verbose_name="绑定IP", help_text="绑定IP地址")
    user_allow_onlinenums = models.IntegerField(null=True, blank=True, verbose_name="允许在线数", help_text="允许同时在线数")
    user_status = models.IntegerField(verbose_name="用户状态", help_text="用户状态")
    user_pause_datetime = models.DateTimeField(null=True, blank=True, verbose_name="暂停时间", help_text="暂停时间")
    user_open_datetime = models.DateTimeField(null=True, blank=True, verbose_name="开户时间", help_text="开户时间")
    user_open_operator = models.CharField(max_length=100, null=True, blank=True, verbose_name="开户操作员", help_text="开户操作员")
    user_expire_datetime = models.DateTimeField(null=True, blank=True, verbose_name="失效时间", help_text="失效时间")
    user_modify_datetime = models.DateTimeField(null=True, blank=True, verbose_name="修改时间", help_text="修改时间")
    user_modify_operator = models.CharField(max_length=100, null=True, blank=True, verbose_name="修改操作员", help_text="修改操作员")
    user_down_bandwidth = models.DecimalField(max_digits=10, decimal_places=0, null=True, blank=True, verbose_name="下行带宽", help_text="下行带宽")
    user_up_bandwidth = models.DecimalField(max_digits=10, decimal_places=0, null=True, blank=True, verbose_name="上行带宽", help_text="上行带宽")
    user_ip_type = models.IntegerField(null=True, blank=True, verbose_name="IP类型", help_text="IP类型")
    user_ipv6_prefix = models.CharField(max_length=45, null=True, blank=True, verbose_name="IPv6前缀", help_text="IPv6前缀")
    user_ipv6_interfaceid = models.CharField(max_length=20, null=True, blank=True, verbose_name="IPv6接口ID", help_text="IPv6接口ID")
    user_bindwidth_template_id = models.CharField(max_length=20, null=True, blank=True, verbose_name="带宽模板ID", help_text="带宽模板ID")
    user_product_type = models.IntegerField(null=True, blank=True, verbose_name="产品类型", help_text="产品类型")
    user_allow_start_time = models.CharField(max_length=8, null=True, blank=True, verbose_name="允许开始时间", help_text="允许开始时间")
    user_allow_stop_time = models.CharField(max_length=8, null=True, blank=True, verbose_name="允许结束时间", help_text="允许结束时间")
    user_primary_username = models.CharField(max_length=40, null=True, blank=True, verbose_name="主用户名", help_text="主用户名")
    user_session_timemout = models.IntegerField(null=True, blank=True, verbose_name="会话超时", help_text="会话超时")
    user_traffic_limit = models.BigIntegerField(null=True, blank=True, verbose_name="流量限制", help_text="流量限制")
    user_duration_limit = models.IntegerField(null=True, blank=True, verbose_name="时长限制", help_text="时长限制")
    user_traffic_used = models.BigIntegerField(null=True, blank=True, default=0, verbose_name="已用流量", help_text="已用流量")
    user_duration_used = models.IntegerField(null=True, blank=True, default=0, verbose_name="已用时长", help_text="已用时长")
    user_traffic_remain = models.BigIntegerField(null=True, blank=True, default=0, verbose_name="剩余流量", help_text="剩余流量")

    def __str__(self):
        return f"{self.user_name}@{self.user_domain}"

    class Meta:
        db_table = "user"
        verbose_name = "VPDN用户"
        verbose_name_plural = verbose_name
        managed = False  # 告诉Django这是一个非托管模型
        unique_together = [['user_name', 'user_domain', 'user_business_type']]


class AuthRecord(models.Model):
    """认证记录表 - 对应现有authrecord表"""
    id = models.BigAutoField(primary_key=True)
    user_name = models.CharField(max_length=40, verbose_name="用户名", help_text="认证用户名")
    user_domain = models.CharField(max_length=48, null=True, blank=True, verbose_name="用户域名", help_text="用户域名")
    user_business_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="业务类型", help_text="业务类型")
    auth_date = models.DateTimeField(verbose_name="认证时间", help_text="认证时间")
    auth_result_code = models.IntegerField(verbose_name="认证结果码", help_text="认证结果码")
    bras_ip = models.CharField(max_length=15, null=True, blank=True, verbose_name="BRAS IP", help_text="BRAS设备IP地址")
    bras_port = models.IntegerField(null=True, blank=True, verbose_name="BRAS端口", help_text="BRAS端口")
    bras_port_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="BRAS端口类型", help_text="BRAS端口类型")
    line_info = models.CharField(max_length=350, null=True, blank=True, verbose_name="线路信息", help_text="线路信息")
    mac = models.CharField(max_length=20, null=True, blank=True, verbose_name="MAC地址", help_text="MAC地址")
    client_type = models.IntegerField(null=True, blank=True, verbose_name="客户端类型", help_text="客户端类型")
    radius_server = models.CharField(max_length=20, null=True, blank=True, verbose_name="RADIUS服务器", help_text="RADIUS服务器")
    dail_user_name = models.CharField(max_length=40, null=True, blank=True, verbose_name="拨号用户名", help_text="拨号用户名")

    class Meta:
        db_table = "authrecord"
        verbose_name = "认证记录"
        verbose_name_plural = verbose_name
        managed = False


class Detail(models.Model):
    """清单表 - 对应现有detail表"""
    id = models.BigAutoField(primary_key=True)
    user_name = models.CharField(max_length=40, verbose_name="用户名", help_text="用户名")
    user_domain = models.CharField(max_length=48, null=True, blank=True, verbose_name="用户域名", help_text="用户域名")
    user_business_type = models.IntegerField(verbose_name="业务类型", help_text="业务类型")
    online_time = models.DateTimeField(verbose_name="上线时间", help_text="上线时间")
    offline_time = models.DateTimeField(verbose_name="下线时间", help_text="下线时间")
    duration = models.IntegerField(verbose_name="在线时长(秒)", help_text="在线时长(秒)")

    # NAT IP信息
    user_nat_framedip = models.CharField(max_length=15, null=True, blank=True, verbose_name="NAT分配IP", help_text="NAT分配IP")
    user_nat_beginport = models.IntegerField(null=True, blank=True, verbose_name="NAT起始端口", help_text="NAT起始端口")
    user_nat_endport = models.IntegerField(null=True, blank=True, verbose_name="NAT结束端口", help_text="NAT结束端口")

    # IP地址信息
    user_framedip = models.CharField(max_length=15, null=True, blank=True, verbose_name="用户IP", help_text="分配给用户的IP地址")
    user_framedipv6 = models.CharField(max_length=45, null=True, blank=True, verbose_name="用户IPv6", help_text="用户IPv6地址")
    user_delegated_ipv6prefix = models.CharField(max_length=45, null=True, blank=True, verbose_name="委派IPv6前缀", help_text="委派IPv6前缀")

    # IPv4流量信息
    user_ipv4_outoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4上行流量(字节)", help_text="IPv4上行流量(字节)")
    user_ipv4_inoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4下行流量(字节)", help_text="IPv4下行流量(字节)")
    user_ipv4_outpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4上行包数", help_text="IPv4上行包数")
    user_ipv4_inpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4下行包数", help_text="IPv4下行包数")

    # IPv6流量信息
    user_ipv6_outoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6上行流量(字节)", help_text="IPv6上行流量(字节)")
    user_ipv6_inoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6下行流量(字节)", help_text="IPv6下行流量(字节)")
    user_ipv6_outpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6上行包数", help_text="IPv6上行包数")
    user_ipv6_inpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6下行包数", help_text="IPv6下行包数")

    # 其他信息
    line_info = models.CharField(max_length=350, null=True, blank=True, verbose_name="线路信息", help_text="线路信息")
    mac = models.CharField(max_length=20, null=True, blank=True, verbose_name="MAC地址", help_text="MAC地址")
    bras_ip = models.CharField(max_length=15, verbose_name="BRAS IP", help_text="BRAS设备IP地址")
    bras_port = models.IntegerField(null=True, blank=True, verbose_name="BRAS端口", help_text="BRAS端口")
    bras_port_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="BRAS端口类型", help_text="BRAS端口类型")
    session_id = models.CharField(max_length=64, verbose_name="会话ID", help_text="会话ID")
    user_area = models.CharField(max_length=20, null=True, blank=True, verbose_name="用户区域", help_text="用户区域")
    bras_area = models.CharField(max_length=20, null=True, blank=True, verbose_name="BRAS区域", help_text="BRAS区域")
    client_type = models.IntegerField(null=True, blank=True, verbose_name="客户端类型", help_text="客户端类型")
    packet_process_time = models.DateTimeField(verbose_name="包处理时间", help_text="包处理时间")
    dail_user_name = models.CharField(max_length=40, null=True, blank=True, verbose_name="拨号用户名", help_text="拨号用户名")
    down_reason = models.CharField(max_length=40, null=True, blank=True, verbose_name="下线原因", help_text="下线原因")
    radius_server = models.CharField(max_length=20, null=True, blank=True, verbose_name="RADIUS服务器", help_text="RADIUS服务器")

    class Meta:
        db_table = "detail"
        verbose_name = "清单记录"
        verbose_name_plural = verbose_name
        managed = False


class OnlineRecord(models.Model):
    """在线记录表 - 对应现有onlinerecord表"""
    user_name = models.CharField(max_length=40, verbose_name="用户名", help_text="用户名")
    user_domain = models.CharField(max_length=48, null=True, blank=True, verbose_name="用户域名", help_text="用户域名")
    user_business_type = models.IntegerField(verbose_name="业务类型", help_text="业务类型")
    online_time = models.DateTimeField(verbose_name="上线时间", help_text="上线时间")

    # 线路和MAC信息
    line_info = models.CharField(max_length=350, null=True, blank=True, verbose_name="线路信息", help_text="线路信息")
    mac = models.CharField(max_length=20, null=True, blank=True, verbose_name="MAC地址", help_text="MAC地址")

    # BRAS信息
    bras_ip = models.CharField(max_length=15, verbose_name="BRAS IP", help_text="BRAS设备IP地址")
    bras_port = models.IntegerField(null=True, blank=True, verbose_name="BRAS端口", help_text="BRAS端口")
    bras_port_type = models.CharField(max_length=20, null=True, blank=True, verbose_name="BRAS端口类型", help_text="BRAS端口类型")

    # 会话信息
    session_id = models.CharField(max_length=64, primary_key=True, verbose_name="会话ID", help_text="会话ID")

    # 区域信息
    user_area = models.CharField(max_length=20, null=True, blank=True, verbose_name="用户区域", help_text="用户区域")
    bras_area = models.CharField(max_length=20, null=True, blank=True, verbose_name="BRAS区域", help_text="BRAS区域")

    # 其他信息
    client_type = models.IntegerField(null=True, blank=True, verbose_name="客户端类型", help_text="客户端类型")
    packet_process_time = models.DateTimeField(verbose_name="包处理时间", help_text="包处理时间")
    dail_user_name = models.CharField(max_length=40, null=True, blank=True, verbose_name="拨号用户名", help_text="拨号用户名")

    # NAT IP信息
    user_nat_framedip = models.CharField(max_length=15, null=True, blank=True, verbose_name="NAT分配IP", help_text="NAT分配IP")
    user_nat_beginport = models.IntegerField(null=True, blank=True, verbose_name="NAT起始端口", help_text="NAT起始端口")
    user_nat_endport = models.IntegerField(null=True, blank=True, verbose_name="NAT结束端口", help_text="NAT结束端口")

    # IP地址信息
    user_framedip = models.CharField(max_length=15, null=True, blank=True, verbose_name="用户IP", help_text="分配给用户的IP地址")
    user_framedipv6 = models.CharField(max_length=45, null=True, blank=True, verbose_name="用户IPv6", help_text="用户IPv6地址")
    user_delegated_ipv6prefix = models.CharField(max_length=45, null=True, blank=True, verbose_name="委派IPv6前缀", help_text="委派IPv6前缀")

    # 当前流量信息
    user_ipv4_outoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4上行流量(字节)", help_text="IPv4上行流量(字节)")
    user_ipv4_inoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4下行流量(字节)", help_text="IPv4下行流量(字节)")
    user_ipv4_outpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4上行包数", help_text="IPv4上行包数")
    user_ipv4_inpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv4下行包数", help_text="IPv4下行包数")
    user_ipv6_outoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6上行流量(字节)", help_text="IPv6上行流量(字节)")
    user_ipv6_inoctets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6下行流量(字节)", help_text="IPv6下行流量(字节)")
    user_ipv6_outpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6上行包数", help_text="IPv6上行包数")
    user_ipv6_inpackets = models.BigIntegerField(null=True, blank=True, verbose_name="IPv6下行包数", help_text="IPv6下行包数")

    # 包类型和RADIUS服务器
    packet_type = models.CharField(max_length=15, verbose_name="包类型", help_text="包类型")
    radius_server = models.CharField(max_length=20, null=True, blank=True, verbose_name="RADIUS服务器", help_text="RADIUS服务器")

    class Meta:
        db_table = "onlinerecord"
        verbose_name = "在线记录"
        verbose_name_plural = verbose_name
        managed = False
