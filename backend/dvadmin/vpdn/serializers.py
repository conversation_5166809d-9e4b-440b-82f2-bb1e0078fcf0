from rest_framework import serializers
from .models import Domain, Bras, VpdnUser, AuthRecord, Detail, OnlineRecord


class DomainSerializer(serializers.ModelSerializer):
    """域名序列化器"""

    class Meta:
        model = Domain
        fields = '__all__'


class BrasSerializer(serializers.ModelSerializer):
    """LNS设备序列化器"""

    class Meta:
        model = Bras
        fields = '__all__'


class VpdnUserSerializer(serializers.ModelSerializer):
    """VPDN用户序列化器"""
    id = serializers.SerializerMethodField()

    class Meta:
        model = VpdnUser
        fields = '__all__'
        extra_kwargs = {
            'user_password': {'write_only': True}  # 密码字段只写不读
        }

    def get_id(self, obj):
        """获取复合主键ID"""
        return f"{obj.user_name}#{obj.user_domain}#{obj.user_business_type}"


class VpdnUserCreateSerializer(serializers.ModelSerializer):
    """VPDN用户创建序列化器"""

    class Meta:
        model = VpdnUser
        fields = '__all__'
        extra_kwargs = {
            'user_password': {'write_only': True}
        }


class VpdnUserUpdateSerializer(serializers.ModelSerializer):
    """VPDN用户更新序列化器"""

    class Meta:
        model = VpdnUser
        fields = '__all__'
        extra_kwargs = {
            'user_password': {'write_only': True, 'required': False}
        }


class AuthRecordSerializer(serializers.ModelSerializer):
    """认证记录序列化器"""

    class Meta:
        model = AuthRecord
        fields = '__all__'


class DetailSerializer(serializers.ModelSerializer):
    """清单记录序列化器"""
    duration_hours = serializers.SerializerMethodField()
    ipv4_input_mb = serializers.SerializerMethodField()
    ipv4_output_mb = serializers.SerializerMethodField()
    ipv6_input_mb = serializers.SerializerMethodField()
    ipv6_output_mb = serializers.SerializerMethodField()
    total_mb = serializers.SerializerMethodField()

    class Meta:
        model = Detail
        fields = '__all__'

    def get_duration_hours(self, obj):
        """获取时长（小时）"""
        if obj.duration:
            return round(obj.duration / 3600, 2)
        return 0

    def get_ipv4_input_mb(self, obj):
        """获取IPv4上行流量（MB）"""
        if obj.user_ipv4_inoctets:
            return round(obj.user_ipv4_inoctets / (1024 * 1024), 2)
        return 0

    def get_ipv4_output_mb(self, obj):
        """获取IPv4下行流量（MB）"""
        if obj.user_ipv4_outoctets:
            return round(obj.user_ipv4_outoctets / (1024 * 1024), 2)
        return 0

    def get_ipv6_input_mb(self, obj):
        """获取IPv6上行流量（MB）"""
        if obj.user_ipv6_inoctets:
            return round(obj.user_ipv6_inoctets / (1024 * 1024), 2)
        return 0

    def get_ipv6_output_mb(self, obj):
        """获取IPv6下行流量（MB）"""
        if obj.user_ipv6_outoctets:
            return round(obj.user_ipv6_outoctets / (1024 * 1024), 2)
        return 0

    def get_total_mb(self, obj):
        """获取总流量（MB）"""
        ipv4_total = (obj.user_ipv4_inoctets or 0) + (obj.user_ipv4_outoctets or 0)
        ipv6_total = (obj.user_ipv6_inoctets or 0) + (obj.user_ipv6_outoctets or 0)
        return round((ipv4_total + ipv6_total) / (1024 * 1024), 2)


class OnlineRecordSerializer(serializers.ModelSerializer):
    """在线记录序列化器"""
    online_duration = serializers.SerializerMethodField()
    ipv4_input_mb = serializers.SerializerMethodField()
    ipv4_output_mb = serializers.SerializerMethodField()
    ipv6_input_mb = serializers.SerializerMethodField()
    ipv6_output_mb = serializers.SerializerMethodField()
    total_mb = serializers.SerializerMethodField()

    class Meta:
        model = OnlineRecord
        fields = '__all__'

    def get_online_duration(self, obj):
        """获取在线时长（秒）"""
        from datetime import datetime
        if obj.online_time:
            return int((datetime.now() - obj.online_time).total_seconds())
        return 0

    def get_ipv4_input_mb(self, obj):
        """获取IPv4上行流量（MB）"""
        if obj.user_ipv4_inoctets:
            return round(obj.user_ipv4_inoctets / (1024 * 1024), 2)
        return 0

    def get_ipv4_output_mb(self, obj):
        """获取IPv4下行流量（MB）"""
        if obj.user_ipv4_outoctets:
            return round(obj.user_ipv4_outoctets / (1024 * 1024), 2)
        return 0

    def get_ipv6_input_mb(self, obj):
        """获取IPv6上行流量（MB）"""
        if obj.user_ipv6_inoctets:
            return round(obj.user_ipv6_inoctets / (1024 * 1024), 2)
        return 0

    def get_ipv6_output_mb(self, obj):
        """获取IPv6下行流量（MB）"""
        if obj.user_ipv6_outoctets:
            return round(obj.user_ipv6_outoctets / (1024 * 1024), 2)
        return 0

    def get_total_mb(self, obj):
        """获取总流量（MB）"""
        ipv4_total = (obj.user_ipv4_inoctets or 0) + (obj.user_ipv4_outoctets or 0)
        ipv6_total = (obj.user_ipv6_inoctets or 0) + (obj.user_ipv6_outoctets or 0)
        return round((ipv4_total + ipv6_total) / (1024 * 1024), 2)


class BillQuerySerializer(serializers.Serializer):
    """账单查询序列化器"""
    username = serializers.CharField(required=False, help_text="用户名")
    domain = serializers.CharField(required=False, help_text="域名")
    year = serializers.IntegerField(required=True, help_text="年份")
    month = serializers.IntegerField(required=True, help_text="月份")


class TraceQuerySerializer(serializers.Serializer):
    """溯源查询序列化器"""
    # 基础查询条件
    username = serializers.CharField(required=False, help_text="用户名")
    ip_address = serializers.IPAddressField(required=False, help_text="IP地址")
    ipv6_address = serializers.IPAddressField(protocol='IPv6', required=False, help_text="IPv6地址")
    mac_address = serializers.CharField(required=False, help_text="MAC地址")
    
    # 时间条件
    start_time = serializers.DateTimeField(required=False, help_text="开始时间")
    end_time = serializers.DateTimeField(required=False, help_text="结束时间")
    trace_time = serializers.DateTimeField(required=False, help_text="溯源时间点")
    
    # 过滤条件
    domain_filter = serializers.CharField(required=False, help_text="域名过滤")
    location_filter = serializers.CharField(required=False, help_text="属地过滤")
    lns_filter = serializers.IPAddressField(required=False, help_text="LNS过滤")
    
    def validate(self, data):
        """验证查询条件"""
        # 至少需要一个查询条件
        query_fields = ['username', 'ip_address', 'ipv6_address', 'mac_address']
        if not any(data.get(field) for field in query_fields):
            raise serializers.ValidationError("至少需要提供一个查询条件：用户名、IP地址、IPv6地址或MAC地址")
        
        # 如果提供了溯源时间点，需要IP或IPv6地址
        if data.get('trace_time'):
            if not (data.get('ip_address') or data.get('ipv6_address')):
                raise serializers.ValidationError("使用时间点溯源时，必须提供IP地址或IPv6地址")
        
        return data
