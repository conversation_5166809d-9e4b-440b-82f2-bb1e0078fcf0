from django.db.models import Sum
from django.utils import timezone
from datetime import timedelta
from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.json_response import DetailResponse
from dvadmin.tss.models import Device, TrafficStatistics
from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from dvadmin.system.models import Area

class HomeViewSet(ViewSet):
    """
    首页统计数据
    """
    
    def list(self, request):
        """获取首页统计数据"""
        try:
            # 获取终端总数量和总流量数据
            device_count = Device.objects.count()
            
            traffic_stats = TrafficStatistics.objects.filter(
                stat_level='province_day_total'
            ).aggregate(
                total_upload=Sum('upload_traffic'),
                total_download=Sum('download_traffic')
            )
            
            total_upload = float(traffic_stats['total_upload'] or 0)
            total_download = float(traffic_stats['total_download'] or 0)
            total_traffic = total_upload + total_download
            
            total_upload = round(total_upload / 1024, 2)
            total_download = round(total_download / 1024, 2)
            total_traffic = round(total_traffic / 1024, 2)

            # 获取最近10天的固网和移动网流量数据
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=9)
            
            daily_stats = TrafficStatistics.objects.filter(
                stat_level__in=['province_day_fixed', 'province_day_mobile'],
                stat_time__range=[start_date, end_date]
            ).order_by('stat_time')

            dates = []
            fixed_data = []
            mobile_data = []
            
            # 按日期分组数据
            date_data = {}
            for stat in daily_stats:
                date_str = stat.stat_time.strftime('%m-%d')
                if date_str not in date_data:
                    date_data[date_str] = {'fixed': 0, 'mobile': 0}
                
                # 转换为GB并累加上传和下载流量
                total = round((stat.upload_traffic + stat.download_traffic) / 1024, 2)
                if 'fixed' in stat.stat_level:
                    date_data[date_str]['fixed'] = total
                else:
                    date_data[date_str]['mobile'] = total

            # 填充数据
            for date_str, values in date_data.items():
                dates.append(date_str)
                fixed_data.append(values['fixed'])
                mobile_data.append(values['mobile'])

            # 获取各属地的固网、移动网和总流量数据
            area_stats = []
            areas = Area.objects.filter(pcode='53')  # 获取云南省下的所有地市
            
            for area in areas:
                # 获取该属地的固网流量
                fixed_stats = TrafficStatistics.objects.filter(
                    location=area.code,
                    stat_level='area_day_fixed'
                ).aggregate(
                    total=Sum('upload_traffic') + Sum('download_traffic')
                )
                
                # 获取该属地的移动网流量
                mobile_stats = TrafficStatistics.objects.filter(
                    location=area.code,
                    stat_level='area_day_mobile'
                ).aggregate(
                    total=Sum('upload_traffic') + Sum('download_traffic')
                )
                
                fixed_traffic = round(float(fixed_stats['total'] or 0) / 1024, 2)
                mobile_traffic = round(float(mobile_stats['total'] or 0) / 1024, 2)
                total_traffic = fixed_traffic + mobile_traffic
                
                area_stats.append({
                    'name': area.name,
                    'fixed_traffic': fixed_traffic,
                    'mobile_traffic': mobile_traffic,
                    'total_traffic': total_traffic
                })
            
            # 按总流量排序
            area_stats.sort(key=lambda x: x['total_traffic'], reverse=True)

            data = {
                'total_upload': total_upload,
                'total_download': total_download,
                'total_traffic': total_traffic,
                'device_count': device_count,
                'chart_data': {
                    'dates': dates,
                    'fixed_data': fixed_data,
                    'mobile_data': mobile_data
                },
                'area_stats': area_stats  # 添加属地统计数据
            }
            
            return DetailResponse(data=data)
        except Exception as e:
            return DetailResponse(data={}, msg=str(e)) 