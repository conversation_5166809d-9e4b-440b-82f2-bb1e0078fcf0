#!/usr/bin/env python
"""
简单的VPDN系统测试脚本
直接测试数据库连接和基本功能
"""
import requests
import json

def test_api_endpoints():
    """测试API端点"""
    base_url = "http://localhost:8000/api/vpdn"
    
    print("=== 测试VPDN API端点 ===")
    
    # 测试域名API
    print("\n1. 测试域名API:")
    try:
        response = requests.get(f"{base_url}/domains/", timeout=5)
        print(f"  域名列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  域名API测试失败: {e}")
    
    # 测试BRAS API
    print("\n2. 测试BRAS API:")
    try:
        response = requests.get(f"{base_url}/bras/", timeout=5)
        print(f"  BRAS列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  BRAS API测试失败: {e}")
    
    # 测试用户API
    print("\n3. 测试用户API:")
    try:
        response = requests.get(f"{base_url}/users/", timeout=5)
        print(f"  用户列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  用户API测试失败: {e}")
    
    # 测试认证记录API
    print("\n4. 测试认证记录API:")
    try:
        response = requests.get(f"{base_url}/auth-records/", timeout=5)
        print(f"  认证记录API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  认证记录API测试失败: {e}")
    
    # 测试在线记录API
    print("\n5. 测试在线记录API:")
    try:
        response = requests.get(f"{base_url}/online-records/", timeout=5)
        print(f"  在线记录API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  在线记录API测试失败: {e}")

def test_search_functionality():
    """测试搜索功能"""
    base_url = "http://localhost:8000/api/vpdn"
    
    print("\n=== 测试搜索功能 ===")
    
    # 测试用户搜索
    print("\n1. 测试用户搜索:")
    try:
        response = requests.get(f"{base_url}/users/?search=testuser", timeout=5)
        print(f"  用户搜索API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  搜索结果: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"  错误响应: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"  用户搜索测试失败: {e}")

def test_user_operations():
    """测试用户操作功能"""
    base_url = "http://localhost:8000/api/vpdn"
    
    print("\n=== 测试用户操作功能 ===")
    
    # 首先获取一个用户ID
    try:
        response = requests.get(f"{base_url}/users/?search=testuser", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data and len(data) > 0:
                user_id = data[0].get('id') or 1  # 假设第一个用户的ID
                
                # 测试修改密码
                print(f"\n1. 测试修改密码 (用户ID: {user_id}):")
                password_data = {"new_password": "newpassword123"}
                try:
                    response = requests.post(
                        f"{base_url}/users/{user_id}/change_password/",
                        json=password_data,
                        timeout=5
                    )
                    print(f"  修改密码API状态码: {response.status_code}")
                    print(f"  响应: {response.text}")
                except requests.exceptions.RequestException as e:
                    print(f"  修改密码测试失败: {e}")
                
                # 测试绑定IP
                print(f"\n2. 测试绑定IP (用户ID: {user_id}):")
                ip_data = {"bind_ip": "*************"}
                try:
                    response = requests.post(
                        f"{base_url}/users/{user_id}/bind_ip/",
                        json=ip_data,
                        timeout=5
                    )
                    print(f"  绑定IP API状态码: {response.status_code}")
                    print(f"  响应: {response.text}")
                except requests.exceptions.RequestException as e:
                    print(f"  绑定IP测试失败: {e}")
            else:
                print("  没有找到测试用户")
    except Exception as e:
        print(f"  获取用户信息失败: {e}")

def main():
    """主函数"""
    print("开始VPDN系统API测试...")
    print("注意: 请确保Django服务器正在运行 (python manage.py runserver)")
    
    try:
        test_api_endpoints()
        test_search_functionality()
        test_user_operations()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
