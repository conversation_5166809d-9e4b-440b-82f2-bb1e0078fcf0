#!/usr/bin/env python
"""
VPDN API测试脚本
"""
import os
import sys
import django
from django.conf import settings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from dvadmin.vpdn.models import Domain, Bras, VpdnUser, AuthRecord, Detail, OnlineRecord

def test_models():
    """测试模型功能"""
    print("=== 测试VPDN模型功能 ===")
    
    # 测试域名查询
    print("\n1. 测试域名查询:")
    domains = Domain.objects.all()[:5]
    for domain in domains:
        print(f"  域名: {domain.vpdn_domain}, 名称: {domain.vpdn_name}")
    
    # 测试BRAS设备查询
    print("\n2. 测试BRAS设备查询:")
    bras_devices = Bras.objects.all()[:5]
    for bras in bras_devices:
        print(f"  BRAS IP: {bras.bras_ip}, 型号: {bras.bras_model}, 厂商: {bras.bras_vendor}")
    
    # 测试用户查询
    print("\n3. 测试用户查询:")
    users = VpdnUser.objects.filter(user_name__startswith='testuser')
    for user in users:
        print(f"  用户: {user.user_name}@{user.user_domain}, 状态: {user.user_status}")
    
    # 测试认证记录查询
    print("\n4. 测试认证记录查询:")
    auth_records = AuthRecord.objects.filter(user_name__startswith='testuser')
    for record in auth_records:
        print(f"  用户: {record.user_name}, 认证时间: {record.auth_date}, 结果码: {record.auth_result_code}")
    
    # 测试在线记录查询
    print("\n5. 测试在线记录查询:")
    online_records = OnlineRecord.objects.filter(user_name__startswith='testuser')
    for record in online_records:
        print(f"  用户: {record.user_name}, 上线时间: {record.online_time}, IP: {record.user_framedip}")

def test_api_endpoints():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    from django.test import Client
    from django.urls import reverse
    
    client = Client()
    
    # 测试域名API
    print("\n1. 测试域名API:")
    try:
        response = client.get('/api/vpdn/domains/')
        print(f"  域名列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据条数: {len(data.get('results', data))}")
    except Exception as e:
        print(f"  域名API测试失败: {e}")
    
    # 测试BRAS API
    print("\n2. 测试BRAS API:")
    try:
        response = client.get('/api/vpdn/bras/')
        print(f"  BRAS列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据条数: {len(data.get('results', data))}")
    except Exception as e:
        print(f"  BRAS API测试失败: {e}")
    
    # 测试用户API
    print("\n3. 测试用户API:")
    try:
        response = client.get('/api/vpdn/users/')
        print(f"  用户列表API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  返回数据条数: {len(data.get('results', data))}")
    except Exception as e:
        print(f"  用户API测试失败: {e}")

def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    
    from django.test import Client
    
    client = Client()
    
    # 测试用户搜索
    print("\n1. 测试用户搜索:")
    try:
        response = client.get('/api/vpdn/users/?search=testuser')
        print(f"  用户搜索API状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"  搜索结果条数: {len(data.get('results', data))}")
    except Exception as e:
        print(f"  用户搜索测试失败: {e}")

def main():
    """主函数"""
    print("开始VPDN系统测试...")
    
    try:
        test_models()
        test_api_endpoints()
        test_search_functionality()
        print("\n=== 测试完成 ===")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
