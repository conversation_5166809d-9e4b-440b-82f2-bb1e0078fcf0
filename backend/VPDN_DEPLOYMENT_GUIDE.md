# VPDN管理系统部署指南

## 系统概述

VPDN管理系统是一个基于Django REST Framework开发的后端API系统，提供完整的VPDN用户管理、设备管理、认证记录查询等功能。

## 已完成的功能模块

### 1. 核心模块
- ✅ **域名管理模块** (`Domain`): 管理VPDN域名信息
- ✅ **LNS设备管理模块** (`Bras`): 管理BRAS/LNS设备信息
- ✅ **用户管理模块** (`VpdnUser`): 管理VPDN用户账户
- ✅ **认证记录模块** (`AuthRecord`): 查询用户认证记录
- ✅ **清单查询模块** (`Detail`): 查询用户上下线清单
- ✅ **在线查询模块** (`OnlineRecord`): 查询在线用户信息

### 2. API接口
- ✅ **RESTful API**: 完整的CRUD操作接口
- ✅ **搜索功能**: 支持关键词搜索
- ✅ **过滤功能**: 支持多条件过滤
- ✅ **用户操作**: 密码修改、暂停/恢复、注销等
- ✅ **账单查询**: 按月统计用户使用情况
- ✅ **溯源查询**: IP/IPv6/MAC地址溯源

### 3. 数据库适配
- ✅ **现有数据库兼容**: 适配现有的数据库表结构
- ✅ **数据模型**: 完整的Django模型定义
- ✅ **测试数据**: 已插入测试数据验证功能

## 文件结构

```
backend/dvadmin/vpdn/
├── __init__.py
├── apps.py                 # 应用配置
├── models.py              # 数据模型定义
├── serializers.py         # API序列化器
├── views.py               # API视图
├── urls.py                # URL路由配置
└── migrations/            # 数据库迁移文件
    └── __init__.py
```

## 部署步骤

### 1. 环境准备

确保已安装以下依赖：
```bash
# Python依赖 (requirements.txt中已包含)
Django==4.2.14
djangorestframework==3.15.2
mysqlclient==2.2.0
django-cors-headers==4.4.0
django-filter==24.2
```

### 2. 配置更新

#### 2.1 应用注册
在 `backend/application/settings.py` 中已添加：
```python
INSTALLED_APPS = [
    # ... 其他应用
    "dvadmin.vpdn"  # 新增VPDN应用
]
```

#### 2.2 URL配置
在 `backend/application/urls.py` 中已添加：
```python
urlpatterns = [
    # ... 其他URL
    path('api/vpdn/', include('dvadmin.vpdn.urls')),  # VPDN应用URL
]
```

### 3. 数据库配置

系统已适配现有数据库表结构，无需额外的数据库迁移。使用的表包括：
- `vpdn` - 域名表
- `bras` - LNS设备表  
- `user` - 用户表
- `authrecord` - 认证记录表
- `detail` - 清单表
- `onlinerecord` - 在线记录表

### 4. 启动服务

#### 4.1 开发环境
```bash
cd backend
python manage.py runserver 0.0.0.0:8000
```

#### 4.2 生产环境 (Docker)
```bash
# 使用现有的Docker配置
docker-compose up -d
```

### 5. API测试

#### 5.1 基础测试
```bash
# 测试域名API
curl http://localhost:8000/api/vpdn/domains/

# 测试BRAS设备API
curl http://localhost:8000/api/vpdn/bras/

# 测试用户API
curl http://localhost:8000/api/vpdn/users/
```

#### 5.2 搜索测试
```bash
# 搜索用户
curl "http://localhost:8000/api/vpdn/users/?search=testuser"

# 过滤域名
curl "http://localhost:8000/api/vpdn/users/?user_domain=test.vpdn.com"
```

## API端点总览

| 功能模块 | HTTP方法 | URL路径 | 描述 |
|---------|---------|---------|------|
| 域名管理 | GET/POST/PUT/DELETE | `/api/vpdn/domains/` | 域名CRUD操作 |
| 设备管理 | GET/POST/PUT/DELETE | `/api/vpdn/bras/` | BRAS设备CRUD操作 |
| 用户管理 | GET/POST/PUT/DELETE | `/api/vpdn/users/` | 用户CRUD操作 |
| 用户操作 | POST | `/api/vpdn/users/{id}/change_password/` | 修改密码 |
| 用户操作 | POST | `/api/vpdn/users/{id}/suspend/` | 暂停用户 |
| 用户操作 | POST | `/api/vpdn/users/{id}/resume/` | 恢复用户 |
| 用户操作 | POST | `/api/vpdn/users/{id}/cancel/` | 注销用户 |
| 用户操作 | POST | `/api/vpdn/users/{id}/bind_ip/` | 绑定IP |
| 认证记录 | GET | `/api/vpdn/auth-records/` | 认证记录查询 |
| 认证统计 | GET | `/api/vpdn/auth-records/statistics/` | 认证统计 |
| 清单查询 | GET | `/api/vpdn/details/` | 清单记录查询 |
| 账单查询 | POST | `/api/vpdn/details/bill_query/` | 账单查询 |
| 在线查询 | GET | `/api/vpdn/online-records/` | 在线记录查询 |
| 溯源查询 | POST | `/api/vpdn/online-records/trace_query/` | 溯源查询 |

## 测试数据

系统已插入以下测试数据：

### 域名数据
- `test.vpdn.com` - 测试VPDN域名
- `demo.vpdn.com` - 演示VPDN域名

### BRAS设备数据
- `***********` - 华为ME60设备
- `***********` - 思科ASR9000设备

### 用户数据
- `<EMAIL>` - 测试用户1
- `<EMAIL>` - 测试用户2

## 注意事项

1. **权限控制**: 当前版本使用Django的基础权限系统，生产环境建议加强权限控制
2. **数据验证**: 已实现基础的数据验证，可根据业务需求进一步完善
3. **日志记录**: 建议在生产环境中配置详细的日志记录
4. **性能优化**: 对于大数据量查询，建议添加数据库索引和查询优化
5. **安全性**: 生产环境中需要配置HTTPS和其他安全措施

## 后续开发建议

1. **前端界面**: 开发Web前端管理界面
2. **批量操作**: 实现用户批量导入/导出功能
3. **报表功能**: 增加更多统计报表
4. **监控告警**: 添加系统监控和告警功能
5. **API文档**: 集成Swagger自动生成API文档

## 技术支持

如有问题，请参考：
- API文档: `VPDN_API_Documentation.md`
- 测试脚本: `test_vpdn_api.py`
- Django官方文档: https://docs.djangoproject.com/
- DRF官方文档: https://www.django-rest-framework.org/
