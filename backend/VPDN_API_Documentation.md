# VPDN管理系统API文档

## 概述

本系统为VPDN（Virtual Private Dial-up Network）管理系统，提供域名管理、LNS设备管理、用户管理、认证记录查询、清单查询、在线查询等功能。

## 基础URL

```
http://your-domain/api/vpdn/
```

## API端点

### 1. 域名管理 (Domains)

#### 1.1 获取域名列表
- **URL**: `GET /api/vpdn/domains/`
- **描述**: 获取所有VPDN域名列表
- **查询参数**:
  - `search`: 搜索关键词（域名、名称、描述）
- **响应示例**:
```json
{
  "count": 100,
  "results": [
    {
      "vpdn_domain": "test.vpdn.com",
      "vpdn_name": "测试VPDN域名",
      "vpdn_areano": "0871",
      "contect_man_name": "张三",
      "contect_man_phone": "13800138000",
      "description": "测试用域名"
    }
  ]
}
```

#### 1.2 创建域名
- **URL**: `POST /api/vpdn/domains/`
- **请求体**:
```json
{
  "vpdn_domain": "new.vpdn.com",
  "vpdn_name": "新域名",
  "vpdn_areano": "0871",
  "contect_man_name": "联系人",
  "contect_man_phone": "13800138000",
  "description": "描述信息"
}
```

#### 1.3 更新域名
- **URL**: `PUT /api/vpdn/domains/{vpdn_domain}/`
- **URL**: `PATCH /api/vpdn/domains/{vpdn_domain}/`

#### 1.4 删除域名
- **URL**: `DELETE /api/vpdn/domains/{vpdn_domain}/`

### 2. LNS设备管理 (BRAS)

#### 2.1 获取设备列表
- **URL**: `GET /api/vpdn/bras/`
- **查询参数**:
  - `search`: 搜索关键词（IP、型号、厂商、区域）
- **响应示例**:
```json
{
  "count": 50,
  "results": [
    {
      "bras_ip": "***********",
      "bras_secret": "secret123",
      "bras_model": "ME60",
      "bras_vendor": "Huawei",
      "bras_area": "0871",
      "bras_description": "昆明核心BRAS设备"
    }
  ]
}
```

#### 2.2 创建设备
- **URL**: `POST /api/vpdn/bras/`

#### 2.3 更新设备
- **URL**: `PUT /api/vpdn/bras/{bras_ip}/`
- **URL**: `PATCH /api/vpdn/bras/{bras_ip}/`

#### 2.4 删除设备
- **URL**: `DELETE /api/vpdn/bras/{bras_ip}/`

### 3. VPDN用户管理 (Users)

#### 3.1 获取用户列表
- **URL**: `GET /api/vpdn/users/`
- **查询参数**:
  - `search`: 搜索关键词（用户名、域名、区域）
  - `user_status`: 用户状态过滤
  - `user_domain`: 域名过滤
- **响应示例**:
```json
{
  "count": 1000,
  "results": [
    {
      "user_name": "testuser1",
      "user_domain": "test.vpdn.com",
      "user_business_type": 1,
      "user_area": "0871",
      "user_status": 1,
      "user_open_datetime": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### 3.2 创建用户
- **URL**: `POST /api/vpdn/users/`

#### 3.3 用户操作

##### 3.3.1 修改密码
- **URL**: `POST /api/vpdn/users/{id}/change_password/`
- **请求体**:
```json
{
  "new_password": "newpassword123"
}
```

##### 3.3.2 暂停用户
- **URL**: `POST /api/vpdn/users/{id}/suspend/`

##### 3.3.3 恢复用户
- **URL**: `POST /api/vpdn/users/{id}/resume/`

##### 3.3.4 注销用户
- **URL**: `POST /api/vpdn/users/{id}/cancel/`

##### 3.3.5 绑定IP
- **URL**: `POST /api/vpdn/users/{id}/bind_ip/`
- **请求体**:
```json
{
  "bind_ip": "***********00"
}
```

### 4. 认证记录查询 (Auth Records)

#### 4.1 获取认证记录
- **URL**: `GET /api/vpdn/auth-records/`
- **查询参数**:
  - `search`: 搜索关键词
  - `auth_result`: 认证结果过滤
  - `user_domain`: 域名过滤

#### 4.2 认证统计
- **URL**: `GET /api/vpdn/auth-records/statistics/`
- **查询参数**:
  - `start_date`: 开始日期
  - `end_date`: 结束日期
- **响应示例**:
```json
{
  "total_count": 1000,
  "success_count": 950,
  "failed_count": 50,
  "success_rate": 95.0
}
```

### 5. 清单查询 (Details)

#### 5.1 获取清单记录
- **URL**: `GET /api/vpdn/details/`
- **查询参数**:
  - `search`: 搜索关键词
  - `user_domain`: 域名过滤

#### 5.2 账单查询
- **URL**: `POST /api/vpdn/details/bill_query/`
- **请求体**:
```json
{
  "year": 2024,
  "month": 1,
  "username": "testuser1",
  "domain": "test.vpdn.com"
}
```
- **响应示例**:
```json
[
  {
    "user_name": "testuser1",
    "user_domain": "test.vpdn.com",
    "total_duration": 3600,
    "total_duration_hours": 1.0,
    "total_ipv4_input_mb": 100.5,
    "total_ipv4_output_mb": 200.3,
    "total_traffic_mb": 300.8,
    "session_count": 5,
    "account_month": "2024-01"
  }
]
```

### 6. 在线查询 (Online Records)

#### 6.1 获取在线记录
- **URL**: `GET /api/vpdn/online-records/`
- **查询参数**:
  - `search`: 搜索关键词
  - `user_domain`: 域名过滤
  - `user_area`: 区域过滤

#### 6.2 溯源查询
- **URL**: `POST /api/vpdn/online-records/trace_query/`
- **请求体**:
```json
{
  "username": "testuser1",
  "ip_address": "***********00",
  "ipv6_address": "2001:db8::100",
  "mac_address": "00:11:22:33:44:55",
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-01-01T23:59:59Z",
  "trace_time": "2024-01-01T12:00:00Z",
  "domain_filter": "test.vpdn.com",
  "lns_filter": "***********"
}
```

## 数据库表结构

### 1. vpdn (域名表)
- `vpdn_domain` (PK): 域名
- `vpdn_name`: VPDN名称
- `vpdn_areano`: 区域编号
- `contect_man_name`: 联系人姓名
- `contect_man_phone`: 联系人电话
- `description`: 描述

### 2. bras (LNS设备表)
- `bras_ip` (PK): 设备IP地址
- `bras_secret`: 共享密钥
- `bras_model`: 设备型号
- `bras_vendor`: 设备厂商
- `bras_area`: 设备区域
- `bras_description`: 设备描述

### 3. user (用户表)
- `user_name`: 用户名
- `user_domain`: 用户域名
- `user_business_type`: 业务类型
- `user_password`: 密码
- `user_area`: 用户区域
- `user_status`: 用户状态
- `user_bind_ip`: 绑定IP
- 其他字段...

### 4. authrecord (认证记录表)
- `id` (PK): 主键
- `user_name`: 用户名
- `user_domain`: 用户域名
- `auth_date`: 认证时间
- `auth_result_code`: 认证结果码
- `bras_ip`: BRAS IP
- 其他字段...

### 5. detail (清单表)
- `id` (PK): 主键
- `user_name`: 用户名
- `user_domain`: 用户域名
- `online_time`: 上线时间
- `offline_time`: 下线时间
- `duration`: 在线时长
- `user_framedip`: 用户IP
- `user_framedipv6`: 用户IPv6
- 流量统计字段...

### 6. onlinerecord (在线记录表)
- `session_id` (PK): 会话ID
- `user_name`: 用户名
- `user_domain`: 用户域名
- `online_time`: 上线时间
- `user_framedip`: 用户IP
- `user_framedipv6`: 用户IPv6
- `mac`: MAC地址
- 其他字段...

## 功能特性

1. **域名管理**: 对VPDN域名进行统一管理
2. **LNS设备管理**: 管理不同型号的LNS设备及RADIUS配置
3. **用户业务受理**: 支持用户开户、修改、销户、暂停/恢复等操作
4. **认证记录查询**: 查询用户认证成功/失败记录
5. **清单查询**: 查询用户上下线清单和账单信息
6. **在线查询**: 查询当前在线用户信息
7. **溯源功能**: 支持IP、IPv6、MAC地址溯源查询

## 溯源能力

- 支持在线、离线统一溯源
- 支持私网IP溯源
- 支持IPv6溯源
- 支持MAC地址溯源
- 支持数据过滤（域名、属地、LNS）
