from channels.generic.websocket import AsyncWebsocketConsumer
import json
from .connection_manager import connection_manager
from ..utils.logger import logger

class ClientConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.serial_number = None

    async def connect(self):
        """处理WebSocket连接"""
        await self.accept()
        logger.info("New WebSocket connection established")

    async def disconnect(self, close_code):
        """处理WebSocket断开"""
        if self.serial_number:
            connection_manager.remove_connection(self.serial_number)
            logger.info(f"Client {self.serial_number} disconnected")

    async def receive(self, text_data):
        """处理接收到的消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'identity':
                # 处理身份标识消息
                self.serial_number = data.get('serial_number')
                if self.serial_number:
                    connection_manager.register_connection(self.serial_number, self)
                    logger.info(f"Client {self.serial_number} registered")
                    
                    # 发送确认消息
                    await self.send(text_data=json.dumps({
                        'type': 'identity_confirmed',
                        'status': 'success'
                    }))
            else:
                # 处理其他类型的消息
                await self.handle_message(data)

        except json.JSONDecodeError:
            logger.error("Invalid JSON message received")
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")

    async def handle_message(self, data: dict):
        """处理其他类型的消息"""
        message_type = data.get('type')
        if message_type == 'task_status':
            # 处理任务状态更新
            await self.handle_task_status(data)
        # 添加其他消息类型的处理...

    async def handle_task_status(self, data: dict):
        """处理任务状态更新"""
        # 实现任务状态更新的处理逻辑
        pass 