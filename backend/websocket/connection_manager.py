from typing import Dict
from channels.generic.websocket import AsyncWebsocketConsumer
import json

class ClientConnectionManager:
    """客户端连接管理器"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            # 使用字典存储连接映射关系
            self.connections: Dict[str, AsyncWebsocketConsumer] = {}
            self.initialized = True
    
    def register_connection(self, serial_number: str, connection: AsyncWebsocketConsumer):
        """注册客户端连接"""
        self.connections[serial_number] = connection
        
    def remove_connection(self, serial_number: str):
        """移除客户端连接"""
        if serial_number in self.connections:
            del self.connections[serial_number]
            
    async def send_to_client(self, serial_number: str, message: dict) -> bool:
        """向指定客户端发送消息"""
        if serial_number not in self.connections:
            return False
        
        try:
            await self.connections[serial_number].send(text_data=json.dumps(message))
            return True
        except Exception as e:
            print(f"Error sending message to client {serial_number}: {str(e)}")
            return False
            
    def get_all_clients(self) -> list:
        """获取所有已连接的客户端序列号"""
        return list(self.connections.keys())

# 全局连接管理器实例
connection_manager = ClientConnectionManager() 