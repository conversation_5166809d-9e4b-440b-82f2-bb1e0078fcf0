#!/bin/bash

# 每年1月1日凌晨5点执行的统计脚本
# 0 5 1 1 * /path/to/statistics/yearly_stats.sh

# 加载数据库配置
source "$(dirname "$0")/db_config.sh"

# 获取去年的开始和结束时间
last_year=$(date -d "last year" +%Y)
last_year_first="${last_year}-01-01"
last_year_last="${last_year}-12-31"

# 执行统计SQL
mysql_execute << EOF
-- 终端年度级别统计(固网和移动网) - 源终端统计
INSERT INTO tss_traffic_statistics (
    device_id, device_name, location, task_name,
    network_type, task_type, stat_time,
    upload_flowrate, download_flowrate,
    upload_traffic, download_traffic,
    upload_duration, download_duration,
    stat_level, create_datetime, update_datetime
)
SELECT 
    r.source_device_id as device_id,
    sd.name as device_name,
    sd.location,
    'yearly_stats' as task_name,
    r.network_type,
    'traffic' as task_type,
    DATE('${last_year_first}') as stat_time,
    SUM(CASE WHEN r.direction = 'upload' THEN r.total_traffic ELSE 0 END) / 
        NULLIF(SUM(CASE WHEN r.direction = 'upload' THEN r.actual_duration ELSE 0 END), 0) * 8 as upload_flowrate,
    SUM(CASE WHEN r.direction = 'download' THEN r.total_traffic ELSE 0 END) / 
        NULLIF(SUM(CASE WHEN r.direction = 'download' THEN r.actual_duration ELSE 0 END), 0) * 8 as download_flowrate,
    SUM(CASE WHEN r.direction = 'upload' THEN r.total_traffic ELSE 0 END) as upload_traffic,
    SUM(CASE WHEN r.direction = 'download' THEN r.total_traffic ELSE 0 END) as download_traffic,
    SUM(CASE WHEN r.direction = 'upload' THEN r.actual_duration ELSE 0 END) as upload_duration,
    SUM(CASE WHEN r.direction = 'download' THEN r.actual_duration ELSE 0 END) as download_duration,
    CONCAT('terminal_year_', r.network_type) as stat_level,
    NOW() as create_datetime,
    NOW() as update_datetime
FROM tss_traffictask_result r
JOIN tss_device sd ON r.source_device_id = sd.id
WHERE r.actual_start_time BETWEEN '${last_year_first} 00:00:00' AND '${last_year_last} 23:59:59'
    AND r.result = 'success'
    AND r.network_type IN ('fixed', 'mobile')
GROUP BY 
    r.source_device_id,
    sd.name,
    sd.location,
    r.network_type;

-- 终端年度级别统计(固网和移动网) - 目标终端统计
INSERT INTO tss_traffic_statistics (
    device_id, device_name, location, task_name,
    network_type, task_type, stat_time,
    upload_flowrate, download_flowrate,
    upload_traffic, download_traffic,
    upload_duration, download_duration,
    stat_level, create_datetime, update_datetime
)
SELECT 
    r.target_device_id as device_id,
    td.name as device_name,
    td.location,
    'yearly_stats' as task_name,
    r.network_type,
    'traffic' as task_type,
    DATE('${last_year_first}') as stat_time,
    SUM(CASE WHEN r.direction = 'download' THEN r.total_traffic ELSE 0 END) / 
        NULLIF(SUM(CASE WHEN r.direction = 'download' THEN r.actual_duration ELSE 0 END), 0) * 8 as upload_flowrate,
    SUM(CASE WHEN r.direction = 'upload' THEN r.total_traffic ELSE 0 END) / 
        NULLIF(SUM(CASE WHEN r.direction = 'upload' THEN r.actual_duration ELSE 0 END), 0) * 8 as download_flowrate,
    SUM(CASE WHEN r.direction = 'download' THEN r.total_traffic ELSE 0 END) as upload_traffic,
    SUM(CASE WHEN r.direction = 'upload' THEN r.total_traffic ELSE 0 END) as download_traffic,
    SUM(CASE WHEN r.direction = 'download' THEN r.actual_duration ELSE 0 END) as upload_duration,
    SUM(CASE WHEN r.direction = 'upload' THEN r.actual_duration ELSE 0 END) as download_duration,
    CONCAT('terminal_year_', r.network_type) as stat_level,
    NOW() as create_datetime,
    NOW() as update_datetime
FROM tss_traffictask_result r
JOIN tss_device td ON r.target_device_id = td.id
WHERE r.actual_start_time BETWEEN '${last_year_first} 00:00:00' AND '${last_year_last} 23:59:59'
    AND r.result = 'success'
    AND r.network_type IN ('fixed', 'mobile')
GROUP BY 
    r.target_device_id,
    td.name,
    td.location,
    r.network_type;

-- 终端年度级别总计
INSERT INTO tss_traffic_statistics (
    device_id, device_name, location, task_name,
    network_type, task_type, stat_time,
    upload_flowrate, download_flowrate,
    upload_traffic, download_traffic,
    upload_duration, download_duration,
    stat_level, create_datetime, update_datetime
)
SELECT 
    device_id,
    device_name,
    location,
    'yearly_stats' as task_name,
    'total' as network_type,
    'traffic' as task_type,
    stat_time,
    SUM(upload_traffic) / NULLIF(SUM(upload_duration), 0) * 8 as upload_flowrate,
    SUM(download_traffic) / NULLIF(SUM(download_duration), 0) * 8 as download_flowrate,
    SUM(upload_traffic) as upload_traffic,
    SUM(download_traffic) as download_traffic,
    SUM(upload_duration) as upload_duration,
    SUM(download_duration) as download_duration,
    'terminal_year_total' as stat_level,
    NOW() as create_datetime,
    NOW() as update_datetime
FROM tss_traffic_statistics
WHERE stat_time = DATE('${last_year_first}')
    AND stat_level IN ('terminal_year_fixed', 'terminal_year_mobile')
GROUP BY device_id, device_name, location, stat_time;

-- 属地年度级别统计
INSERT INTO tss_traffic_statistics (
    location, task_name, network_type, task_type,
    stat_time, upload_flowrate, download_flowrate,
    upload_traffic, download_traffic,
    upload_duration, download_duration,
    stat_level, create_datetime, update_datetime
)
SELECT 
    location,
    'yearly_stats' as task_name,
    network_type,
    'traffic' as task_type,
    stat_time,
    SUM(upload_traffic) / NULLIF(SUM(upload_duration), 0) * 8 as upload_flowrate,
    SUM(download_traffic) / NULLIF(SUM(download_duration), 0) * 8 as download_flowrate,
    SUM(upload_traffic) as upload_traffic,
    SUM(download_traffic) as download_traffic,
    SUM(upload_duration) as upload_duration,
    SUM(download_duration) as download_duration,
    CONCAT('area_year_', network_type) as stat_level,
    NOW() as create_datetime,
    NOW() as update_datetime
FROM tss_traffic_statistics
WHERE stat_time = DATE('${last_year_first}')
    AND stat_level LIKE 'terminal_year_%'
GROUP BY location, network_type, stat_time;

-- 全省年度级别统计
INSERT INTO tss_traffic_statistics (
    location,task_name, network_type, task_type,
    stat_time, upload_flowrate, download_flowrate,
    upload_traffic, download_traffic,
    upload_duration, download_duration,
    stat_level, create_datetime, update_datetime
)
SELECT 
    '53',
    'yearly_stats' as task_name,
    network_type,
    'traffic' as task_type,
    stat_time,
    SUM(upload_traffic) / NULLIF(SUM(upload_duration), 0) * 8 as upload_flowrate,
    SUM(download_traffic) / NULLIF(SUM(download_duration), 0) * 8 as download_flowrate,
    SUM(upload_traffic) as upload_traffic,
    SUM(download_traffic) as download_traffic,
    SUM(upload_duration) as upload_duration,
    SUM(download_duration) as download_duration,
    CONCAT('province_year_', network_type) as stat_level,
    NOW() as create_datetime,
    NOW() as update_datetime
FROM tss_traffic_statistics
WHERE stat_time = DATE('${last_year_first}')
    AND stat_level LIKE 'area_year_%'
GROUP BY network_type, stat_time;
EOF