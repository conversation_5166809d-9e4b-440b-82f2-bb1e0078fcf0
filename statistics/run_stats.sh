#!/bin/bash

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 根据当前日期决定要执行哪些统计
today=$(date +%Y-%m-%d)
day_of_week=$(date +%u)
day_of_month=$(date +%d)
month=$(date +%m)

# 每天都执行日统计
"$SCRIPT_DIR/daily_stats.sh"

# 周一执行周统计
if [ "$day_of_week" = "1" ]; then
    "$SCRIPT_DIR/weekly_stats.sh"
fi

# 每月1号执行月统计
if [ "$day_of_month" = "01" ]; then
    "$SCRIPT_DIR/monthly_stats.sh"
fi

# 每季度第一天执行季度统计
if [ "$day_of_month" = "01" ] && [ "$month" = "01" -o "$month" = "04" -o "$month" = "07" -o "$month" = "10" ]; then
    "$SCRIPT_DIR/quarterly_stats.sh"
fi

# 每年1月1日执行年度统计
if [ "$day_of_month" = "01" ] && [ "$month" = "01" ]; then
    "$SCRIPT_DIR/yearly_stats.sh"
fi 