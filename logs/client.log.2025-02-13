2025-02-13 14:40:07 [INFO] Client starting...
2025-02-13 14:40:08 [INFO] Login successful
2025-02-13 14:40:08 [INFO] Checking registration status...
2025-02-13 14:40:08 [INFO] Client already registered
2025-02-13 14:40:08 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 14:40:08 [INFO] Starting heartbeat...
2025-02-13 14:40:10 [DEBUG] Heartbeat sent successfully
2025-02-13 14:40:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:10 [INFO] SSH tunnel established
2025-02-13 14:40:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:25 [ERROR] 连接失败: [<PERSON>rr<PERSON> 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:39 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:44 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:40:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:40:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:11 [DEBUG] Heartbeat sent successfully
2025-02-13 14:41:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:41:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:41:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:12 [DEBUG] Heartbeat sent successfully
2025-02-13 14:42:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:21 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:42:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:42:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:13 [DEBUG] Heartbeat sent successfully
2025-02-13 14:43:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:43:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:43:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:01 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:11 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:14 [DEBUG] Heartbeat sent successfully
2025-02-13 14:44:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:44:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:44:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:15 [DEBUG] Heartbeat sent successfully
2025-02-13 14:45:16 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:45:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:45:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:11 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:17 [DEBUG] Heartbeat sent successfully
2025-02-13 14:46:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:28 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:46:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:46:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:11 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:18 [DEBUG] Heartbeat sent successfully
2025-02-13 14:47:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:33 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:38 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:47:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:47:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:09 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:14 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:16 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:19 [DEBUG] Heartbeat sent successfully
2025-02-13 14:48:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:45 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:48:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:48:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:11 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:20 [DEBUG] Heartbeat sent successfully
2025-02-13 14:49:21 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:28 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:49:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:49:57 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:50:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:50:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:50:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:50:11 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:50:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:50:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 14:50:21 [DEBUG] Heartbeat sent successfully
2025-02-13 14:50:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 14:50:24 [INFO] 注册成功
2025-02-13 14:51:22 [DEBUG] Heartbeat sent successfully
2025-02-13 14:52:24 [DEBUG] Heartbeat sent successfully
2025-02-13 14:53:25 [DEBUG] Heartbeat sent successfully
2025-02-13 14:54:26 [DEBUG] Heartbeat sent successfully
2025-02-13 14:55:27 [DEBUG] Heartbeat sent successfully
2025-02-13 14:56:28 [DEBUG] Heartbeat sent successfully
2025-02-13 14:57:30 [DEBUG] Heartbeat sent successfully
2025-02-13 14:58:31 [DEBUG] Heartbeat sent successfully
2025-02-13 14:59:32 [DEBUG] Heartbeat sent successfully
2025-02-13 15:00:33 [DEBUG] Heartbeat sent successfully
2025-02-13 15:01:34 [DEBUG] Heartbeat sent successfully
2025-02-13 15:02:35 [DEBUG] Heartbeat sent successfully
2025-02-13 15:03:37 [DEBUG] Heartbeat sent successfully
2025-02-13 15:04:38 [DEBUG] Heartbeat sent successfully
2025-02-13 15:05:39 [DEBUG] Heartbeat sent successfully
2025-02-13 15:06:40 [DEBUG] Heartbeat sent successfully
2025-02-13 15:07:41 [DEBUG] Heartbeat sent successfully
2025-02-13 15:08:42 [DEBUG] Heartbeat sent successfully
2025-02-13 15:09:44 [DEBUG] Heartbeat sent successfully
2025-02-13 15:10:45 [DEBUG] Heartbeat sent successfully
2025-02-13 15:11:46 [DEBUG] Heartbeat sent successfully
2025-02-13 15:12:47 [DEBUG] Heartbeat sent successfully
2025-02-13 15:13:48 [DEBUG] Heartbeat sent successfully
2025-02-13 15:14:49 [DEBUG] Heartbeat sent successfully
2025-02-13 15:15:51 [DEBUG] Heartbeat sent successfully
2025-02-13 15:16:52 [DEBUG] Heartbeat sent successfully
2025-02-13 15:17:53 [DEBUG] Heartbeat sent successfully
2025-02-13 15:18:54 [DEBUG] Heartbeat sent successfully
2025-02-13 15:19:55 [DEBUG] Heartbeat sent successfully
2025-02-13 15:20:56 [DEBUG] Heartbeat sent successfully
2025-02-13 15:21:58 [DEBUG] Heartbeat sent successfully
2025-02-13 15:22:59 [DEBUG] Heartbeat sent successfully
2025-02-13 15:24:00 [DEBUG] Heartbeat sent successfully
2025-02-13 15:25:01 [DEBUG] Heartbeat sent successfully
2025-02-13 15:26:02 [DEBUG] Heartbeat sent successfully
2025-02-13 15:27:03 [DEBUG] Heartbeat sent successfully
2025-02-13 15:28:05 [DEBUG] Heartbeat sent successfully
2025-02-13 15:29:06 [DEBUG] Heartbeat sent successfully
2025-02-13 15:30:07 [DEBUG] Heartbeat sent successfully
2025-02-13 15:31:08 [DEBUG] Heartbeat sent successfully
2025-02-13 15:32:09 [DEBUG] Heartbeat sent successfully
2025-02-13 15:33:11 [DEBUG] Heartbeat sent successfully
2025-02-13 15:34:12 [DEBUG] Heartbeat sent successfully
2025-02-13 15:35:13 [DEBUG] Heartbeat sent successfully
2025-02-13 15:36:14 [DEBUG] Heartbeat sent successfully
2025-02-13 15:37:15 [DEBUG] Heartbeat sent successfully
2025-02-13 15:38:16 [DEBUG] Heartbeat sent successfully
2025-02-13 15:39:18 [DEBUG] Heartbeat sent successfully
2025-02-13 15:40:19 [DEBUG] Heartbeat sent successfully
2025-02-13 15:41:20 [DEBUG] Heartbeat sent successfully
2025-02-13 15:42:21 [DEBUG] Heartbeat sent successfully
2025-02-13 15:43:22 [DEBUG] Heartbeat sent successfully
2025-02-13 15:44:23 [DEBUG] Heartbeat sent successfully
2025-02-13 15:45:25 [DEBUG] Heartbeat sent successfully
2025-02-13 15:46:26 [DEBUG] Heartbeat sent successfully
2025-02-13 15:47:27 [DEBUG] Heartbeat sent successfully
2025-02-13 15:48:28 [DEBUG] Heartbeat sent successfully
2025-02-13 15:49:29 [DEBUG] Heartbeat sent successfully
2025-02-13 15:50:30 [DEBUG] Heartbeat sent successfully
2025-02-13 15:51:32 [DEBUG] Heartbeat sent successfully
2025-02-13 15:52:33 [DEBUG] Heartbeat sent successfully
2025-02-13 15:53:34 [DEBUG] Heartbeat sent successfully
2025-02-13 15:54:35 [DEBUG] Heartbeat sent successfully
2025-02-13 15:55:36 [DEBUG] Heartbeat sent successfully
2025-02-13 15:56:37 [DEBUG] Heartbeat sent successfully
2025-02-13 15:57:39 [DEBUG] Heartbeat sent successfully
2025-02-13 15:58:40 [DEBUG] Heartbeat sent successfully
2025-02-13 15:59:41 [DEBUG] Heartbeat sent successfully
2025-02-13 16:00:42 [DEBUG] Heartbeat sent successfully
2025-02-13 16:01:43 [DEBUG] Heartbeat sent successfully
2025-02-13 16:02:45 [DEBUG] Heartbeat sent successfully
2025-02-13 16:03:46 [DEBUG] Heartbeat sent successfully
2025-02-13 16:04:47 [DEBUG] Heartbeat sent successfully
2025-02-13 16:05:48 [DEBUG] Heartbeat sent successfully
2025-02-13 16:06:49 [DEBUG] Heartbeat sent successfully
2025-02-13 16:07:50 [DEBUG] Heartbeat sent successfully
2025-02-13 16:08:52 [DEBUG] Heartbeat sent successfully
2025-02-13 16:09:53 [DEBUG] Heartbeat sent successfully
2025-02-13 16:10:54 [DEBUG] Heartbeat sent successfully
2025-02-13 16:11:55 [DEBUG] Heartbeat sent successfully
2025-02-13 16:12:56 [DEBUG] Heartbeat sent successfully
2025-02-13 16:13:57 [DEBUG] Heartbeat sent successfully
2025-02-13 16:14:59 [DEBUG] Heartbeat sent successfully
2025-02-13 16:16:00 [DEBUG] Heartbeat sent successfully
2025-02-13 16:17:01 [DEBUG] Heartbeat sent successfully
2025-02-13 16:18:02 [DEBUG] Heartbeat sent successfully
2025-02-13 16:19:03 [DEBUG] Heartbeat sent successfully
2025-02-13 16:20:04 [DEBUG] Heartbeat sent successfully
2025-02-13 16:21:06 [DEBUG] Heartbeat sent successfully
2025-02-13 16:22:07 [DEBUG] Heartbeat sent successfully
2025-02-13 16:23:08 [DEBUG] Heartbeat sent successfully
2025-02-13 16:24:09 [DEBUG] Heartbeat sent successfully
2025-02-13 16:25:10 [DEBUG] Heartbeat sent successfully
2025-02-13 16:26:11 [DEBUG] Heartbeat sent successfully
2025-02-13 16:27:13 [DEBUG] Heartbeat sent successfully
2025-02-13 16:28:14 [DEBUG] Heartbeat sent successfully
2025-02-13 16:29:15 [DEBUG] Heartbeat sent successfully
2025-02-13 16:30:16 [DEBUG] Heartbeat sent successfully
2025-02-13 16:31:17 [DEBUG] Heartbeat sent successfully
2025-02-13 16:32:18 [DEBUG] Heartbeat sent successfully
2025-02-13 16:33:20 [DEBUG] Heartbeat sent successfully
2025-02-13 16:34:21 [DEBUG] Heartbeat sent successfully
2025-02-13 16:35:22 [DEBUG] Heartbeat sent successfully
2025-02-13 16:36:23 [DEBUG] Heartbeat sent successfully
2025-02-13 16:37:24 [DEBUG] Heartbeat sent successfully
2025-02-13 16:38:26 [DEBUG] Heartbeat sent successfully
2025-02-13 16:39:27 [DEBUG] Heartbeat sent successfully
2025-02-13 16:40:28 [DEBUG] Heartbeat sent successfully
2025-02-13 16:41:29 [DEBUG] Heartbeat sent successfully
2025-02-13 16:42:30 [DEBUG] Heartbeat sent successfully
2025-02-13 16:43:31 [DEBUG] Heartbeat sent successfully
2025-02-13 16:44:33 [DEBUG] Heartbeat sent successfully
2025-02-13 16:45:34 [DEBUG] Heartbeat sent successfully
2025-02-13 16:46:35 [DEBUG] Heartbeat sent successfully
2025-02-13 16:47:36 [DEBUG] Heartbeat sent successfully
2025-02-13 16:48:37 [DEBUG] Heartbeat sent successfully
2025-02-13 16:49:38 [DEBUG] Heartbeat sent successfully
2025-02-13 16:50:40 [DEBUG] Heartbeat sent successfully
2025-02-13 16:51:41 [DEBUG] Heartbeat sent successfully
2025-02-13 16:52:42 [DEBUG] Heartbeat sent successfully
2025-02-13 16:53:43 [DEBUG] Heartbeat sent successfully
2025-02-13 16:54:44 [DEBUG] Heartbeat sent successfully
2025-02-13 16:55:46 [DEBUG] Heartbeat sent successfully
2025-02-13 16:56:47 [DEBUG] Heartbeat sent successfully
2025-02-13 16:57:48 [DEBUG] Heartbeat sent successfully
2025-02-13 16:58:49 [DEBUG] Heartbeat sent successfully
2025-02-13 16:59:51 [DEBUG] Heartbeat sent successfully
2025-02-13 17:00:52 [DEBUG] Heartbeat sent successfully
2025-02-13 17:01:53 [DEBUG] Heartbeat sent successfully
2025-02-13 17:02:54 [DEBUG] Heartbeat sent successfully
2025-02-13 17:03:55 [DEBUG] Heartbeat sent successfully
2025-02-13 17:04:56 [DEBUG] Heartbeat sent successfully
2025-02-13 17:05:58 [DEBUG] Heartbeat sent successfully
2025-02-13 17:06:59 [DEBUG] Heartbeat sent successfully
2025-02-13 17:08:00 [DEBUG] Heartbeat sent successfully
2025-02-13 17:09:01 [DEBUG] Heartbeat sent successfully
2025-02-13 17:10:02 [DEBUG] Heartbeat sent successfully
2025-02-13 17:11:03 [DEBUG] Heartbeat sent successfully
2025-02-13 17:12:05 [DEBUG] Heartbeat sent successfully
2025-02-13 17:13:06 [DEBUG] Heartbeat sent successfully
2025-02-13 17:14:07 [DEBUG] Heartbeat sent successfully
2025-02-13 17:15:08 [DEBUG] Heartbeat sent successfully
2025-02-13 17:16:09 [DEBUG] Heartbeat sent successfully
2025-02-13 17:17:11 [DEBUG] Heartbeat sent successfully
2025-02-13 17:18:12 [DEBUG] Heartbeat sent successfully
2025-02-13 17:19:13 [DEBUG] Heartbeat sent successfully
2025-02-13 17:20:14 [DEBUG] Heartbeat sent successfully
2025-02-13 17:21:15 [DEBUG] Heartbeat sent successfully
2025-02-13 17:22:17 [DEBUG] Heartbeat sent successfully
2025-02-13 17:23:18 [DEBUG] Heartbeat sent successfully
2025-02-13 17:24:19 [DEBUG] Heartbeat sent successfully
2025-02-13 17:25:20 [DEBUG] Heartbeat sent successfully
2025-02-13 17:26:21 [DEBUG] Heartbeat sent successfully
2025-02-13 17:27:22 [DEBUG] Heartbeat sent successfully
2025-02-13 17:28:24 [DEBUG] Heartbeat sent successfully
2025-02-13 17:29:25 [DEBUG] Heartbeat sent successfully
2025-02-13 17:30:26 [DEBUG] Heartbeat sent successfully
2025-02-13 17:31:27 [DEBUG] Heartbeat sent successfully
2025-02-13 17:32:28 [DEBUG] Heartbeat sent successfully
2025-02-13 17:33:29 [DEBUG] Heartbeat sent successfully
2025-02-13 17:34:31 [DEBUG] Heartbeat sent successfully
2025-02-13 17:35:32 [DEBUG] Heartbeat sent successfully
2025-02-13 17:36:33 [DEBUG] Heartbeat sent successfully
2025-02-13 17:37:34 [DEBUG] Heartbeat sent successfully
2025-02-13 17:38:35 [DEBUG] Heartbeat sent successfully
2025-02-13 17:39:36 [DEBUG] Heartbeat sent successfully
2025-02-13 17:40:38 [DEBUG] Heartbeat sent successfully
2025-02-13 17:41:39 [DEBUG] Heartbeat sent successfully
2025-02-13 17:42:40 [DEBUG] Heartbeat sent successfully
2025-02-13 17:43:41 [DEBUG] Heartbeat sent successfully
2025-02-13 17:44:43 [DEBUG] Heartbeat sent successfully
2025-02-13 17:45:44 [DEBUG] Heartbeat sent successfully
2025-02-13 17:46:45 [DEBUG] Heartbeat sent successfully
2025-02-13 17:47:46 [DEBUG] Heartbeat sent successfully
2025-02-13 17:48:47 [DEBUG] Heartbeat sent successfully
2025-02-13 17:49:49 [DEBUG] Heartbeat sent successfully
2025-02-13 17:50:50 [DEBUG] Heartbeat sent successfully
2025-02-13 17:51:51 [DEBUG] Heartbeat sent successfully
2025-02-13 17:52:52 [DEBUG] Heartbeat sent successfully
2025-02-13 17:53:53 [DEBUG] Heartbeat sent successfully
2025-02-13 17:54:54 [DEBUG] Heartbeat sent successfully
2025-02-13 17:55:56 [DEBUG] Heartbeat sent successfully
2025-02-13 17:56:57 [DEBUG] Heartbeat sent successfully
2025-02-13 17:57:58 [DEBUG] Heartbeat sent successfully
2025-02-13 17:58:59 [DEBUG] Heartbeat sent successfully
2025-02-13 18:00:00 [DEBUG] Heartbeat sent successfully
2025-02-13 18:01:02 [DEBUG] Heartbeat sent successfully
2025-02-13 18:02:03 [DEBUG] Heartbeat sent successfully
2025-02-13 18:03:04 [DEBUG] Heartbeat sent successfully
2025-02-13 18:04:05 [DEBUG] Heartbeat sent successfully
2025-02-13 18:05:06 [DEBUG] Heartbeat sent successfully
2025-02-13 18:06:07 [DEBUG] Heartbeat sent successfully
2025-02-13 18:07:09 [DEBUG] Heartbeat sent successfully
2025-02-13 18:08:10 [DEBUG] Heartbeat sent successfully
2025-02-13 18:09:11 [DEBUG] Heartbeat sent successfully
2025-02-13 18:10:12 [DEBUG] Heartbeat sent successfully
2025-02-13 18:11:13 [DEBUG] Heartbeat sent successfully
2025-02-13 19:07:22 [ERROR] WebSocket连接断开，准备重连
2025-02-13 19:07:22 [INFO] WebSocket连接已安全关闭
2025-02-13 22:40:42 [INFO] Client starting...
2025-02-13 22:40:43 [INFO] Login successful
2025-02-13 22:40:43 [INFO] Checking registration status...
2025-02-13 22:40:43 [INFO] Client already registered
2025-02-13 22:40:43 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 22:40:43 [INFO] Starting heartbeat...
2025-02-13 22:40:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 22:40:43 [INFO] SSH tunnel established
2025-02-13 22:40:44 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:40:44 [INFO] 注册成功
2025-02-13 22:41:45 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:42:46 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:43:47 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:44:48 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:45:49 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:46:50 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:47:51 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:48:52 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-13 22:49:02 [INFO] Program interrupted by user
2025-02-13 22:49:02 [ERROR] 关闭WebSocket连接时发生错误: no running event loop
2025-02-13 22:49:02 [INFO] 连接管理器已停止
2025-02-13 22:49:19 [INFO] Client starting...
2025-02-13 22:49:20 [INFO] Login successful
2025-02-13 22:49:20 [INFO] Checking registration status...
2025-02-13 22:49:20 [INFO] Client already registered
2025-02-13 22:49:20 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 22:49:20 [INFO] Starting heartbeat...
2025-02-13 22:49:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 22:49:20 [INFO] SSH tunnel established
2025-02-13 22:49:21 [DEBUG] Heartbeat sent successfully
2025-02-13 22:49:21 [INFO] 注册成功
2025-02-13 22:50:22 [DEBUG] Heartbeat sent successfully
2025-02-13 22:51:23 [DEBUG] Heartbeat sent successfully
2025-02-13 22:52:25 [DEBUG] Heartbeat sent successfully
2025-02-13 22:53:26 [DEBUG] Heartbeat sent successfully
2025-02-13 22:54:27 [DEBUG] Heartbeat sent successfully
2025-02-13 22:55:28 [DEBUG] Heartbeat sent successfully
2025-02-13 22:56:29 [DEBUG] Heartbeat sent successfully
2025-02-13 22:57:31 [DEBUG] Heartbeat sent successfully
2025-02-13 22:58:32 [DEBUG] Heartbeat sent successfully
2025-02-13 22:59:33 [DEBUG] Heartbeat sent successfully
2025-02-13 23:00:34 [DEBUG] Heartbeat sent successfully
2025-02-13 23:01:35 [DEBUG] Heartbeat sent successfully
2025-02-13 23:02:36 [DEBUG] Heartbeat sent successfully
2025-02-13 23:03:38 [DEBUG] Heartbeat sent successfully
2025-02-13 23:04:39 [DEBUG] Heartbeat sent successfully
2025-02-13 23:05:40 [DEBUG] Heartbeat sent successfully
2025-02-13 23:06:33 [INFO] 收到消息: start_task
2025-02-13 23:06:33 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 25, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:06:33 [INFO] iperf3 客户端启动配置:
2025-02-13 23:06:33 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:06:33 [INFO]   - 端口: 39999
2025-02-13 23:06:33 [INFO]   - 网卡: ens37
2025-02-13 23:06:33 [INFO]   - IPv6模式: True
2025-02-13 23:06:33 [INFO]   - 持续时间: 30秒
2025-02-13 23:06:33 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:06:33 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:06:33 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:06:41 [DEBUG] Heartbeat sent successfully
2025-02-13 23:07:35 [INFO] 收到消息: start_task
2025-02-13 23:07:35 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 26, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:07:35 [INFO] iperf3 客户端启动配置:
2025-02-13 23:07:35 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:07:35 [INFO]   - 端口: 39999
2025-02-13 23:07:35 [INFO]   - 网卡: ens37
2025-02-13 23:07:35 [INFO]   - IPv6模式: True
2025-02-13 23:07:35 [INFO]   - 持续时间: 30秒
2025-02-13 23:07:35 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:07:35 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:07:35 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:07:42 [DEBUG] Heartbeat sent successfully
2025-02-13 23:08:37 [INFO] 收到消息: start_task
2025-02-13 23:08:37 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 27, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:08:37 [INFO] iperf3 客户端启动配置:
2025-02-13 23:08:37 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:08:37 [INFO]   - 端口: 39999
2025-02-13 23:08:37 [INFO]   - 网卡: ens37
2025-02-13 23:08:37 [INFO]   - IPv6模式: True
2025-02-13 23:08:37 [INFO]   - 持续时间: 30秒
2025-02-13 23:08:37 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:08:37 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:08:37 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:08:43 [DEBUG] Heartbeat sent successfully
2025-02-13 23:09:39 [INFO] 收到消息: start_task
2025-02-13 23:09:39 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 28, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:09:39 [INFO] iperf3 客户端启动配置:
2025-02-13 23:09:39 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:09:39 [INFO]   - 端口: 39999
2025-02-13 23:09:39 [INFO]   - 网卡: ens37
2025-02-13 23:09:39 [INFO]   - IPv6模式: True
2025-02-13 23:09:39 [INFO]   - 持续时间: 30秒
2025-02-13 23:09:39 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:09:39 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:09:39 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:09:45 [DEBUG] Heartbeat sent successfully
2025-02-13 23:10:41 [INFO] 收到消息: start_task
2025-02-13 23:10:41 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 29, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:10:41 [INFO] iperf3 客户端启动配置:
2025-02-13 23:10:41 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:10:41 [INFO]   - 端口: 39999
2025-02-13 23:10:41 [INFO]   - 网卡: ens37
2025-02-13 23:10:41 [INFO]   - IPv6模式: True
2025-02-13 23:10:41 [INFO]   - 持续时间: 30秒
2025-02-13 23:10:41 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:10:41 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:10:41 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:10:46 [DEBUG] Heartbeat sent successfully
2025-02-13 23:11:43 [INFO] 收到消息: start_task
2025-02-13 23:11:43 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 30, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:11:43 [INFO] iperf3 客户端启动配置:
2025-02-13 23:11:43 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:11:43 [INFO]   - 端口: 39999
2025-02-13 23:11:43 [INFO]   - 网卡: ens37
2025-02-13 23:11:43 [INFO]   - IPv6模式: True
2025-02-13 23:11:43 [INFO]   - 持续时间: 30秒
2025-02-13 23:11:43 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:11:43 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:11:43 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:11:47 [DEBUG] Heartbeat sent successfully
2025-02-13 23:12:17 [INFO] Program interrupted by user
2025-02-13 23:12:20 [INFO] Client starting...
2025-02-13 23:12:21 [INFO] Login successful
2025-02-13 23:12:21 [INFO] Checking registration status...
2025-02-13 23:12:21 [INFO] Client already registered
2025-02-13 23:12:21 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:12:21 [INFO] Starting heartbeat...
2025-02-13 23:12:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:12:21 [INFO] SSH tunnel established
2025-02-13 23:12:22 [DEBUG] Heartbeat sent successfully
2025-02-13 23:12:22 [INFO] 注册成功
2025-02-13 23:12:45 [INFO] 收到消息: start_task
2025-02-13 23:12:45 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 31, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:12:45 [INFO] iperf3 客户端启动配置:
2025-02-13 23:12:45 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:12:45 [INFO]   - 端口: 39999
2025-02-13 23:12:45 [INFO]   - 网卡: ens37
2025-02-13 23:12:45 [INFO]   - IPv6模式: True
2025-02-13 23:12:45 [INFO]   - 持续时间: 30秒
2025-02-13 23:12:45 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:12:45 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:12:45 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:13:23 [DEBUG] Heartbeat sent successfully
2025-02-13 23:13:47 [INFO] 收到消息: start_task
2025-02-13 23:13:47 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 32, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:13:47 [INFO] iperf3 客户端启动配置:
2025-02-13 23:13:47 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:13:47 [INFO]   - 端口: 39999
2025-02-13 23:13:47 [INFO]   - 网卡: ens37
2025-02-13 23:13:47 [INFO]   - IPv6模式: True
2025-02-13 23:13:47 [INFO]   - 持续时间: 30秒
2025-02-13 23:13:47 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:13:47 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:13:47 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:14:24 [DEBUG] Heartbeat sent successfully
2025-02-13 23:14:49 [INFO] 收到消息: start_task
2025-02-13 23:14:49 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 33, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:14:49 [INFO] iperf3 客户端启动配置:
2025-02-13 23:14:49 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:14:49 [INFO]   - 端口: 39999
2025-02-13 23:14:49 [INFO]   - 网卡: ens37
2025-02-13 23:14:49 [INFO]   - IPv6模式: True
2025-02-13 23:14:49 [INFO]   - 持续时间: 30秒
2025-02-13 23:14:49 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:14:49 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:14:49 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:15:25 [DEBUG] Heartbeat sent successfully
2025-02-13 23:15:51 [INFO] 收到消息: start_task
2025-02-13 23:15:51 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 34, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:15:51 [INFO] iperf3 客户端启动配置:
2025-02-13 23:15:51 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:15:51 [INFO]   - 端口: 39999
2025-02-13 23:15:51 [INFO]   - 网卡: ens37
2025-02-13 23:15:51 [INFO]   - IPv6模式: True
2025-02-13 23:15:51 [INFO]   - 持续时间: 30秒
2025-02-13 23:15:51 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:15:51 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:15:51 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:16:27 [DEBUG] Heartbeat sent successfully
2025-02-13 23:16:53 [INFO] 收到消息: start_task
2025-02-13 23:16:53 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 35, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:16:53 [INFO] iperf3 客户端启动配置:
2025-02-13 23:16:53 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:16:53 [INFO]   - 端口: 39999
2025-02-13 23:16:53 [INFO]   - 网卡: ens37
2025-02-13 23:16:53 [INFO]   - IPv6模式: True
2025-02-13 23:16:53 [INFO]   - 持续时间: 30秒
2025-02-13 23:16:53 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:16:53 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:16:53 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:17:28 [DEBUG] Heartbeat sent successfully
2025-02-13 23:17:55 [INFO] 收到消息: start_task
2025-02-13 23:17:55 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 36, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:17:55 [INFO] iperf3 客户端启动配置:
2025-02-13 23:17:55 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:17:55 [INFO]   - 端口: 39999
2025-02-13 23:17:55 [INFO]   - 网卡: ens37
2025-02-13 23:17:55 [INFO]   - IPv6模式: True
2025-02-13 23:17:55 [INFO]   - 持续时间: 30秒
2025-02-13 23:17:55 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:17:55 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:17:55 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:18:29 [DEBUG] Heartbeat sent successfully
2025-02-13 23:18:57 [INFO] 收到消息: start_task
2025-02-13 23:18:57 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 37, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:18:57 [INFO] iperf3 客户端启动配置:
2025-02-13 23:18:57 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:18:57 [INFO]   - 端口: 39999
2025-02-13 23:18:57 [INFO]   - 网卡: ens37
2025-02-13 23:18:57 [INFO]   - IPv6模式: True
2025-02-13 23:18:57 [INFO]   - 持续时间: 30秒
2025-02-13 23:18:57 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:18:57 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:18:57 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:19:30 [DEBUG] Heartbeat sent successfully
2025-02-13 23:19:59 [INFO] 收到消息: start_task
2025-02-13 23:19:59 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 38, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:19:59 [INFO] iperf3 客户端启动配置:
2025-02-13 23:19:59 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:19:59 [INFO]   - 端口: 39999
2025-02-13 23:19:59 [INFO]   - 网卡: ens37
2025-02-13 23:19:59 [INFO]   - IPv6模式: True
2025-02-13 23:19:59 [INFO]   - 持续时间: 30秒
2025-02-13 23:19:59 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:19:59 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:19:59 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:20:31 [DEBUG] Heartbeat sent successfully
2025-02-13 23:21:01 [INFO] 收到消息: start_task
2025-02-13 23:21:01 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 39, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:21:01 [INFO] iperf3 客户端启动配置:
2025-02-13 23:21:01 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:21:01 [INFO]   - 端口: 39999
2025-02-13 23:21:01 [INFO]   - 网卡: ens37
2025-02-13 23:21:01 [INFO]   - IPv6模式: True
2025-02-13 23:21:01 [INFO]   - 持续时间: 30秒
2025-02-13 23:21:01 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:21:01 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:21:01 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:21:02 [INFO] Program interrupted by user
2025-02-13 23:21:26 [INFO] Client starting...
2025-02-13 23:21:27 [INFO] Login successful
2025-02-13 23:21:27 [INFO] Checking registration status...
2025-02-13 23:21:27 [INFO] Client already registered
2025-02-13 23:21:27 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:21:27 [INFO] Starting heartbeat...
2025-02-13 23:21:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:21:27 [INFO] SSH tunnel established
2025-02-13 23:21:28 [DEBUG] Heartbeat sent successfully
2025-02-13 23:21:28 [INFO] 注册成功
2025-02-13 23:22:03 [INFO] 收到消息: start_task
2025-02-13 23:22:03 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 40, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:22:03 [INFO] iperf3 客户端启动配置:
2025-02-13 23:22:03 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:22:03 [INFO]   - 端口: 39999
2025-02-13 23:22:03 [INFO]   - 网卡: ens37
2025-02-13 23:22:03 [INFO]   - IPv6模式: True
2025-02-13 23:22:03 [INFO]   - 持续时间: 30秒
2025-02-13 23:22:03 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:22:03 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:22:03 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:22:29 [DEBUG] Heartbeat sent successfully
2025-02-13 23:23:05 [INFO] 收到消息: start_task
2025-02-13 23:23:05 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 41, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:23:05 [INFO] iperf3 客户端启动配置:
2025-02-13 23:23:05 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:23:05 [INFO]   - 端口: 39999
2025-02-13 23:23:05 [INFO]   - 网卡: ens37
2025-02-13 23:23:05 [INFO]   - IPv6模式: True
2025-02-13 23:23:05 [INFO]   - 持续时间: 30秒
2025-02-13 23:23:05 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:23:05 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:23:05 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:23:30 [DEBUG] Heartbeat sent successfully
2025-02-13 23:24:07 [INFO] 收到消息: start_task
2025-02-13 23:24:07 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 42, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:24:07 [INFO] iperf3 客户端启动配置:
2025-02-13 23:24:07 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:24:07 [INFO]   - 端口: 39999
2025-02-13 23:24:07 [INFO]   - 网卡: ens37
2025-02-13 23:24:07 [INFO]   - IPv6模式: True
2025-02-13 23:24:07 [INFO]   - 持续时间: 30秒
2025-02-13 23:24:07 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:24:07 [ERROR] iperf3 测试失败: cannot access local variable 'loop' where it is not associated with a value
2025-02-13 23:24:07 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 137, in handle_task_start
    result = await loop.run_in_executor(None, self.iperf_client.run)
                   ^^^^
UnboundLocalError: cannot access local variable 'loop' where it is not associated with a value

2025-02-13 23:24:32 [DEBUG] Heartbeat sent successfully
2025-02-13 23:24:44 [INFO] Program interrupted by user
2025-02-13 23:24:45 [INFO] Client starting...
2025-02-13 23:24:45 [INFO] Login successful
2025-02-13 23:24:45 [INFO] Checking registration status...
2025-02-13 23:24:46 [INFO] Client already registered
2025-02-13 23:24:46 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:24:46 [INFO] Starting heartbeat...
2025-02-13 23:24:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:24:46 [INFO] SSH tunnel established
2025-02-13 23:24:47 [DEBUG] Heartbeat sent successfully
2025-02-13 23:24:47 [INFO] 注册成功
2025-02-13 23:25:09 [INFO] 收到消息: start_task
2025-02-13 23:25:09 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 43, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:25:09 [INFO] iperf3 客户端启动配置:
2025-02-13 23:25:09 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:25:09 [INFO]   - 端口: 39999
2025-02-13 23:25:09 [INFO]   - 网卡: ens37
2025-02-13 23:25:09 [INFO]   - IPv6模式: True
2025-02-13 23:25:09 [INFO]   - 持续时间: 30秒
2025-02-13 23:25:09 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:25:40 [ERROR] iperf3 测试失败: None
2025-02-13 23:25:48 [DEBUG] Heartbeat sent successfully
2025-02-13 23:26:11 [INFO] 收到消息: start_task
2025-02-13 23:26:11 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 44, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:26:11 [INFO] iperf3 客户端启动配置:
2025-02-13 23:26:11 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:26:11 [INFO]   - 端口: 39999
2025-02-13 23:26:11 [INFO]   - 网卡: ens37
2025-02-13 23:26:11 [INFO]   - IPv6模式: True
2025-02-13 23:26:11 [INFO]   - 持续时间: 30秒
2025-02-13 23:26:11 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:26:42 [ERROR] iperf3 测试失败: None
2025-02-13 23:26:49 [DEBUG] Heartbeat sent successfully
2025-02-13 23:27:13 [INFO] 收到消息: start_task
2025-02-13 23:27:13 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 45, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:27:14 [INFO] iperf3 客户端启动配置:
2025-02-13 23:27:14 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:27:14 [INFO]   - 端口: 39999
2025-02-13 23:27:14 [INFO]   - 网卡: ens37
2025-02-13 23:27:14 [INFO]   - IPv6模式: True
2025-02-13 23:27:14 [INFO]   - 持续时间: 30秒
2025-02-13 23:27:14 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:27:44 [ERROR] iperf3 测试失败: None
2025-02-13 23:27:50 [DEBUG] Heartbeat sent successfully
2025-02-13 23:28:16 [INFO] 收到消息: start_task
2025-02-13 23:28:16 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 46, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:28:16 [INFO] iperf3 客户端启动配置:
2025-02-13 23:28:16 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:28:16 [INFO]   - 端口: 39999
2025-02-13 23:28:16 [INFO]   - 网卡: ens37
2025-02-13 23:28:16 [INFO]   - IPv6模式: True
2025-02-13 23:28:16 [INFO]   - 持续时间: 30秒
2025-02-13 23:28:16 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:28:37 [INFO] Program interrupted by user
2025-02-13 23:28:42 [INFO] Client starting...
2025-02-13 23:28:43 [INFO] Login successful
2025-02-13 23:28:43 [INFO] Checking registration status...
2025-02-13 23:28:43 [INFO] Client already registered
2025-02-13 23:28:43 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:28:43 [INFO] Starting heartbeat...
2025-02-13 23:28:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:28:43 [INFO] SSH tunnel established
2025-02-13 23:28:45 [DEBUG] Heartbeat sent successfully
2025-02-13 23:28:45 [INFO] 注册成功
2025-02-13 23:29:18 [INFO] 收到消息: start_task
2025-02-13 23:29:18 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 47, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:29:18 [INFO] iperf3 客户端启动配置:
2025-02-13 23:29:18 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:29:18 [INFO]   - 端口: 39999
2025-02-13 23:29:18 [INFO]   - 网卡: ens37
2025-02-13 23:29:18 [INFO]   - IPv6模式: True
2025-02-13 23:29:18 [INFO]   - 持续时间: 30秒
2025-02-13 23:29:18 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:29:46 [DEBUG] Heartbeat sent successfully
2025-02-13 23:29:48 [ERROR] iperf3 测试失败: None
2025-02-13 23:30:20 [INFO] 收到消息: start_task
2025-02-13 23:30:20 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 48, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:30:20 [INFO] iperf3 客户端启动配置:
2025-02-13 23:30:20 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:30:20 [INFO]   - 端口: 39999
2025-02-13 23:30:20 [INFO]   - 网卡: ens37
2025-02-13 23:30:20 [INFO]   - IPv6模式: True
2025-02-13 23:30:20 [INFO]   - 持续时间: 30秒
2025-02-13 23:30:20 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:30:26 [INFO] Program interrupted by user
2025-02-13 23:41:48 [INFO] Client starting...
2025-02-13 23:41:49 [INFO] Login successful
2025-02-13 23:41:49 [INFO] Checking registration status...
2025-02-13 23:41:49 [INFO] Client already registered
2025-02-13 23:41:49 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:41:49 [INFO] Starting heartbeat...
2025-02-13 23:41:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:41:49 [INFO] SSH tunnel established
2025-02-13 23:41:50 [DEBUG] Heartbeat sent successfully
2025-02-13 23:41:50 [INFO] 注册成功
2025-02-13 23:42:51 [DEBUG] Heartbeat sent successfully
2025-02-13 23:43:52 [DEBUG] Heartbeat sent successfully
2025-02-13 23:44:29 [INFO] 收到消息: start_task
2025-02-13 23:44:29 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 49, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:44:29 [INFO] iperf3 客户端启动配置:
2025-02-13 23:44:29 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:44:29 [INFO]   - 端口: 39999
2025-02-13 23:44:29 [INFO]   - 网卡: ens37
2025-02-13 23:44:29 [INFO]   - IPv6模式: True
2025-02-13 23:44:29 [INFO]   - 持续时间: 30秒
2025-02-13 23:44:29 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:44:54 [DEBUG] Heartbeat sent successfully
2025-02-13 23:44:59 [ERROR] iperf3 测试失败: None
2025-02-13 23:45:31 [INFO] 收到消息: start_task
2025-02-13 23:45:31 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 50, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:45:31 [INFO] iperf3 客户端启动配置:
2025-02-13 23:45:31 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:45:31 [INFO]   - 端口: 39999
2025-02-13 23:45:31 [INFO]   - 网卡: ens37
2025-02-13 23:45:31 [INFO]   - IPv6模式: True
2025-02-13 23:45:31 [INFO]   - 持续时间: 30秒
2025-02-13 23:45:31 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:45:55 [DEBUG] Heartbeat sent successfully
2025-02-13 23:46:02 [ERROR] iperf3 测试失败: None
2025-02-13 23:46:22 [ERROR] WebSocket连接断开，准备重连
2025-02-13 23:46:22 [INFO] WebSocket连接已安全关闭
2025-02-13 23:46:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:46:23 [INFO] 注册成功
2025-02-13 23:46:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:46:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 23:46:25 [INFO] WebSocket连接已安全关闭
2025-02-13 23:46:26 [INFO] Program interrupted by user
2025-02-13 23:50:48 [INFO] Client starting...
2025-02-13 23:50:48 [INFO] Login successful
2025-02-13 23:50:48 [INFO] Checking registration status...
2025-02-13 23:50:49 [INFO] Client already registered
2025-02-13 23:50:49 [INFO] Starting SSH tunnel to *************:40000
2025-02-13 23:50:49 [INFO] Starting heartbeat...
2025-02-13 23:50:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:50:49 [INFO] SSH tunnel established
2025-02-13 23:50:50 [DEBUG] Heartbeat sent successfully
2025-02-13 23:50:51 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-13 23:50:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-13 23:50:58 [INFO] 注册成功
2025-02-13 23:51:51 [DEBUG] Heartbeat sent successfully
2025-02-13 23:52:00 [INFO] 收到消息: start_task
2025-02-13 23:52:00 [INFO] 收到任务启动请求: task_id=68_client, config={'type': 'iperf3_client', 'server_ip': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999, 'result_id': 51, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'duration': 30, 'bandwidth': 1, 'network_interface': 'ens37', 'mode': 'rate', 'time': 30}
2025-02-13 23:52:00 [INFO] iperf3 客户端启动配置:
2025-02-13 23:52:00 [INFO]   - 服务器: 240e:44d:508:a16:4897:87ff:fe73:1cac
2025-02-13 23:52:00 [INFO]   - 端口: 39999
2025-02-13 23:52:00 [INFO]   - 网卡: ens37
2025-02-13 23:52:00 [INFO]   - IPv6模式: True
2025-02-13 23:52:00 [INFO]   - 持续时间: 30秒
2025-02-13 23:52:00 [INFO]   - 带宽限制: 1 Mbps
2025-02-13 23:52:30 [INFO] iperf3 测试完成
2025-02-13 23:52:30 [DEBUG] 测试结果: {'start': {'connected': [{'socket': 17, 'local_host': '240e:34c:70:bdb0:20c:29ff:fe8b:47f3', 'local_port': 52476, 'remote_host': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'remote_port': 39999}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'timestamp': {'time': 'Thu, 13 Feb 2025 15:52:00 GMT', 'timesecs': 1739461920}, 'connecting_to': {'host': '240e:44d:508:a16:4897:87ff:fe73:1cac', 'port': 39999}, 'cookie': 'r7ero6sgtluynar5ylkizimj23nnzen447g3', 'tcp_mss_default': 1400, 'target_bitrate': 1000000, 'fq_rate': 0, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 30, 'bytes': 0, 'blocks': 0, 'reverse': 0, 'tos': 0, 'target_bitrate': 1000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 17, 'start': 0, 'end': 1.000122, 'seconds': 1.0001219511032104, 'bytes': 131072, 'bits_per_second': 1048448.1405925958, 'retransmits': 0, 'snd_cwnd': 51800, 'snd_wnd': 240256, 'rtt': 24526, 'rttvar': 2333, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 0, 'end': 1.000122, 'seconds': 1.0001219511032104, 'bytes': 131072, 'bits_per_second': 1048448.1405925958, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 1.000122, 'end': 2.000095, 'seconds': 0.9999729990959167, 'bytes': 131072, 'bits_per_second': 1048604.3132644838, 'retransmits': 0, 'snd_cwnd': 53200, 'snd_wnd': 333440, 'rtt': 21793, 'rttvar': 2317, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 1.000122, 'end': 2.000095, 'seconds': 0.9999729990959167, 'bytes': 131072, 'bits_per_second': 1048604.3132644838, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 2.000095, 'end': 3.000216, 'seconds': 1.000120997428894, 'bytes': 131072, 'bits_per_second': 1048449.1403496915, 'retransmits': 0, 'snd_cwnd': 51800, 'snd_wnd': 600448, 'rtt': 20538, 'rttvar': 2813, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 2.000095, 'end': 3.000216, 'seconds': 1.000120997428894, 'bytes': 131072, 'bits_per_second': 1048449.1403496915, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 3.000216, 'end': 4.000132, 'seconds': 0.9999160170555115, 'bytes': 131072, 'bits_per_second': 1048664.0698963692, 'retransmits': 0, 'snd_cwnd': 53200, 'snd_wnd': 848384, 'rtt': 32307, 'rttvar': 6628, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 3.000216, 'end': 4.000132, 'seconds': 0.9999160170555115, 'bytes': 131072, 'bits_per_second': 1048664.0698963692, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 4.000132, 'end': 5.000159, 'seconds': 1.0000269412994385, 'bytes': 131072, 'bits_per_second': 1048547.7507610712, 'retransmits': 0, 'snd_cwnd': 54600, 'snd_wnd': 848384, 'rtt': 18604, 'rttvar': 256, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 4.000132, 'end': 5.000159, 'seconds': 1.0000269412994385, 'bytes': 131072, 'bits_per_second': 1048547.7507610712, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 5.000159, 'end': 6.000118, 'seconds': 0.9999589920043945, 'bytes': 131072, 'bits_per_second': 1048619.0017634162, 'retransmits': 0, 'snd_cwnd': 54600, 'snd_wnd': 848384, 'rtt': 14260, 'rttvar': 157, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 5.000159, 'end': 6.000118, 'seconds': 0.9999589920043945, 'bytes': 131072, 'bits_per_second': 1048619.0017634162, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 6.000118, 'end': 7.000124, 'seconds': 1.0000059604644775, 'bytes': 131072, 'bits_per_second': 1048569.7500372527, 'retransmits': 0, 'snd_cwnd': 56000, 'snd_wnd': 848384, 'rtt': 22639, 'rttvar': 1085, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 6.000118, 'end': 7.000124, 'seconds': 1.0000059604644775, 'bytes': 131072, 'bits_per_second': 1048569.7500372527, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 7.000124, 'end': 8.000202, 'seconds': 1.0000779628753662, 'bytes': 131072, 'bits_per_second': 1048494.2563729682, 'retransmits': 0, 'snd_cwnd': 56000, 'snd_wnd': 848384, 'rtt': 17002, 'rttvar': 674, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 7.000124, 'end': 8.000202, 'seconds': 1.0000779628753662, 'bytes': 131072, 'bits_per_second': 1048494.2563729682, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 8.000202, 'end': 9.000208, 'seconds': 1.0000059604644775, 'bytes': 131072, 'bits_per_second': 1048569.7500372527, 'retransmits': 0, 'snd_cwnd': 57400, 'snd_wnd': 848384, 'rtt': 28991, 'rttvar': 2687, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 8.000202, 'end': 9.000208, 'seconds': 1.0000059604644775, 'bytes': 131072, 'bits_per_second': 1048569.7500372527, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 9.000208, 'end': 10.000186, 'seconds': 0.999978005886078, 'bytes': 131072, 'bits_per_second': 1048599.0630072504, 'retransmits': 0, 'snd_cwnd': 58800, 'snd_wnd': 848384, 'rtt': 22083, 'rttvar': 5558, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 9.000208, 'end': 10.000186, 'seconds': 0.999978005886078, 'bytes': 131072, 'bits_per_second': 1048599.0630072504, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 10.000186, 'end': 11.000097, 'seconds': 0.9999110102653503, 'bytes': 131072, 'bits_per_second': 1048669.3208045936, 'retransmits': 0, 'snd_cwnd': 61600, 'snd_wnd': 848384, 'rtt': 18081, 'rttvar': 405, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 10.000186, 'end': 11.000097, 'seconds': 0.9999110102653503, 'bytes': 131072, 'bits_per_second': 1048669.3208045936, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 11.000097, 'end': 12.000097, 'seconds': 1, 'bytes': 131072, 'bits_per_second': 1048576, 'retransmits': 0, 'snd_cwnd': 61600, 'snd_wnd': 849408, 'rtt': 23446, 'rttvar': 1148, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 11.000097, 'end': 12.000097, 'seconds': 1, 'bytes': 131072, 'bits_per_second': 1048576, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 12.000097, 'end': 13.000122, 'seconds': 1.0000250339508057, 'bytes': 131072, 'bits_per_second': 1048549.7506571248, 'retransmits': 0, 'snd_cwnd': 63000, 'snd_wnd': 848640, 'rtt': 25235, 'rttvar': 1236, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 12.000097, 'end': 13.000122, 'seconds': 1.0000250339508057, 'bytes': 131072, 'bits_per_second': 1048549.7506571248, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 13.000122, 'end': 14.000096, 'seconds': 0.999974012374878, 'bytes': 131072, 'bits_per_second': 1048603.2507081812, 'retransmits': 0, 'snd_cwnd': 67200, 'snd_wnd': 848640, 'rtt': 19994, 'rttvar': 825, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 13.000122, 'end': 14.000096, 'seconds': 0.999974012374878, 'bytes': 131072, 'bits_per_second': 1048603.2507081812, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 14.000096, 'end': 15.000116, 'seconds': 1.0000200271606445, 'bytes': 131072, 'bits_per_second': 1048555.000420562, 'retransmits': 0, 'snd_cwnd': 64400, 'snd_wnd': 849408, 'rtt': 13314, 'rttvar': 635, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 14.000096, 'end': 15.000116, 'seconds': 1.0000200271606445, 'bytes': 131072, 'bits_per_second': 1048555.000420562, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 15.000116, 'end': 16.000096, 'seconds': 0.9999799728393555, 'bytes': 131072, 'bits_per_second': 1048597.0004205788, 'retransmits': 0, 'snd_cwnd': 65800, 'snd_wnd': 849408, 'rtt': 19114, 'rttvar': 877, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 15.000116, 'end': 16.000096, 'seconds': 0.9999799728393555, 'bytes': 131072, 'bits_per_second': 1048597.0004205788, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 16.000096, 'end': 17.000119, 'seconds': 1.0000230073928833, 'bytes': 131072, 'bits_per_second': 1048551.8755550405, 'retransmits': 0, 'snd_cwnd': 65800, 'snd_wnd': 848000, 'rtt': 12603, 'rttvar': 1205, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 16.000096, 'end': 17.000119, 'seconds': 1.0000230073928833, 'bytes': 131072, 'bits_per_second': 1048551.8755550405, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 17.000119, 'end': 18.000122, 'seconds': 1.0000029802322388, 'bytes': 131072, 'bits_per_second': 1048572.8750093132, 'retransmits': 0, 'snd_cwnd': 70000, 'snd_wnd': 849408, 'rtt': 25261, 'rttvar': 4108, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 17.000119, 'end': 18.000122, 'seconds': 1.0000029802322388, 'bytes': 131072, 'bits_per_second': 1048572.8750093132, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 18.000122, 'end': 19.000156, 'seconds': 1.000033974647522, 'bytes': 131072, 'bits_per_second': 1048540.3762103057, 'retransmits': 0, 'snd_cwnd': 68600, 'snd_wnd': 849408, 'rtt': 36264, 'rttvar': 4207, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 18.000122, 'end': 19.000156, 'seconds': 1.000033974647522, 'bytes': 131072, 'bits_per_second': 1048540.3762103057, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 19.000156, 'end': 20.000127, 'seconds': 0.9999709725379944, 'bytes': 131072, 'bits_per_second': 1048606.438383549, 'retransmits': 0, 'snd_cwnd': 21000, 'snd_wnd': 848128, 'rtt': 54916, 'rttvar': 22150, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 19.000156, 'end': 20.000127, 'seconds': 0.9999709725379944, 'bytes': 131072, 'bits_per_second': 1048606.438383549, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 20.000127, 'end': 21.00012, 'seconds': 0.9999930262565613, 'bytes': 131072, 'bits_per_second': 1048583.312550996, 'retransmits': 0, 'snd_cwnd': 28000, 'snd_wnd': 849408, 'rtt': 21129, 'rttvar': 7741, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 20.000127, 'end': 21.00012, 'seconds': 0.9999930262565613, 'bytes': 131072, 'bits_per_second': 1048583.312550996, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 21.00012, 'end': 22.000091, 'seconds': 0.9999709725379944, 'bytes': 0, 'bits_per_second': 0, 'retransmits': 0, 'snd_cwnd': 71400, 'snd_wnd': 849408, 'rtt': 20141, 'rttvar': 2706, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 21.00012, 'end': 22.000091, 'seconds': 0.9999709725379944, 'bytes': 0, 'bits_per_second': 0, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 22.000091, 'end': 23.00014, 'seconds': 1.0000489950180054, 'bytes': 131072, 'bits_per_second': 1048524.6275169958, 'retransmits': 0, 'snd_cwnd': 72800, 'snd_wnd': 849408, 'rtt': 21572, 'rttvar': 2241, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 22.000091, 'end': 23.00014, 'seconds': 1.0000489950180054, 'bytes': 131072, 'bits_per_second': 1048524.6275169958, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 23.00014, 'end': 24.00025, 'seconds': 1.0001100301742554, 'bytes': 131072, 'bits_per_second': 1048460.6376933347, 'retransmits': 0, 'snd_cwnd': 72800, 'snd_wnd': 849408, 'rtt': 29960, 'rttvar': 7640, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 23.00014, 'end': 24.00025, 'seconds': 1.0001100301742554, 'bytes': 131072, 'bits_per_second': 1048460.6376933347, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 24.00025, 'end': 25.000095, 'seconds': 0.999845027923584, 'bytes': 131072, 'bits_per_second': 1048738.5251868656, 'retransmits': 0, 'snd_cwnd': 74200, 'snd_wnd': 849408, 'rtt': 25224, 'rttvar': 1801, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 24.00025, 'end': 25.000095, 'seconds': 0.999845027923584, 'bytes': 131072, 'bits_per_second': 1048738.5251868656, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 25.000095, 'end': 26.000238, 'seconds': 1.000143051147461, 'bytes': 131072, 'bits_per_second': 1048426.0214546031, 'retransmits': 0, 'snd_cwnd': 77000, 'snd_wnd': 849408, 'rtt': 19689, 'rttvar': 1307, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 25.000095, 'end': 26.000238, 'seconds': 1.000143051147461, 'bytes': 131072, 'bits_per_second': 1048426.0214546031, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 26.000238, 'end': 27.000088, 'seconds': 0.9998499751091003, 'bytes': 131072, 'bits_per_second': 1048733.3361043318, 'retransmits': 0, 'snd_cwnd': 75600, 'snd_wnd': 849408, 'rtt': 17138, 'rttvar': 1256, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 26.000238, 'end': 27.000088, 'seconds': 0.9998499751091003, 'bytes': 131072, 'bits_per_second': 1048733.3361043318, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 27.000088, 'end': 28.000133, 'seconds': 1.0000449419021606, 'bytes': 131072, 'bits_per_second': 1048528.8771177919, 'retransmits': 0, 'snd_cwnd': 78400, 'snd_wnd': 848640, 'rtt': 18090, 'rttvar': 940, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 27.000088, 'end': 28.000133, 'seconds': 1.0000449419021606, 'bytes': 131072, 'bits_per_second': 1048528.8771177919, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 28.000133, 'end': 29.000126, 'seconds': 0.9999930262565613, 'bytes': 131072, 'bits_per_second': 1048583.312550996, 'retransmits': 0, 'snd_cwnd': 78400, 'snd_wnd': 849408, 'rtt': 34899, 'rttvar': 2179, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 28.000133, 'end': 29.000126, 'seconds': 0.9999930262565613, 'bytes': 131072, 'bits_per_second': 1048583.312550996, 'retransmits': 0, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 17, 'start': 29.000126, 'end': 30.000438, 'seconds': 1.0003119707107544, 'bytes': 131072, 'bits_per_second': 1048248.977021591, 'retransmits': 0, 'snd_cwnd': 78400, 'snd_wnd': 848640, 'rtt': 16549, 'rttvar': 558, 'pmtu': 1500, 'omitted': False, 'sender': True}], 'sum': {'start': 29.000126, 'end': 30.000438, 'seconds': 1.0003119707107544, 'bytes': 131072, 'bits_per_second': 1048248.977021591, 'retransmits': 0, 'omitted': False, 'sender': True}}], 'end': {'streams': [{'sender': {'socket': 17, 'start': 0, 'end': 30.000438, 'seconds': 30.000438, 'bytes': 3801088, 'bits_per_second': 1013608.6679801142, 'retransmits': 0, 'max_snd_cwnd': 78400, 'max_snd_wnd': 849408, 'max_rtt': 54916, 'min_rtt': 12603, 'mean_rtt': 23178, 'sender': True}, 'receiver': {'socket': 17, 'start': 0, 'end': 30.011847, 'seconds': 30.000438, 'bytes': 3801088, 'bits_per_second': 1013223.3447678179, 'sender': True}}], 'sum_sent': {'start': 0, 'end': 30.000438, 'seconds': 30.000438, 'bytes': 3801088, 'bits_per_second': 1013608.6679801142, 'retransmits': 0, 'sender': True}, 'sum_received': {'start': 0, 'end': 30.011847, 'seconds': 30.011847, 'bytes': 3801088, 'bits_per_second': 1013223.3447678179, 'sender': True}, 'cpu_utilization_percent': {'host_total': 101.03011921548332, 'host_user': 100.99635735920135, 'host_system': 0.0337585293351373, 'remote_total': 0.3996251483787667, 'remote_user': 0.16731710364647326, 'remote_system': 0.23230804473229347}, 'sender_tcp_congestion': 'cubic', 'receiver_tcp_congestion': 'cubic'}}
2025-02-13 23:52:52 [DEBUG] Heartbeat sent successfully
2025-02-13 23:53:53 [DEBUG] Heartbeat sent successfully
2025-02-13 23:54:54 [DEBUG] Heartbeat sent successfully
2025-02-13 23:55:56 [DEBUG] Heartbeat sent successfully
2025-02-13 23:56:57 [DEBUG] Heartbeat sent successfully
2025-02-13 23:57:58 [DEBUG] Heartbeat sent successfully
2025-02-13 23:58:59 [DEBUG] Heartbeat sent successfully
