2025-02-27 09:49:07 [ERROR] WebSocket连接断开，准备重连
2025-02-27 09:49:07 [INFO] WebSocket连接已安全关闭
2025-02-27 09:49:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:07 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 09:49:08 [ERROR] 获取私网IPv4地址失败: 2
2025-02-27 09:49:08 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 09:49:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:12 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 09:49:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:22 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 09:49:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:47 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 09:49:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 09:49:52 [INFO] 注册成功
2025-02-27 09:50:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:51:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:52:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:53:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:54:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:55:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:56:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:57:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:58:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 09:59:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:00:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:00:28 [INFO] 收到消息: start_task
2025-02-27 10:00:28 [INFO] 收到任务启动请求: task_id=87_client, config={'type': 'iperf3_client', 'server_ip': '240e:34c:71:59e0:20c:29ff:fe54:d98f', 'port': 39999, 'result_id': 144, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'bandwidth': None, 'network_interface': 'ens37', 'direction': 'download', 'interval': 5, 'mode': 'traffic', 'traffic': 500}
2025-02-27 10:00:28 [INFO] 启动 iperf3 客户端: iperf3 -c 240e:34c:71:59e0:20c:29ff:fe54:d98f -p 39999 --bind-dev ens37 --json -i 5 -6 --connect-timeout 20 -n 500M
2025-02-27 10:00:28 [INFO] iperf3 client result: {'start': {'connected': [], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64'}, 'intervals': [], 'end': {}, 'error': 'unable to connect to server - server may have stopped running or use a different port, firewall issue, etc.: Connection timed out'}
2025-02-27 10:01:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:02:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:03:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:04:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:05:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:06:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:07:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:08:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:09:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:10:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:11:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:12:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:13:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:14:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:15:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:16:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:17:44 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:18:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:19:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:20:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:21:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:22:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:23:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:24:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:26:11 [ERROR] Heartbeat failed with status code: 503
2025-02-27 10:26:41 [ERROR] WebSocket连接断开，准备重连
2025-02-27 10:26:41 [INFO] WebSocket连接已安全关闭
2025-02-27 10:26:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:26:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:26:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:26:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:26:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:26:51 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:26:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:26:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:01 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:12 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:27:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:22 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:27:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:27:57 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:02 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:13 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:28:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:28 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:33 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:38 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:28:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:28:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:14 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:29:14 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:39 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:39 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:44 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:29:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:29:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:09 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:15 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:30:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:45 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:30:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:30:55 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:16 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:31:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:16 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:21 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:26 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:31 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:51 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:31:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:31:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:01 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:11 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:11 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:17 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:32:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:22 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:32:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:32:57 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:02 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:18 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:33:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:28 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:33 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:38 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:33:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:33:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:19 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:34:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:39 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:39 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:44 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:34:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:34:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:20 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 10:35:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:35:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:35:45 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:50:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:50:36 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 10:50:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:50:41 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 10:50:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:50:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 10:50:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 10:50:59 [INFO] 注册成功
2025-02-27 10:51:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:52:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:53:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:54:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 10:55:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:41:52 [ERROR] WebSocket连接断开，准备重连
2025-02-27 11:41:52 [INFO] WebSocket连接已安全关闭
2025-02-27 11:41:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:41:53 [ERROR] 获取私网IPv4地址失败: 2
2025-02-27 11:41:53 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-27 11:42:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 11:42:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:42:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 11:42:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:42:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 11:42:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:42:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 11:42:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:42:20 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 11:42:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 11:42:25 [INFO] 注册成功
2025-02-27 11:42:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:43:56 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:44:57 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:45:59 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:47:00 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 11:48:01 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 13:54:50 [ERROR] WebSocket连接断开，准备重连
2025-02-27 13:54:50 [INFO] WebSocket连接已安全关闭
2025-02-27 13:54:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 13:54:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-27 13:54:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 13:54:55 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-27 13:55:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 13:55:00 [INFO] 注册成功
2025-02-27 13:55:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 13:56:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 13:57:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 13:58:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 13:59:30 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:00:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:00:32 [INFO] 收到消息: start_task
2025-02-27 14:00:32 [INFO] 收到任务启动请求: task_id=87_client, config={'type': 'iperf3_client', 'server_ip': '240e:34c:71:59e0:20c:29ff:fe54:d98f', 'port': 39999, 'result_id': 145, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'bandwidth': None, 'network_interface': 'ens37', 'direction': 'download', 'interval': 5, 'mode': 'traffic', 'traffic': 500}
2025-02-27 14:00:32 [INFO] 启动 iperf3 客户端: iperf3 -c 240e:34c:71:59e0:20c:29ff:fe54:d98f -p 39999 --bind-dev ens37 --json -i 5 -6 --connect-timeout 20 -n 500M
2025-02-27 14:00:32 [INFO] iperf3 client result: {'start': {'connected': [], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64'}, 'intervals': [], 'end': {}, 'error': 'unable to connect to server - server may have stopped running or use a different port, firewall issue, etc.: Connection timed out'}
2025-02-27 14:01:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:02:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:03:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:04:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:05:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:06:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:07:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:08:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:09:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:10:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:11:44 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:12:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:13:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:14:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:15:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:16:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:17:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:18:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:19:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:20:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:21:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:22:56 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:23:57 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:24:59 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:26:00 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:27:01 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:28:02 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:29:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:30:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:31:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:32:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:33:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:34:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:35:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:36:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:37:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:38:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:39:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:40:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:41:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:42:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:43:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:44:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:45:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:46:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:47:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:48:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:49:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:50:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:51:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:52:30 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:53:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:54:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:55:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:56:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:57:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:58:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 14:59:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:00:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:01:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:02:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:03:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:04:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:05:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:06:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:07:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:08:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:09:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:10:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:11:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:13:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:13:03 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:13:03 [INFO] WebSocket连接已安全关闭
2025-02-27 15:13:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:13:03 [INFO] 注册成功
2025-02-27 15:14:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:15:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:16:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:17:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:18:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:18:19 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:18:19 [INFO] WebSocket连接已安全关闭
2025-02-27 15:18:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:18:19 [INFO] 注册成功
2025-02-27 15:19:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:20:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:21:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:22:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:23:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:23:34 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:23:34 [INFO] WebSocket连接已安全关闭
2025-02-27 15:23:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:23:35 [INFO] 注册成功
2025-02-27 15:24:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:25:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:26:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:27:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:28:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:28:50 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:28:50 [INFO] WebSocket连接已安全关闭
2025-02-27 15:28:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:28:50 [INFO] 注册成功
2025-02-27 15:29:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:30:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:31:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:32:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:34:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:34:06 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:34:06 [INFO] WebSocket连接已安全关闭
2025-02-27 15:34:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:34:06 [INFO] 注册成功
2025-02-27 15:34:36 [ERROR] WebSocket连接断开，准备重连
2025-02-27 15:34:36 [INFO] WebSocket连接已安全关闭
2025-02-27 15:34:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 15:34:36 [INFO] 注册成功
2025-02-27 15:35:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:36:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:37:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:38:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:39:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:40:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:41:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:42:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:43:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:44:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:45:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:46:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:47:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:48:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:49:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:50:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:51:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:52:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:53:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:54:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:55:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:56:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:57:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:58:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 15:59:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:00:35 [INFO] 收到消息: start_task
2025-02-27 16:00:35 [INFO] 收到任务启动请求: task_id=87_client, config={'type': 'iperf3_client', 'server_ip': '240e:34c:71:59e0:20c:29ff:fe54:d98f', 'port': 39999, 'result_id': 146, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'bandwidth': None, 'network_interface': 'ens37', 'direction': 'download', 'interval': 5, 'mode': 'traffic', 'traffic': 500}
2025-02-27 16:00:35 [INFO] 启动 iperf3 客户端: iperf3 -c 240e:34c:71:59e0:20c:29ff:fe54:d98f -p 39999 --bind-dev ens37 --json -i 5 -6 --connect-timeout 20 -n 500M
2025-02-27 16:00:35 [INFO] iperf3 client result: {'start': {'connected': [], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64'}, 'intervals': [], 'end': {}, 'error': 'unable to connect to server - server may have stopped running or use a different port, firewall issue, etc.: Connection timed out'}
2025-02-27 16:00:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:01:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:02:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:03:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:04:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:05:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:06:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:07:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:08:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:09:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:10:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:11:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:12:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:13:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:14:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:15:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:16:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:17:56 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:18:57 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:19:59 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:21:00 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:22:01 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:23:02 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:24:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:25:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:26:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:27:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:28:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:29:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:30:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:31:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:32:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:33:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:34:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:35:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:36:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:37:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:38:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:39:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:40:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:41:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:42:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:43:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:44:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:45:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:45:38 [ERROR] WebSocket连接断开，准备重连
2025-02-27 16:45:38 [INFO] WebSocket连接已安全关闭
2025-02-27 16:45:38 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 16:45:38 [INFO] 注册成功
2025-02-27 16:46:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:47:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:48:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:49:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:50:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:50:54 [ERROR] WebSocket连接断开，准备重连
2025-02-27 16:50:54 [INFO] WebSocket连接已安全关闭
2025-02-27 16:50:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 16:50:54 [INFO] 注册成功
2025-02-27 16:51:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:52:56 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:53:57 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:54:58 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:56:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:56:10 [ERROR] WebSocket连接断开，准备重连
2025-02-27 16:56:10 [INFO] WebSocket连接已安全关闭
2025-02-27 16:56:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 16:56:10 [INFO] 注册成功
2025-02-27 16:57:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:58:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 16:59:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:00:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:01:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:02:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:03:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:04:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:05:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:06:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:06:31 [ERROR] WebSocket连接断开，准备重连
2025-02-27 17:06:31 [INFO] WebSocket连接已安全关闭
2025-02-27 17:06:31 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 17:06:32 [INFO] 注册成功
2025-02-27 17:07:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:08:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:09:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:10:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:11:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:12:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:13:39 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:14:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:15:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:16:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:17:44 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:18:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:19:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:20:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:21:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:22:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:23:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:24:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:25:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:27:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:27:04 [ERROR] WebSocket连接断开，准备重连
2025-02-27 17:27:04 [INFO] WebSocket连接已安全关闭
2025-02-27 17:27:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 17:27:05 [INFO] 注册成功
2025-02-27 17:28:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:29:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:30:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:31:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:32:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:33:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:34:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:35:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:36:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:37:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:37:26 [ERROR] WebSocket连接断开，准备重连
2025-02-27 17:37:26 [INFO] WebSocket连接已安全关闭
2025-02-27 17:37:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 17:37:26 [INFO] 注册成功
2025-02-27 17:38:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:39:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:40:30 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:41:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:42:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:43:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:44:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:45:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:46:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:47:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:47:48 [ERROR] WebSocket连接断开，准备重连
2025-02-27 17:47:48 [INFO] WebSocket连接已安全关闭
2025-02-27 17:47:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 17:47:48 [INFO] 注册成功
2025-02-27 17:48:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:49:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:50:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:51:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:53:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:53:04 [ERROR] WebSocket连接断开，准备重连
2025-02-27 17:53:04 [INFO] WebSocket连接已安全关闭
2025-02-27 17:53:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 17:53:04 [INFO] 注册成功
2025-02-27 17:54:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:55:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:56:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:57:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:58:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 17:59:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:00:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:00:38 [INFO] 收到消息: start_task
2025-02-27 18:00:38 [INFO] 收到任务启动请求: task_id=87_client, config={'type': 'iperf3_client', 'server_ip': '240e:34c:71:59e0:20c:29ff:fe54:d98f', 'port': 39999, 'result_id': 147, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'bandwidth': None, 'network_interface': 'ens37', 'direction': 'download', 'interval': 5, 'mode': 'traffic', 'traffic': 500}
2025-02-27 18:00:38 [INFO] 启动 iperf3 客户端: iperf3 -c 240e:34c:71:59e0:20c:29ff:fe54:d98f -p 39999 --bind-dev ens37 --json -i 5 -6 --connect-timeout 20 -n 500M
2025-02-27 18:00:38 [INFO] iperf3 client result: {'start': {'connected': [], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64'}, 'intervals': [], 'end': {}, 'error': 'unable to connect to server - server may have stopped running or use a different port, firewall issue, etc.: Connection timed out'}
2025-02-27 18:12:40 [ERROR] WebSocket连接断开，准备重连
2025-02-27 18:12:40 [INFO] WebSocket连接已安全关闭
2025-02-27 18:12:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 18:12:40 [INFO] 注册成功
2025-02-27 18:12:57 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:13:58 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:14:59 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:16:00 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:16:40 [ERROR] WebSocket连接断开，准备重连
2025-02-27 18:16:40 [INFO] WebSocket连接已安全关闭
2025-02-27 18:16:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 18:16:40 [INFO] 注册成功
2025-02-27 18:17:02 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:18:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:19:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:20:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:21:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:22:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:23:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:24:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:25:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:26:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:27:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:28:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:29:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:30:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:31:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:32:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:33:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-27 18:33:40 [ERROR] WebSocket连接断开，准备重连
2025-02-27 18:33:40 [INFO] WebSocket连接已安全关闭
2025-02-27 18:33:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-27 18:33:41 [INFO] 注册成功
