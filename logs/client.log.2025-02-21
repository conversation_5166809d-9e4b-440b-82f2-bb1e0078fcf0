2025-02-21 22:01:04 [INFO] Client starting...
2025-02-21 22:01:05 [INFO] Login successful
2025-02-21 22:01:05 [INFO] Checking registration status...
2025-02-21 22:01:05 [INFO] Client already registered
2025-02-21 22:01:05 [INFO] Starting SSH tunnel to *************:40000
2025-02-21 22:01:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-21 22:01:05 [INFO] Starting heartbeat...
2025-02-21 22:01:05 [INFO] SSH tunnel established
2025-02-21 22:01:06 [DEBUG] Heartbeat sent successfully
2025-02-21 22:01:06 [INFO] 注册成功
2025-02-21 22:02:07 [DEBUG] Heartbeat sent successfully
2025-02-21 22:03:08 [DEBUG] Heartbeat sent successfully
2025-02-21 22:04:09 [DEBUG] Heartbeat sent successfully
2025-02-21 22:05:11 [DEBUG] Heartbeat sent successfully
2025-02-21 22:05:56 [INFO] 收到消息: start_task
2025-02-21 22:05:56 [INFO] 收到任务启动请求: task_id=86_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 126, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 120, 'network_interface': 'ens37', 'interval': 8}
2025-02-21 22:05:56 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 8 -6 --one-off --idle-timeout 20
2025-02-21 22:06:12 [DEBUG] Heartbeat sent successfully
2025-02-21 22:07:13 [DEBUG] Heartbeat sent successfully
2025-02-21 22:07:59 [INFO] iperf3 server result: {'start': {'connected': [{'socket': 5, 'local_host': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'local_port': 39999, 'remote_host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'remote_port': 38530}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'target_bitrate': 10000000, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'timestamp': {'time': 'Fri, 21 Feb 2025 14:05:59 GMT', 'timesecs': 1740146759}, 'accepted_connection': {'host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'port': 38518}, 'cookie': '3w2tb7usba6fp65dxcmeq5h3akjjncqix4zh', 'tcp_mss_default': 0, 'fq_rate': 0, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 120, 'bytes': 0, 'blocks': 0, 'reverse': 0, 'tos': 0, 'target_bitrate': 10000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 5, 'start': 0, 'end': 8.008129, 'seconds': 8.008129119873047, 'bytes': 10092544, 'bits_per_second': 10082298.972881693, 'omitted': False, 'sender': False}], 'sum': {'start': 0, 'end': 8.008129, 'seconds': 8.008129119873047, 'bytes': 10092544, 'bits_per_second': 10082298.972881693, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 8.008129, 'end': 16.00762, 'seconds': 7.999491214752197, 'bytes': 9961472, 'bits_per_second': 9962105.57154398, 'omitted': False, 'sender': False}], 'sum': {'start': 8.008129, 'end': 16.00762, 'seconds': 7.999491214752197, 'bytes': 9961472, 'bits_per_second': 9962105.57154398, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 16.00762, 'end': 24.008204, 'seconds': 8.00058364868164, 'bytes': 9961472, 'bits_per_second': 9960745.303016966, 'omitted': False, 'sender': False}], 'sum': {'start': 16.00762, 'end': 24.008204, 'seconds': 8.00058364868164, 'bytes': 9961472, 'bits_per_second': 9960745.303016966, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 24.008204, 'end': 32.008085, 'seconds': 7.99988079071045, 'bytes': 10092544, 'bits_per_second': 10092694.392866028, 'omitted': False, 'sender': False}], 'sum': {'start': 24.008204, 'end': 32.008085, 'seconds': 7.99988079071045, 'bytes': 10092544, 'bits_per_second': 10092694.392866028, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 32.008085, 'end': 40.008254, 'seconds': 8.000168800354004, 'bytes': 9961472, 'bits_per_second': 9961261.81693487, 'omitted': False, 'sender': False}], 'sum': {'start': 32.008085, 'end': 40.008254, 'seconds': 8.000168800354004, 'bytes': 9961472, 'bits_per_second': 9961261.81693487, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 40.008254, 'end': 48.008265, 'seconds': 8.000011444091797, 'bytes': 9961472, 'bits_per_second': 9961457.750020385, 'omitted': False, 'sender': False}], 'sum': {'start': 40.008254, 'end': 48.008265, 'seconds': 8.000011444091797, 'bytes': 9961472, 'bits_per_second': 9961457.750020385, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 48.008265, 'end': 56.008198, 'seconds': 7.999932765960693, 'bytes': 9961472, 'bits_per_second': 9961555.7194536, 'omitted': False, 'sender': False}], 'sum': {'start': 48.008265, 'end': 56.008198, 'seconds': 7.999932765960693, 'bytes': 9961472, 'bits_per_second': 9961555.7194536, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 56.008198, 'end': 64.008235, 'seconds': 8.00003719329834, 'bytes': 10092544, 'bits_per_second': 10092497.078343146, 'omitted': False, 'sender': False}], 'sum': {'start': 56.008198, 'end': 64.008235, 'seconds': 8.00003719329834, 'bytes': 10092544, 'bits_per_second': 10092497.078343146, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 64.008235, 'end': 72.008146, 'seconds': 7.999910831451416, 'bytes': 9961472, 'bits_per_second': 9961583.032487575, 'omitted': False, 'sender': False}], 'sum': {'start': 64.008235, 'end': 72.008146, 'seconds': 7.999910831451416, 'bytes': 9961472, 'bits_per_second': 9961583.032487575, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 72.008146, 'end': 80.008637, 'seconds': 8.00049114227295, 'bytes': 9961472, 'bits_per_second': 9960860.47504322, 'omitted': False, 'sender': False}], 'sum': {'start': 72.008146, 'end': 80.008637, 'seconds': 8.00049114227295, 'bytes': 9961472, 'bits_per_second': 9960860.47504322, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 80.008637, 'end': 88.008134, 'seconds': 7.999496936798096, 'bytes': 10092544, 'bits_per_second': 10093178.688348545, 'omitted': False, 'sender': False}], 'sum': {'start': 80.008637, 'end': 88.008134, 'seconds': 7.999496936798096, 'bytes': 10092544, 'bits_per_second': 10093178.688348545, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 88.008134, 'end': 96.008102, 'seconds': 7.9999680519104, 'bytes': 9961472, 'bits_per_second': 9961511.781408867, 'omitted': False, 'sender': False}], 'sum': {'start': 88.008134, 'end': 96.008102, 'seconds': 7.9999680519104, 'bytes': 9961472, 'bits_per_second': 9961511.781408867, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 96.008102, 'end': 104.008189, 'seconds': 8.000086784362793, 'bytes': 9961472, 'bits_per_second': 9961363.938672254, 'omitted': False, 'sender': False}], 'sum': {'start': 96.008102, 'end': 104.008189, 'seconds': 8.000086784362793, 'bytes': 9961472, 'bits_per_second': 9961363.938672254, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 104.008189, 'end': 112.007593, 'seconds': 7.999403953552246, 'bytes': 10092544, 'bits_per_second': 10093296.009154048, 'omitted': False, 'sender': False}], 'sum': {'start': 104.008189, 'end': 112.007593, 'seconds': 7.999403953552246, 'bytes': 10092544, 'bits_per_second': 10093296.009154048, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 112.007593, 'end': 120.008644, 'seconds': 8.00105094909668, 'bytes': 9961472, 'bits_per_second': 9960163.5468897, 'omitted': False, 'sender': False}], 'sum': {'start': 112.007593, 'end': 120.008644, 'seconds': 8.00105094909668, 'bytes': 9961472, 'bits_per_second': 9960163.5468897, 'omitted': False, 'sender': False}}], 'end': {'streams': [{'sender': {'socket': 5, 'start': 0, 'end': 120.010406, 'seconds': 120.010406, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'receiver': {'socket': 5, 'start': 0, 'end': 120.010406, 'seconds': 120.010406, 'bytes': 150077440, 'bits_per_second': 10004295.127540857, 'sender': False}}], 'sum_sent': {'start': 0, 'end': 120.010406, 'seconds': 120.010406, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'sum_received': {'start': 0, 'end': 120.010406, 'seconds': 120.010406, 'bytes': 150077440, 'bits_per_second': 10004295.127540857, 'sender': False}, 'cpu_utilization_percent': {'host_total': 1.7715936995617891, 'host_user': 0.046703335619240616, 'host_system': 1.7248895306835141, 'remote_total': 0, 'remote_user': 0, 'remote_system': 0}, 'receiver_tcp_congestion': 'cubic'}}
2025-02-21 22:08:14 [DEBUG] Heartbeat sent successfully
2025-02-21 22:09:15 [DEBUG] Heartbeat sent successfully
2025-02-21 22:10:17 [DEBUG] Heartbeat sent successfully
2025-02-21 22:11:18 [DEBUG] Heartbeat sent successfully
2025-02-21 22:12:19 [DEBUG] Heartbeat sent successfully
2025-02-21 22:13:20 [DEBUG] Heartbeat sent successfully
2025-02-21 22:14:04 [INFO] Program interrupted by user
2025-02-21 22:14:20 [INFO] Client starting...
2025-02-21 22:14:21 [INFO] Login successful
2025-02-21 22:14:21 [INFO] Checking registration status...
2025-02-21 22:14:21 [INFO] Client already registered
2025-02-21 22:14:21 [INFO] Starting SSH tunnel to *************:40000
2025-02-21 22:14:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-21 22:14:21 [INFO] Starting heartbeat...
2025-02-21 22:14:21 [INFO] SSH tunnel established
2025-02-21 22:14:22 [DEBUG] Heartbeat sent successfully
2025-02-21 22:14:22 [INFO] 注册成功
2025-02-21 22:15:23 [DEBUG] Heartbeat sent successfully
2025-02-21 22:16:25 [DEBUG] Heartbeat sent successfully
2025-02-21 22:17:26 [DEBUG] Heartbeat sent successfully
2025-02-21 22:18:27 [DEBUG] Heartbeat sent successfully
2025-02-21 22:19:28 [DEBUG] Heartbeat sent successfully
2025-02-21 22:20:29 [DEBUG] Heartbeat sent successfully
2025-02-21 22:21:30 [DEBUG] Heartbeat sent successfully
2025-02-21 22:22:31 [DEBUG] Heartbeat sent successfully
2025-02-21 22:23:33 [DEBUG] Heartbeat sent successfully
2025-02-21 22:24:34 [DEBUG] Heartbeat sent successfully
2025-02-21 22:25:35 [DEBUG] Heartbeat sent successfully
2025-02-21 22:26:36 [DEBUG] Heartbeat sent successfully
2025-02-21 22:27:37 [DEBUG] Heartbeat sent successfully
2025-02-21 22:28:38 [DEBUG] Heartbeat sent successfully
2025-02-21 22:29:39 [DEBUG] Heartbeat sent successfully
2025-02-21 22:30:41 [DEBUG] Heartbeat sent successfully
2025-02-21 22:31:42 [DEBUG] Heartbeat sent successfully
2025-02-21 22:32:43 [DEBUG] Heartbeat sent successfully
2025-02-21 22:33:44 [DEBUG] Heartbeat sent successfully
2025-02-21 22:34:45 [DEBUG] Heartbeat sent successfully
2025-02-21 22:35:46 [DEBUG] Heartbeat sent successfully
2025-02-21 22:36:48 [DEBUG] Heartbeat sent successfully
2025-02-21 22:37:49 [DEBUG] Heartbeat sent successfully
2025-02-21 22:38:50 [DEBUG] Heartbeat sent successfully
2025-02-21 22:39:51 [DEBUG] Heartbeat sent successfully
2025-02-21 22:40:52 [DEBUG] Heartbeat sent successfully
2025-02-21 22:41:53 [DEBUG] Heartbeat sent successfully
2025-02-21 22:42:54 [DEBUG] Heartbeat sent successfully
2025-02-21 22:43:56 [DEBUG] Heartbeat sent successfully
2025-02-21 22:44:57 [DEBUG] Heartbeat sent successfully
2025-02-21 22:45:58 [DEBUG] Heartbeat sent successfully
2025-02-21 22:46:59 [DEBUG] Heartbeat sent successfully
2025-02-21 22:48:00 [DEBUG] Heartbeat sent successfully
2025-02-21 22:49:01 [DEBUG] Heartbeat sent successfully
2025-02-21 22:50:03 [DEBUG] Heartbeat sent successfully
2025-02-21 22:51:04 [DEBUG] Heartbeat sent successfully
2025-02-21 22:52:05 [DEBUG] Heartbeat sent successfully
2025-02-21 22:53:06 [DEBUG] Heartbeat sent successfully
2025-02-21 22:54:07 [DEBUG] Heartbeat sent successfully
2025-02-21 22:55:08 [DEBUG] Heartbeat sent successfully
2025-02-21 22:56:21 [ERROR] Heartbeat failed with status code: 500
2025-02-21 22:56:21 [ERROR] WebSocket连接断开，准备重连
2025-02-21 22:56:21 [INFO] WebSocket连接已安全关闭
2025-02-21 22:56:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-21 22:56:21 [INFO] 注册成功
2025-02-21 22:57:32 [ERROR] Heartbeat failed with status code: 500
2025-02-21 22:57:32 [ERROR] WebSocket连接断开，准备重连
2025-02-21 22:57:32 [INFO] WebSocket连接已安全关闭
2025-02-21 22:57:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-21 22:57:33 [INFO] 注册成功
2025-02-21 22:58:34 [DEBUG] Heartbeat sent successfully
2025-02-21 22:59:35 [DEBUG] Heartbeat sent successfully
2025-02-21 23:00:47 [ERROR] Heartbeat failed with status code: 500
2025-02-21 23:00:47 [ERROR] WebSocket连接断开，准备重连
2025-02-21 23:00:47 [INFO] WebSocket连接已安全关闭
2025-02-21 23:00:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-21 23:00:48 [INFO] 注册成功
2025-02-21 23:01:49 [ERROR] Heartbeat failed with status code: 503
2025-02-21 23:02:51 [DEBUG] Heartbeat sent successfully
2025-02-21 23:03:52 [DEBUG] Heartbeat sent successfully
2025-02-21 23:04:53 [DEBUG] Heartbeat sent successfully
2025-02-21 23:05:54 [DEBUG] Heartbeat sent successfully
2025-02-21 23:06:55 [DEBUG] Heartbeat sent successfully
2025-02-21 23:07:56 [DEBUG] Heartbeat sent successfully
2025-02-21 23:08:58 [DEBUG] Heartbeat sent successfully
2025-02-21 23:09:59 [DEBUG] Heartbeat sent successfully
2025-02-21 23:11:00 [DEBUG] Heartbeat sent successfully
2025-02-21 23:12:01 [DEBUG] Heartbeat sent successfully
2025-02-21 23:13:03 [DEBUG] Heartbeat sent successfully
2025-02-21 23:14:04 [DEBUG] Heartbeat sent successfully
2025-02-21 23:15:05 [DEBUG] Heartbeat sent successfully
2025-02-21 23:16:06 [DEBUG] Heartbeat sent successfully
2025-02-21 23:17:07 [DEBUG] Heartbeat sent successfully
2025-02-21 23:18:09 [DEBUG] Heartbeat sent successfully
2025-02-21 23:19:10 [DEBUG] Heartbeat sent successfully
2025-02-21 23:20:11 [DEBUG] Heartbeat sent successfully
2025-02-21 23:21:12 [DEBUG] Heartbeat sent successfully
2025-02-21 23:22:13 [DEBUG] Heartbeat sent successfully
2025-02-21 23:23:14 [DEBUG] Heartbeat sent successfully
2025-02-21 23:24:16 [DEBUG] Heartbeat sent successfully
2025-02-21 23:25:17 [DEBUG] Heartbeat sent successfully
2025-02-21 23:26:18 [DEBUG] Heartbeat sent successfully
2025-02-21 23:27:19 [DEBUG] Heartbeat sent successfully
2025-02-21 23:28:20 [DEBUG] Heartbeat sent successfully
2025-02-21 23:29:21 [DEBUG] Heartbeat sent successfully
2025-02-21 23:30:22 [DEBUG] Heartbeat sent successfully
2025-02-21 23:31:24 [DEBUG] Heartbeat sent successfully
2025-02-21 23:32:25 [DEBUG] Heartbeat sent successfully
2025-02-21 23:33:26 [DEBUG] Heartbeat sent successfully
2025-02-21 23:34:27 [DEBUG] Heartbeat sent successfully
2025-02-21 23:35:28 [DEBUG] Heartbeat sent successfully
2025-02-21 23:36:29 [DEBUG] Heartbeat sent successfully
2025-02-21 23:37:30 [DEBUG] Heartbeat sent successfully
2025-02-21 23:38:32 [DEBUG] Heartbeat sent successfully
2025-02-21 23:39:33 [DEBUG] Heartbeat sent successfully
2025-02-21 23:40:34 [DEBUG] Heartbeat sent successfully
2025-02-21 23:41:35 [DEBUG] Heartbeat sent successfully
2025-02-21 23:42:36 [DEBUG] Heartbeat sent successfully
2025-02-21 23:43:37 [DEBUG] Heartbeat sent successfully
2025-02-21 23:44:39 [DEBUG] Heartbeat sent successfully
2025-02-21 23:45:40 [DEBUG] Heartbeat sent successfully
2025-02-21 23:46:41 [DEBUG] Heartbeat sent successfully
2025-02-21 23:47:42 [DEBUG] Heartbeat sent successfully
2025-02-21 23:48:43 [DEBUG] Heartbeat sent successfully
2025-02-21 23:49:44 [DEBUG] Heartbeat sent successfully
2025-02-21 23:50:45 [DEBUG] Heartbeat sent successfully
2025-02-21 23:51:47 [DEBUG] Heartbeat sent successfully
2025-02-21 23:52:48 [DEBUG] Heartbeat sent successfully
2025-02-21 23:53:49 [DEBUG] Heartbeat sent successfully
2025-02-21 23:54:50 [DEBUG] Heartbeat sent successfully
2025-02-21 23:55:51 [DEBUG] Heartbeat sent successfully
2025-02-21 23:56:52 [DEBUG] Heartbeat sent successfully
2025-02-21 23:57:53 [DEBUG] Heartbeat sent successfully
2025-02-21 23:58:55 [DEBUG] Heartbeat sent successfully
2025-02-21 23:59:56 [DEBUG] Heartbeat sent successfully
