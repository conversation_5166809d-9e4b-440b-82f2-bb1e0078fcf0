2025-02-20 00:00:15 [DEBUG] Heartbeat sent successfully
2025-02-20 00:01:16 [DEBUG] Heartbeat sent successfully
2025-02-20 00:02:17 [DEBUG] Heartbeat sent successfully
2025-02-20 00:03:18 [DEBUG] Heartbeat sent successfully
2025-02-20 00:04:19 [DEBUG] Heartbeat sent successfully
2025-02-20 00:05:20 [DEBUG] Heartbeat sent successfully
2025-02-20 00:06:22 [DEBUG] Heartbeat sent successfully
2025-02-20 00:07:23 [DEBUG] Heartbeat sent successfully
2025-02-20 00:08:24 [DEBUG] Heartbeat sent successfully
2025-02-20 00:09:25 [DEBUG] Heartbeat sent successfully
2025-02-20 00:10:26 [DEBUG] Heartbeat sent successfully
2025-02-20 00:11:27 [DEBUG] Heartbeat sent successfully
2025-02-20 00:12:29 [DEBUG] Heartbeat sent successfully
2025-02-20 00:13:30 [DEBUG] Heartbeat sent successfully
2025-02-20 00:14:31 [DEBUG] Heartbeat sent successfully
2025-02-20 00:15:32 [DEBUG] <PERSON>beat sent successfully
2025-02-20 00:16:33 [DEBUG] <PERSON>beat sent successfully
2025-02-20 00:17:34 [DEBUG] <PERSON><PERSON> sent successfully
2025-02-20 00:18:36 [DEBUG] Heartbeat sent successfully
2025-02-20 00:19:37 [DEBUG] Heartbeat sent successfully
2025-02-20 00:20:38 [DEBUG] Heartbeat sent successfully
2025-02-20 00:21:39 [DEBUG] Heartbeat sent successfully
2025-02-20 00:22:40 [DEBUG] Heartbeat sent successfully
2025-02-20 00:23:41 [DEBUG] Heartbeat sent successfully
2025-02-20 00:24:43 [DEBUG] Heartbeat sent successfully
2025-02-20 00:25:44 [DEBUG] Heartbeat sent successfully
2025-02-20 00:26:45 [DEBUG] Heartbeat sent successfully
2025-02-20 00:26:49 [INFO] 收到消息: start_task
2025-02-20 00:26:49 [INFO] 收到任务启动请求: task_id=79_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 119, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 00:26:49 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6
2025-02-20 00:27:46 [DEBUG] Heartbeat sent successfully
2025-02-20 00:28:48 [DEBUG] Heartbeat sent successfully
2025-02-20 00:29:49 [DEBUG] Heartbeat sent successfully
2025-02-20 00:30:49 [INFO] Client starting...
2025-02-20 00:30:50 [INFO] Login successful
2025-02-20 00:30:50 [INFO] Checking registration status...
2025-02-20 00:30:52 [INFO] Client already registered
2025-02-20 00:30:52 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:30:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:30:52 [INFO] Starting heartbeat...
2025-02-20 00:30:52 [INFO] SSH tunnel established
2025-02-20 00:30:53 [DEBUG] Heartbeat sent successfully
2025-02-20 00:30:53 [INFO] 注册成功
2025-02-20 00:31:20 [INFO] 收到消息: start_task
2025-02-20 00:31:20 [INFO] 发现已存在的 iperf3 进程(PID:157824),准备清理...
2025-02-20 00:31:20 [INFO] 已终止 iperf3 进程 157824
2025-02-20 00:31:20 [INFO] 收到任务启动请求: task_id=79_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 120, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 00:31:20 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 00:31:40 [ERROR] 解析 iperf3 输出失败: Expecting value: line 1 column 1 (char 0)
2025-02-20 00:31:54 [DEBUG] Heartbeat sent successfully
2025-02-20 00:32:34 [ERROR] WebSocket连接断开，准备重连
2025-02-20 00:32:34 [INFO] WebSocket连接已安全关闭
2025-02-20 00:32:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:32:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:32:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:32:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:32:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:32:51 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:32:56 [DEBUG] Heartbeat sent successfully
2025-02-20 00:32:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:32:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:39 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:33:55 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:33:57 [DEBUG] Heartbeat sent successfully
2025-02-20 00:34:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:44 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:34:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:34:58 [DEBUG] Heartbeat sent successfully
2025-02-20 00:34:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:22 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:46 [INFO] Client starting...
2025-02-20 00:35:47 [INFO] Login successful
2025-02-20 00:35:47 [INFO] Checking registration status...
2025-02-20 00:35:47 [INFO] Client already registered
2025-02-20 00:35:47 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:35:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:47 [INFO] Starting heartbeat...
2025-02-20 00:35:47 [INFO] SSH tunnel established
2025-02-20 00:35:47 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:35:47 [INFO] SSH tunnel stopped
2025-02-20 00:35:47 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:35:47 [INFO] SSH tunnel established
2025-02-20 00:35:48 [DEBUG] Heartbeat sent successfully
2025-02-20 00:35:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:52 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:35:52 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:35:52 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:35:52 [INFO] SSH tunnel established
2025-02-20 00:35:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:35:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:57 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:35:57 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:35:57 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:35:57 [INFO] SSH tunnel established
2025-02-20 00:35:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:35:59 [DEBUG] Heartbeat sent successfully
2025-02-20 00:36:01 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:02 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:02 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:02 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:02 [INFO] SSH tunnel established
2025-02-20 00:36:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:07 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:07 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:07 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:07 [INFO] SSH tunnel established
2025-02-20 00:36:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:12 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:12 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:12 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:12 [INFO] SSH tunnel established
2025-02-20 00:36:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:17 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:17 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:17 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:17 [INFO] SSH tunnel established
2025-02-20 00:36:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:22 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:22 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:22 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:22 [INFO] SSH tunnel established
2025-02-20 00:36:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:26 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:27 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:27 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:27 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:27 [INFO] SSH tunnel established
2025-02-20 00:36:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:31 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:32 [ERROR] SSH tunnel died, restarting...
2025-02-20 00:36:32 [ERROR] Error stopping SSH tunnel: 
2025-02-20 00:36:32 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:36:32 [INFO] SSH tunnel established
2025-02-20 00:36:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:36 [INFO] Program interrupted by user
2025-02-20 00:36:39 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:36:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:36:55 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:00 [DEBUG] Heartbeat sent successfully
2025-02-20 00:37:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:02 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:43 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:37:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:37:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:01 [DEBUG] Heartbeat sent successfully
2025-02-20 00:38:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:21 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:43 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 00:38:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:38:57 [INFO] 注册成功
2025-02-20 00:39:03 [DEBUG] Heartbeat sent successfully
2025-02-20 00:40:04 [DEBUG] Heartbeat sent successfully
2025-02-20 00:41:05 [DEBUG] Heartbeat sent successfully
2025-02-20 00:42:06 [DEBUG] Heartbeat sent successfully
2025-02-20 00:43:07 [DEBUG] Heartbeat sent successfully
2025-02-20 00:43:52 [INFO] 收到消息: start_task
2025-02-20 00:43:52 [INFO] 收到任务启动请求: task_id=79_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 121, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 00:43:52 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 00:44:08 [DEBUG] Heartbeat sent successfully
2025-02-20 00:44:12 [ERROR] 解析 iperf3 输出失败: Expecting value: line 1 column 1 (char 0)
2025-02-20 00:48:29 [INFO] Client starting...
2025-02-20 00:48:29 [INFO] Login successful
2025-02-20 00:48:29 [INFO] Checking registration status...
2025-02-20 00:48:30 [INFO] Client already registered
2025-02-20 00:48:30 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:48:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:48:30 [INFO] Starting heartbeat...
2025-02-20 00:48:30 [INFO] SSH tunnel established
2025-02-20 00:48:31 [DEBUG] Heartbeat sent successfully
2025-02-20 00:48:31 [INFO] 注册成功
2025-02-20 00:48:54 [INFO] 收到消息: start_task
2025-02-20 00:48:54 [INFO] 收到任务启动请求: task_id=79_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 122, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 00:48:54 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 00:49:32 [DEBUG] Heartbeat sent successfully
2025-02-20 00:50:01 [INFO] iperf3 server result: {'start': {'connected': [{'socket': 5, 'local_host': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'local_port': 39999, 'remote_host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'remote_port': 59208}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'target_bitrate': 50000000, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'timestamp': {'time': 'Wed, 19 Feb 2025 16:48:56 GMT', 'timesecs': 1739983736}, 'accepted_connection': {'host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'port': 59196}, 'cookie': '4sj6xt456nfioqggnkrtubhehfundovsunav', 'tcp_mss_default': 0, 'fq_rate': 0, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 3600, 'bytes': 0, 'blocks': 0, 'reverse': 0, 'tos': 0, 'target_bitrate': 50000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 5, 'start': 0, 'end': 4.004165, 'seconds': 4.004165172576904, 'bytes': 22937600, 'bits_per_second': 45827480.15909318, 'omitted': False, 'sender': False}], 'sum': {'start': 0, 'end': 4.004165, 'seconds': 4.004165172576904, 'bytes': 22937600, 'bits_per_second': 45827480.15909318, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 4.004165, 'end': 8.004207, 'seconds': 4.000041961669922, 'bytes': 22937600, 'bits_per_second': 45874718.75504846, 'omitted': False, 'sender': False}], 'sum': {'start': 4.004165, 'end': 8.004207, 'seconds': 4.000041961669922, 'bytes': 22937600, 'bits_per_second': 45874718.75504846, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 8.004207, 'end': 12.004139, 'seconds': 3.999932050704956, 'bytes': 23199744, 'bits_per_second': 46400276.2165147, 'omitted': False, 'sender': False}], 'sum': {'start': 8.004207, 'end': 12.004139, 'seconds': 3.999932050704956, 'bytes': 23199744, 'bits_per_second': 46400276.2165147, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 12.004139, 'end': 16.004202, 'seconds': 4.000062942504883, 'bytes': 23199744, 'bits_per_second': 46398757.8864888, 'omitted': False, 'sender': False}], 'sum': {'start': 12.004139, 'end': 16.004202, 'seconds': 4.000062942504883, 'bytes': 23199744, 'bits_per_second': 46398757.8864888, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 16.004202, 'end': 20.004204, 'seconds': 4.000001907348633, 'bytes': 22937600, 'bits_per_second': 45875178.12501043, 'omitted': False, 'sender': False}], 'sum': {'start': 16.004202, 'end': 20.004204, 'seconds': 4.000001907348633, 'bytes': 22937600, 'bits_per_second': 45875178.12501043, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 20.004204, 'end': 24.004231, 'seconds': 4.000027179718018, 'bytes': 22806528, 'bits_per_second': 45612746.06460599, 'omitted': False, 'sender': False}], 'sum': {'start': 20.004204, 'end': 24.004231, 'seconds': 4.000027179718018, 'bytes': 22806528, 'bits_per_second': 45612746.06460599, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 24.004231, 'end': 28.004205, 'seconds': 3.999974012374878, 'bytes': 22675456, 'bits_per_second': 45351206.64253926, 'omitted': False, 'sender': False}], 'sum': {'start': 24.004231, 'end': 28.004205, 'seconds': 3.999974012374878, 'bytes': 22675456, 'bits_per_second': 45351206.64253926, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 28.004205, 'end': 32.00415, 'seconds': 3.9999449253082275, 'bytes': 23068672, 'bits_per_second': 46137979.25874667, 'omitted': False, 'sender': False}], 'sum': {'start': 28.004205, 'end': 32.00415, 'seconds': 3.9999449253082275, 'bytes': 23068672, 'bits_per_second': 46137979.25874667, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 32.00415, 'end': 36.004111, 'seconds': 3.9999608993530273, 'bytes': 22937600, 'bits_per_second': 45875648.4418836, 'omitted': False, 'sender': False}], 'sum': {'start': 32.00415, 'end': 36.004111, 'seconds': 3.9999608993530273, 'bytes': 22937600, 'bits_per_second': 45875648.4418836, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 36.004111, 'end': 40.001687, 'seconds': 3.9975759983062744, 'bytes': 20316160, 'bits_per_second': 40656958.13384454, 'omitted': False, 'sender': False}], 'sum': {'start': 36.004111, 'end': 40.001687, 'seconds': 3.9975759983062744, 'bytes': 20316160, 'bits_per_second': 40656958.13384454, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 40.001687, 'end': 44.004065, 'seconds': 4.00237798690796, 'bytes': 25690112, 'bits_per_second': 51349696.773336336, 'omitted': False, 'sender': False}], 'sum': {'start': 40.001687, 'end': 44.004065, 'seconds': 4.00237798690796, 'bytes': 25690112, 'bits_per_second': 51349696.773336336, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 44.004065, 'end': 48.004255, 'seconds': 4.000189781188965, 'bytes': 23068672, 'bits_per_second': 46135155.10385283, 'omitted': False, 'sender': False}], 'sum': {'start': 44.004065, 'end': 48.004255, 'seconds': 4.000189781188965, 'bytes': 23068672, 'bits_per_second': 46135155.10385283, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 48.004255, 'end': 52.004127, 'seconds': 3.9998719692230225, 'bytes': 22937600, 'bits_per_second': 45876668.4063753, 'omitted': False, 'sender': False}], 'sum': {'start': 48.004255, 'end': 52.004127, 'seconds': 3.9998719692230225, 'bytes': 22937600, 'bits_per_second': 45876668.4063753, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 52.004127, 'end': 56.004093, 'seconds': 3.9999659061431885, 'bytes': 22937600, 'bits_per_second': 45875591.01895784, 'omitted': False, 'sender': False}], 'sum': {'start': 52.004127, 'end': 56.004093, 'seconds': 3.9999659061431885, 'bytes': 22937600, 'bits_per_second': 45875591.01895784, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 56.004093, 'end': 60.004086, 'seconds': 3.999993085861206, 'bytes': 23068672, 'bits_per_second': 46137423.75013785, 'omitted': False, 'sender': False}], 'sum': {'start': 56.004093, 'end': 60.004086, 'seconds': 3.999993085861206, 'bytes': 23068672, 'bits_per_second': 46137423.75013785, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 60.004086, 'end': 64.004185, 'seconds': 4.000099182128906, 'bytes': 22937600, 'bits_per_second': 45874062.52820422, 'omitted': False, 'sender': False}], 'sum': {'start': 60.004086, 'end': 64.004185, 'seconds': 4.000099182128906, 'bytes': 22937600, 'bits_per_second': 45874062.52820422, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 60.004086, 'end': 64.004185, 'seconds': 4.000099182128906, 'bytes': 22937600, 'bits_per_second': 45874062.52820422, 'omitted': False, 'sender': False}], 'sum': {'start': 60.004086, 'end': 64.004185, 'seconds': 4.000099182128906, 'bytes': 22937600, 'bits_per_second': 45874062.52820422, 'omitted': False, 'sender': False}}], 'end': {'streams': [{'sender': {'socket': 5, 'start': 0, 'end': 64.004185, 'seconds': 64.004185, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'receiver': {'socket': 5, 'start': 0, 'end': 64.004185, 'seconds': 64.004185, 'bytes': 371688824, 'bits_per_second': 46458065.07808825, 'sender': False}}], 'sum_sent': {'start': 0, 'end': 64.004185, 'seconds': 64.004185, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'sum_received': {'start': 0, 'end': 64.004185, 'seconds': 64.004185, 'bytes': 371688824, 'bits_per_second': 46458065.07808825, 'sender': False}, 'cpu_utilization_percent': {'host_total': 18.600071588219713, 'host_user': 0.4658659973392777, 'host_system': 18.134208682442868, 'remote_total': 0, 'remote_user': 0, 'remote_system': 0}, 'receiver_tcp_congestion': 'cubic'}, 'error': 'the client has terminated'}
2025-02-20 00:50:04 [INFO] Program interrupted by user
2025-02-20 00:57:01 [INFO] Client starting...
2025-02-20 00:57:01 [INFO] Login successful
2025-02-20 00:57:01 [INFO] Checking registration status...
2025-02-20 00:57:02 [INFO] Client already registered
2025-02-20 00:57:02 [INFO] Starting SSH tunnel to *************:40000
2025-02-20 00:57:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 00:57:02 [INFO] Starting heartbeat...
2025-02-20 00:57:02 [INFO] SSH tunnel established
2025-02-20 00:57:03 [DEBUG] Heartbeat sent successfully
2025-02-20 00:57:03 [INFO] 注册成功
2025-02-20 00:57:54 [INFO] 收到消息: start_task
2025-02-20 00:57:54 [INFO] 收到任务启动请求: task_id=79_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 123, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 00:57:54 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 00:58:04 [DEBUG] Heartbeat sent successfully
2025-02-20 00:58:56 [INFO] iperf3 server result: {'start': {'connected': [{'socket': 5, 'local_host': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'local_port': 39999, 'remote_host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'remote_port': 58762}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'target_bitrate': 50000000, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'timestamp': {'time': 'Wed, 19 Feb 2025 16:57:56 GMT', 'timesecs': 1739984276}, 'accepted_connection': {'host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'port': 58760}, 'cookie': 'xd5igg54msiptfkdcmq7hpgcp5hdbw6splff', 'tcp_mss_default': 0, 'fq_rate': 0, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 60, 'bytes': 0, 'blocks': 0, 'reverse': 0, 'tos': 0, 'target_bitrate': 50000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 5, 'start': 0, 'end': 4.004181, 'seconds': 4.004180908203125, 'bytes': 22937600, 'bits_per_second': 45827300.06630643, 'omitted': False, 'sender': False}], 'sum': {'start': 0, 'end': 4.004181, 'seconds': 4.004180908203125, 'bytes': 22937600, 'bits_per_second': 45827300.06630643, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 4.004181, 'end': 8.003967, 'seconds': 3.999785900115967, 'bytes': 22937600, 'bits_per_second': 45877655.60018593, 'omitted': False, 'sender': False}], 'sum': {'start': 4.004181, 'end': 8.003967, 'seconds': 3.999785900115967, 'bytes': 22937600, 'bits_per_second': 45877655.60018593, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 8.003967, 'end': 12.004099, 'seconds': 4.000132083892822, 'bytes': 23068672, 'bits_per_second': 46135820.5503058, 'omitted': False, 'sender': False}], 'sum': {'start': 8.003967, 'end': 12.004099, 'seconds': 4.000132083892822, 'bytes': 23068672, 'bits_per_second': 46135820.5503058, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 12.004099, 'end': 16.004189, 'seconds': 4.0000901222229, 'bytes': 22937600, 'bits_per_second': 45874166.42953692, 'omitted': False, 'sender': False}], 'sum': {'start': 12.004099, 'end': 16.004189, 'seconds': 4.0000901222229, 'bytes': 22937600, 'bits_per_second': 45874166.42953692, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 16.004189, 'end': 20.004097, 'seconds': 3.999907970428467, 'bytes': 22937600, 'bits_per_second': 45876255.49303414, 'omitted': False, 'sender': False}], 'sum': {'start': 16.004189, 'end': 20.004097, 'seconds': 3.999907970428467, 'bytes': 22937600, 'bits_per_second': 45876255.49303414, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 20.004097, 'end': 24.004051, 'seconds': 3.9999539852142334, 'bytes': 23068672, 'bits_per_second': 46137874.756105654, 'omitted': False, 'sender': False}], 'sum': {'start': 20.004097, 'end': 24.004051, 'seconds': 3.9999539852142334, 'bytes': 23068672, 'bits_per_second': 46137874.756105654, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 24.004051, 'end': 28.004086, 'seconds': 4.00003480911255, 'bytes': 22937600, 'bits_per_second': 45874800.78472408, 'omitted': False, 'sender': False}], 'sum': {'start': 24.004051, 'end': 28.004086, 'seconds': 4.00003480911255, 'bytes': 22937600, 'bits_per_second': 45874800.78472408, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 28.004086, 'end': 32.004096, 'seconds': 4.000010013580322, 'bytes': 23068672, 'bits_per_second': 46137228.50028914, 'omitted': False, 'sender': False}], 'sum': {'start': 28.004086, 'end': 32.004096, 'seconds': 4.000010013580322, 'bytes': 23068672, 'bits_per_second': 46137228.50028914, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 32.004096, 'end': 36.004082, 'seconds': 3.999985933303833, 'bytes': 22937600, 'bits_per_second': 45875361.32869234, 'omitted': False, 'sender': False}], 'sum': {'start': 32.004096, 'end': 36.004082, 'seconds': 3.999985933303833, 'bytes': 22937600, 'bits_per_second': 45875361.32869234, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 36.004082, 'end': 40.004152, 'seconds': 4.000070095062256, 'bytes': 22937600, 'bits_per_second': 45874396.107837215, 'omitted': False, 'sender': False}], 'sum': {'start': 36.004082, 'end': 40.004152, 'seconds': 4.000070095062256, 'bytes': 22937600, 'bits_per_second': 45874396.107837215, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 40.004152, 'end': 44.004046, 'seconds': 3.9998939037323, 'bytes': 23068672, 'bits_per_second': 46138567.7824597, 'omitted': False, 'sender': False}], 'sum': {'start': 40.004152, 'end': 44.004046, 'seconds': 3.9998939037323, 'bytes': 23068672, 'bits_per_second': 46138567.7824597, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 44.004046, 'end': 48.004042, 'seconds': 3.9999959468841553, 'bytes': 22937600, 'bits_per_second': 45875246.4844221, 'omitted': False, 'sender': False}], 'sum': {'start': 44.004046, 'end': 48.004042, 'seconds': 3.9999959468841553, 'bytes': 22937600, 'bits_per_second': 45875246.4844221, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 48.004042, 'end': 52.004044, 'seconds': 4.000001907348633, 'bytes': 22937600, 'bits_per_second': 45875178.12501043, 'omitted': False, 'sender': False}], 'sum': {'start': 48.004042, 'end': 52.004044, 'seconds': 4.000001907348633, 'bytes': 22937600, 'bits_per_second': 45875178.12501043, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 52.004044, 'end': 56.004092, 'seconds': 4.0000481605529785, 'bytes': 22937600, 'bits_per_second': 45874647.66290022, 'omitted': False, 'sender': False}], 'sum': {'start': 52.004044, 'end': 56.004092, 'seconds': 4.0000481605529785, 'bytes': 22937600, 'bits_per_second': 45874647.66290022, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 56.004092, 'end': 60.004192, 'seconds': 4.000100135803223, 'bytes': 23068672, 'bits_per_second': 46136189.0289135, 'omitted': False, 'sender': False}], 'sum': {'start': 56.004092, 'end': 60.004192, 'seconds': 4.000100135803223, 'bytes': 23068672, 'bits_per_second': 46136189.0289135, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 5, 'start': 60.004192, 'end': 60.343236, 'seconds': 0.3390440046787262, 'bytes': 1835008, 'bits_per_second': 43298403.14949867, 'omitted': False, 'sender': False}], 'sum': {'start': 60.004192, 'end': 60.343236, 'seconds': 0.3390440046787262, 'bytes': 1835008, 'bits_per_second': 43298403.14949867, 'omitted': False, 'sender': False}}], 'end': {'streams': [{'sender': {'socket': 5, 'start': 0, 'end': 60.343236, 'seconds': 60.343236, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'receiver': {'socket': 5, 'start': 0, 'end': 60.343236, 'seconds': 60.343236, 'bytes': 346685440, 'bits_per_second': 45961796.28152524, 'sender': False}}], 'sum_sent': {'start': 0, 'end': 60.343236, 'seconds': 60.343236, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'sum_received': {'start': 0, 'end': 60.343236, 'seconds': 60.343236, 'bytes': 346685440, 'bits_per_second': 45961796.28152524, 'sender': False}, 'cpu_utilization_percent': {'host_total': 17.94695538555985, 'host_user': 0.4651862661597122, 'host_system': 17.481772433766736, 'remote_total': 0, 'remote_user': 0, 'remote_system': 0}, 'receiver_tcp_congestion': 'cubic'}}
2025-02-20 00:59:05 [DEBUG] Heartbeat sent successfully
2025-02-20 01:00:06 [DEBUG] Heartbeat sent successfully
2025-02-20 01:01:07 [DEBUG] Heartbeat sent successfully
2025-02-20 01:01:56 [INFO] 收到消息: start_task
2025-02-20 01:01:56 [INFO] 收到任务启动请求: task_id=80_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 124, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 01:01:56 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 01:02:09 [DEBUG] Heartbeat sent successfully
2025-02-20 01:02:16 [ERROR] 解析 iperf3 输出失败: Expecting value: line 1 column 1 (char 0)
2025-02-20 01:03:10 [DEBUG] Heartbeat sent successfully
2025-02-20 01:03:58 [INFO] 收到消息: start_task
2025-02-20 01:03:58 [INFO] 收到任务启动请求: task_id=80_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 125, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'duration': 60, 'network_interface': 'ens37', 'interval': 4}
2025-02-20 01:03:58 [INFO] 启动 iperf3 服务端: iperf3 -s -p 39999 --bind-dev ens37 --json -i 4 -6 --one-off --idle-timeout 20
2025-02-20 01:04:11 [DEBUG] Heartbeat sent successfully
2025-02-20 01:05:00 [INFO] iperf3 server result: {'start': {'connected': [{'socket': 5, 'local_host': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'local_port': 39999, 'remote_host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'remote_port': 33312}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'target_bitrate': 50000000, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'timestamp': {'time': 'Wed, 19 Feb 2025 17:04:00 GMT', 'timesecs': 1739984640}, 'accepted_connection': {'host': '240e:34c:562:2460:2ab1:33ff:fe05:606f', 'port': 33302}, 'cookie': 'm53f4dmi7ur545icidolpq44o3wyj4vh3pw4', 'tcp_mss_default': 0, 'fq_rate': 0, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 60, 'bytes': 0, 'blocks': 0, 'reverse': 1, 'tos': 0, 'target_bitrate': 50000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 5, 'start': 0, 'end': 4.000206, 'seconds': 4.000205993652344, 'bytes': 7602176, 'bits_per_second': 15203569.04032118, 'retransmits': 238, 'snd_cwnd': 5600, 'snd_wnd': 738944, 'rtt': 6487, 'rttvar': 341, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 0, 'end': 4.000206, 'seconds': 4.000205993652344, 'bytes': 7602176, 'bits_per_second': 15203569.04032118, 'retransmits': 238, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 4.000206, 'end': 8.000116, 'seconds': 3.9999101161956787, 'bytes': 4849664, 'bits_per_second': 9699545.958022725, 'retransmits': 144, 'snd_cwnd': 7000, 'snd_wnd': 738944, 'rtt': 6525, 'rttvar': 384, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 4.000206, 'end': 8.000116, 'seconds': 3.9999101161956787, 'bytes': 4849664, 'bits_per_second': 9699545.958022725, 'retransmits': 144, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 8.000116, 'end': 12.000092, 'seconds': 3.9999759197235107, 'bytes': 5373952, 'bits_per_second': 10747968.70351452, 'retransmits': 154, 'snd_cwnd': 22400, 'snd_wnd': 740224, 'rtt': 7125, 'rttvar': 222, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 8.000116, 'end': 12.000092, 'seconds': 3.9999759197235107, 'bytes': 5373952, 'bits_per_second': 10747968.70351452, 'retransmits': 154, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 12.000092, 'end': 16.00032, 'seconds': 4.00022792816162, 'bytes': 7864320, 'bits_per_second': 15727743.801067244, 'retransmits': 172, 'snd_cwnd': 22400, 'snd_wnd': 740224, 'rtt': 7566, 'rttvar': 867, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 12.000092, 'end': 16.00032, 'seconds': 4.00022792816162, 'bytes': 7864320, 'bits_per_second': 15727743.801067244, 'retransmits': 172, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 16.00032, 'end': 20.000132, 'seconds': 3.999811887741089, 'bytes': 7995392, 'bits_per_second': 15991536.0509925, 'retransmits': 190, 'snd_cwnd': 16800, 'snd_wnd': 736256, 'rtt': 6809, 'rttvar': 100, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 16.00032, 'end': 20.000132, 'seconds': 3.999811887741089, 'bytes': 7995392, 'bits_per_second': 15991536.0509925, 'retransmits': 190, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 20.000132, 'end': 24.000125, 'seconds': 3.999993085861206, 'bytes': 6029312, 'bits_per_second': 12058644.84378603, 'retransmits': 196, 'snd_cwnd': 12600, 'snd_wnd': 738944, 'rtt': 6823, 'rttvar': 130, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 20.000132, 'end': 24.000125, 'seconds': 3.999993085861206, 'bytes': 6029312, 'bits_per_second': 12058644.84378603, 'retransmits': 196, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 24.000125, 'end': 28.000117, 'seconds': 3.99999189376831, 'bytes': 7077888, 'bits_per_second': 14155804.687558137, 'retransmits': 165, 'snd_cwnd': 9800, 'snd_wnd': 737920, 'rtt': 6856, 'rttvar': 99, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 24.000125, 'end': 28.000117, 'seconds': 3.99999189376831, 'bytes': 7077888, 'bits_per_second': 14155804.687558137, 'retransmits': 165, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 28.000117, 'end': 32.000108, 'seconds': 3.999990940093994, 'bytes': 5898240, 'bits_per_second': 11796506.718810517, 'retransmits': 161, 'snd_cwnd': 8400, 'snd_wnd': 738944, 'rtt': 6614, 'rttvar': 223, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 28.000117, 'end': 32.000108, 'seconds': 3.999990940093994, 'bytes': 5898240, 'bits_per_second': 11796506.718810517, 'retransmits': 161, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 32.000108, 'end': 36.000095, 'seconds': 3.99998688697815, 'bytes': 6815744, 'bits_per_second': 13631532.687646497, 'retransmits': 160, 'snd_cwnd': 15400, 'snd_wnd': 737152, 'rtt': 6857, 'rttvar': 226, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 32.000108, 'end': 36.000095, 'seconds': 3.99998688697815, 'bytes': 6815744, 'bits_per_second': 13631532.687646497, 'retransmits': 160, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 36.000095, 'end': 40.000091, 'seconds': 3.9999959468841553, 'bytes': 7471104, 'bits_per_second': 14942223.140640343, 'retransmits': 183, 'snd_cwnd': 18200, 'snd_wnd': 740224, 'rtt': 6398, 'rttvar': 383, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 36.000095, 'end': 40.000091, 'seconds': 3.9999959468841553, 'bytes': 7471104, 'bits_per_second': 14942223.140640343, 'retransmits': 183, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 40.000091, 'end': 44.000129, 'seconds': 4.000038146972656, 'bytes': 6553600, 'bits_per_second': 13107075.001192082, 'retransmits': 160, 'snd_cwnd': 4200, 'snd_wnd': 738944, 'rtt': 7125, 'rttvar': 317, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 40.000091, 'end': 44.000129, 'seconds': 4.000038146972656, 'bytes': 6553600, 'bits_per_second': 13107075.001192082, 'retransmits': 160, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 44.000129, 'end': 48.001153, 'seconds': 4.001023769378662, 'bytes': 6160384, 'bits_per_second': 12317615.400633674, 'retransmits': 149, 'snd_cwnd': 11200, 'snd_wnd': 738944, 'rtt': 6397, 'rttvar': 233, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 44.000129, 'end': 48.001153, 'seconds': 4.001023769378662, 'bytes': 6160384, 'bits_per_second': 12317615.400633674, 'retransmits': 149, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 48.001153, 'end': 52.000148, 'seconds': 3.998995065689087, 'bytes': 6160384, 'bits_per_second': 12323864.168486485, 'retransmits': 142, 'snd_cwnd': 9800, 'snd_wnd': 740224, 'rtt': 7033, 'rttvar': 381, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 48.001153, 'end': 52.000148, 'seconds': 3.998995065689087, 'bytes': 6160384, 'bits_per_second': 12323864.168486485, 'retransmits': 142, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 52.000148, 'end': 56.000118, 'seconds': 3.999969959259033, 'bytes': 6815744, 'bits_per_second': 13631590.37576886, 'retransmits': 167, 'snd_cwnd': 12600, 'snd_wnd': 738816, 'rtt': 7044, 'rttvar': 255, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 52.000148, 'end': 56.000118, 'seconds': 3.999969959259033, 'bytes': 6815744, 'bits_per_second': 13631590.37576886, 'retransmits': 167, 'omitted': False, 'sender': True}}, {'streams': [{'socket': 5, 'start': 56.000118, 'end': 60.00009, 'seconds': 3.999972105026245, 'bytes': 5373952, 'bits_per_second': 10747978.953647707, 'retransmits': 130, 'snd_cwnd': 11200, 'snd_wnd': 737536, 'rtt': 6829, 'rttvar': 424, 'pmtu': 1492, 'omitted': False, 'sender': True}], 'sum': {'start': 56.000118, 'end': 60.00009, 'seconds': 3.999972105026245, 'bytes': 5373952, 'bits_per_second': 10747978.953647707, 'retransmits': 130, 'omitted': False, 'sender': True}}], 'end': {'streams': [{'sender': {'socket': 5, 'start': 0, 'end': 60.012651, 'seconds': 60.012651, 'bytes': 98041856, 'bits_per_second': 13069491.76432816, 'retransmits': 2511, 'max_snd_cwnd': 22400, 'max_snd_wnd': 740224, 'max_rtt': 7566, 'min_rtt': 6397, 'mean_rtt': 6826, 'sender': True}, 'receiver': {'socket': 5, 'start': 0, 'end': 60.012651, 'seconds': 60.012651, 'bytes': 0, 'bits_per_second': 0, 'sender': True}}], 'sum_sent': {'start': 0, 'end': 60.012651, 'seconds': 60.012651, 'bytes': 98041856, 'bits_per_second': 13069491.76432816, 'retransmits': 2511, 'sender': True}, 'sum_received': {'start': 0, 'end': 60.012651, 'seconds': 60.012651, 'bytes': 0, 'bits_per_second': 0, 'sender': True}, 'cpu_utilization_percent': {'host_total': 3.4771663940028854, 'host_user': 2.3627152580296955, 'host_system': 1.11445113597319, 'remote_total': 0, 'remote_user': 0, 'remote_system': 0}, 'sender_tcp_congestion': 'cubic'}}
2025-02-20 01:05:12 [DEBUG] Heartbeat sent successfully
2025-02-20 01:06:13 [DEBUG] Heartbeat sent successfully
2025-02-20 01:07:14 [DEBUG] Heartbeat sent successfully
2025-02-20 01:07:53 [ERROR] WebSocket连接断开，准备重连
2025-02-20 01:07:53 [INFO] WebSocket连接已安全关闭
2025-02-20 01:07:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-20 01:07:55 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-20 01:07:57 [INFO] Program interrupted by user
