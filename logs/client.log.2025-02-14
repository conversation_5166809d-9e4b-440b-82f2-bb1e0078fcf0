2025-02-14 00:00:00 [DEBUG] Heartbeat sent successfully
2025-02-14 00:01:02 [DEBUG] Heartbeat sent successfully
2025-02-14 00:02:03 [DEBUG] Heartbeat sent successfully
2025-02-14 00:03:04 [DEBUG] Heartbeat sent successfully
2025-02-14 00:04:05 [DEBUG] Heartbeat sent successfully
2025-02-14 00:05:00 [ERROR] WebSocket连接断开，准备重连
2025-02-14 00:05:00 [INFO] WebSocket连接已安全关闭
2025-02-14 00:05:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-14 00:05:02 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-14 00:05:06 [DEBUG] Heartbeat sent successfully
2025-02-14 00:05:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-14 00:05:09 [INFO] Program interrupted by user
2025-02-14 20:43:10 [INFO] Client starting...
2025-02-14 20:43:11 [INFO] Login successful
2025-02-14 20:43:11 [INFO] Checking registration status...
2025-02-14 20:43:11 [INFO] Client already registered
2025-02-14 20:43:11 [INFO] Starting SSH tunnel to *************:40000
2025-02-14 20:43:11 [INFO] Starting heartbeat...
2025-02-14 20:43:11 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-14 20:43:11 [INFO] SSH tunnel established
2025-02-14 20:43:12 [DEBUG] Heartbeat sent successfully
2025-02-14 20:43:12 [INFO] 注册成功
2025-02-14 20:44:13 [DEBUG] Heartbeat sent successfully
2025-02-14 20:45:14 [DEBUG] Heartbeat sent successfully
2025-02-14 20:46:04 [INFO] 收到消息: start_task
2025-02-14 20:46:04 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 53, 'interface': '240e:34c:399:1440:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 20:46:05 [INFO] iperf3 服务端启动配置:
2025-02-14 20:46:05 [INFO]   - 端口: 39999
2025-02-14 20:46:05 [INFO]   - IPv6模式: True
2025-02-14 20:46:05 [INFO]   - 绑定地址: *
2025-02-14 20:46:05 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 20:46:05 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 20:46:16 [DEBUG] Heartbeat sent successfully
2025-02-14 20:47:17 [DEBUG] Heartbeat sent successfully
2025-02-14 20:48:18 [DEBUG] Heartbeat sent successfully
2025-02-14 20:49:19 [DEBUG] Heartbeat sent successfully
2025-02-14 20:50:20 [DEBUG] Heartbeat sent successfully
2025-02-14 20:51:21 [DEBUG] Heartbeat sent successfully
2025-02-14 20:52:23 [DEBUG] Heartbeat sent successfully
2025-02-14 20:53:24 [DEBUG] Heartbeat sent successfully
2025-02-14 20:54:25 [DEBUG] Heartbeat sent successfully
2025-02-14 20:55:26 [DEBUG] Heartbeat sent successfully
2025-02-14 20:56:27 [DEBUG] Heartbeat sent successfully
2025-02-14 20:57:29 [DEBUG] Heartbeat sent successfully
2025-02-14 20:58:30 [DEBUG] Heartbeat sent successfully
2025-02-14 20:59:31 [DEBUG] Heartbeat sent successfully
2025-02-14 21:00:32 [DEBUG] Heartbeat sent successfully
2025-02-14 21:01:33 [DEBUG] Heartbeat sent successfully
2025-02-14 21:02:35 [DEBUG] Heartbeat sent successfully
2025-02-14 21:02:48 [INFO] Program interrupted by user
2025-02-14 21:02:51 [INFO] Client starting...
2025-02-14 21:02:52 [INFO] Login successful
2025-02-14 21:02:52 [INFO] Checking registration status...
2025-02-14 21:02:52 [INFO] Client already registered
2025-02-14 21:02:52 [INFO] Starting SSH tunnel to *************:40000
2025-02-14 21:02:52 [INFO] Starting heartbeat...
2025-02-14 21:02:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-14 21:02:52 [INFO] SSH tunnel established
2025-02-14 21:02:53 [DEBUG] Heartbeat sent successfully
2025-02-14 21:02:53 [INFO] 注册成功
2025-02-14 21:03:54 [DEBUG] Heartbeat sent successfully
2025-02-14 21:04:09 [INFO] 收到消息: start_task
2025-02-14 21:04:09 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 55, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:04:09 [INFO] iperf3 服务端启动配置:
2025-02-14 21:04:09 [INFO]   - 端口: 39999
2025-02-14 21:04:09 [INFO]   - IPv6模式: True
2025-02-14 21:04:09 [INFO]   - 绑定地址: *
2025-02-14 21:04:09 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:04:09 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:04:56 [DEBUG] Heartbeat sent successfully
2025-02-14 21:05:57 [DEBUG] Heartbeat sent successfully
2025-02-14 21:06:58 [DEBUG] Heartbeat sent successfully
2025-02-14 21:07:59 [DEBUG] Heartbeat sent successfully
2025-02-14 21:09:00 [DEBUG] Heartbeat sent successfully
2025-02-14 21:10:02 [DEBUG] Heartbeat sent successfully
2025-02-14 21:11:03 [DEBUG] Heartbeat sent successfully
2025-02-14 21:11:07 [INFO] === iperf3 服务端结果 ===
2025-02-14 21:11:07 [ERROR] iperf3 服务端错误: unable to receive cookie at server: 
2025-02-14 21:11:07 [INFO] 收到消息: start_task
2025-02-14 21:11:07 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 56, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:11:07 [INFO] iperf3 服务端启动配置:
2025-02-14 21:11:07 [INFO]   - 端口: 39999
2025-02-14 21:11:07 [INFO]   - IPv6模式: True
2025-02-14 21:11:07 [INFO]   - 绑定地址: *
2025-02-14 21:11:07 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:11:07 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:12:04 [DEBUG] Heartbeat sent successfully
2025-02-14 21:13:05 [DEBUG] Heartbeat sent successfully
2025-02-14 21:14:06 [DEBUG] Heartbeat sent successfully
2025-02-14 21:15:08 [DEBUG] Heartbeat sent successfully
2025-02-14 21:16:09 [DEBUG] Heartbeat sent successfully
2025-02-14 21:17:10 [DEBUG] Heartbeat sent successfully
2025-02-14 21:18:11 [DEBUG] Heartbeat sent successfully
2025-02-14 21:19:12 [DEBUG] Heartbeat sent successfully
2025-02-14 21:20:13 [DEBUG] Heartbeat sent successfully
2025-02-14 21:21:15 [DEBUG] Heartbeat sent successfully
2025-02-14 21:22:16 [DEBUG] Heartbeat sent successfully
2025-02-14 21:23:17 [DEBUG] Heartbeat sent successfully
2025-02-14 21:24:18 [DEBUG] Heartbeat sent successfully
2025-02-14 21:25:19 [DEBUG] Heartbeat sent successfully
2025-02-14 21:26:21 [DEBUG] Heartbeat sent successfully
2025-02-14 21:27:22 [DEBUG] Heartbeat sent successfully
2025-02-14 21:28:22 [WARNING] 移动网网卡 ens38 未启用，尝试启用...
2025-02-14 21:28:22 [INFO] 网卡 ens38 已启用
2025-02-14 21:28:26 [INFO] 已为网卡 ens38 获取IP地址
2025-02-14 21:28:27 [DEBUG] Heartbeat sent successfully
2025-02-14 21:29:28 [DEBUG] Heartbeat sent successfully
2025-02-14 21:29:30 [INFO] === iperf3 服务端结果 ===
2025-02-14 21:29:30 [ERROR] iperf3 服务端错误: unable to receive cookie at server: 
2025-02-14 21:29:30 [INFO] 收到消息: start_task
2025-02-14 21:29:30 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 57, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:29:30 [INFO] iperf3 服务端启动配置:
2025-02-14 21:29:30 [INFO]   - 端口: 39999
2025-02-14 21:29:30 [INFO]   - IPv6模式: True
2025-02-14 21:29:30 [INFO]   - 绑定地址: *
2025-02-14 21:29:30 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:29:30 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:29:51 [INFO] === iperf3 服务端结果 ===
2025-02-14 21:29:51 [ERROR] iperf3 服务端错误: unable to receive cookie at server: 
2025-02-14 21:29:51 [INFO] 收到消息: start_task
2025-02-14 21:29:51 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 58, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:29:51 [INFO] iperf3 服务端启动配置:
2025-02-14 21:29:51 [INFO]   - 端口: 39999
2025-02-14 21:29:51 [INFO]   - IPv6模式: True
2025-02-14 21:29:51 [INFO]   - 绑定地址: *
2025-02-14 21:29:51 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:29:51 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:30:29 [DEBUG] Heartbeat sent successfully
2025-02-14 21:30:30 [INFO] === iperf3 服务端结果 ===
2025-02-14 21:30:30 [ERROR] iperf3 服务端错误: unable to receive cookie at server: 
2025-02-14 21:30:30 [INFO] 收到消息: start_task
2025-02-14 21:30:30 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 59, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:30:30 [INFO] iperf3 服务端启动配置:
2025-02-14 21:30:30 [INFO]   - 端口: 39999
2025-02-14 21:30:30 [INFO]   - IPv6模式: True
2025-02-14 21:30:30 [INFO]   - 绑定地址: *
2025-02-14 21:30:30 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:30:30 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:31:30 [DEBUG] Heartbeat sent successfully
2025-02-14 21:32:32 [DEBUG] Heartbeat sent successfully
2025-02-14 21:32:39 [INFO] === iperf3 服务端结果 ===
2025-02-14 21:32:39 [ERROR] iperf3 服务端错误: unable to receive cookie at server: 
2025-02-14 21:32:39 [INFO] 收到消息: start_task
2025-02-14 21:32:39 [INFO] 收到任务启动请求: task_id=69_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 60, 'interface': '240e:34c:70:bdb0:20c:29ff:fe8b:47e9', 'network_interface': 'ens37'}
2025-02-14 21:32:39 [INFO] iperf3 服务端启动配置:
2025-02-14 21:32:39 [INFO]   - 端口: 39999
2025-02-14 21:32:39 [INFO]   - IPv6模式: True
2025-02-14 21:32:39 [INFO]   - 绑定地址: *
2025-02-14 21:32:39 [INFO] 开始执行 iperf3 服务端测试...
2025-02-14 21:32:39 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=*
2025-02-14 21:33:33 [DEBUG] Heartbeat sent successfully
2025-02-14 21:34:34 [DEBUG] Heartbeat sent successfully
2025-02-14 21:35:35 [DEBUG] Heartbeat sent successfully
2025-02-14 21:36:36 [DEBUG] Heartbeat sent successfully
2025-02-14 21:37:37 [DEBUG] Heartbeat sent successfully
2025-02-14 21:38:39 [DEBUG] Heartbeat sent successfully
2025-02-14 21:39:39 [WARNING] 移动网网卡 ens38 未启用，尝试启用...
2025-02-14 21:39:39 [INFO] 网卡 ens38 已启用
2025-02-14 21:39:39 [INFO] 已为网卡 ens38 获取IP地址
2025-02-14 21:39:40 [DEBUG] Heartbeat sent successfully
2025-02-14 21:40:41 [DEBUG] Heartbeat sent successfully
2025-02-14 21:41:42 [DEBUG] Heartbeat sent successfully
2025-02-14 21:42:44 [DEBUG] Heartbeat sent successfully
2025-02-14 21:43:45 [DEBUG] Heartbeat sent successfully
2025-02-14 21:44:46 [DEBUG] Heartbeat sent successfully
2025-02-14 21:45:47 [DEBUG] Heartbeat sent successfully
2025-02-14 21:46:48 [DEBUG] Heartbeat sent successfully
2025-02-14 21:47:50 [DEBUG] Heartbeat sent successfully
2025-02-14 21:48:51 [DEBUG] Heartbeat sent successfully
2025-02-14 21:49:52 [DEBUG] Heartbeat sent successfully
2025-02-14 21:50:53 [DEBUG] Heartbeat sent successfully
2025-02-14 21:51:54 [DEBUG] Heartbeat sent successfully
2025-02-14 21:52:54 [WARNING] 移动网网卡 ens38 未启用，尝试启用...
2025-02-14 21:52:54 [INFO] 网卡 ens38 已启用
2025-02-14 21:52:55 [INFO] 已为网卡 ens38 获取IP地址
2025-02-14 21:52:56 [DEBUG] Heartbeat sent successfully
2025-02-14 21:53:57 [DEBUG] Heartbeat sent successfully
2025-02-14 21:54:58 [DEBUG] Heartbeat sent successfully
2025-02-14 21:55:59 [DEBUG] Heartbeat sent successfully
2025-02-14 21:57:01 [DEBUG] Heartbeat sent successfully
2025-02-14 21:58:02 [DEBUG] Heartbeat sent successfully
2025-02-14 21:59:03 [DEBUG] Heartbeat sent successfully
2025-02-14 22:00:04 [DEBUG] Heartbeat sent successfully
2025-02-14 22:01:05 [DEBUG] Heartbeat sent successfully
2025-02-14 22:02:07 [DEBUG] Heartbeat sent successfully
2025-02-14 22:03:08 [DEBUG] Heartbeat sent successfully
2025-02-14 22:04:09 [DEBUG] Heartbeat sent successfully
2025-02-14 22:05:10 [DEBUG] Heartbeat sent successfully
2025-02-14 22:06:11 [DEBUG] Heartbeat sent successfully
2025-02-14 22:07:12 [DEBUG] Heartbeat sent successfully
2025-02-14 22:08:14 [DEBUG] Heartbeat sent successfully
2025-02-14 22:09:15 [DEBUG] Heartbeat sent successfully
2025-02-14 22:10:16 [DEBUG] Heartbeat sent successfully
2025-02-14 22:11:17 [DEBUG] Heartbeat sent successfully
2025-02-14 22:12:19 [DEBUG] Heartbeat sent successfully
2025-02-14 22:13:20 [DEBUG] Heartbeat sent successfully
2025-02-14 22:14:21 [ERROR] 获取私网IPv4地址失败: 2
2025-02-14 22:14:21 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:16:43 [ERROR] Heartbeat failed with status code: 503
2025-02-14 22:18:08 [DEBUG] Heartbeat sent successfully
2025-02-14 22:19:09 [DEBUG] Heartbeat sent successfully
2025-02-14 22:20:10 [DEBUG] Heartbeat sent successfully
2025-02-14 22:21:11 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:22:12 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:23:13 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:24:14 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:25:15 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:26:16 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:27:17 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:28:18 [ERROR] 获取私网IPv4地址失败: 2
2025-02-14 22:28:18 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:29:19 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:30:20 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:31:21 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:32:22 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:33:23 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:34:24 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:35:25 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:36:26 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:37:27 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:38:28 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:39:29 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:40:30 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:41:31 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 22:42:33 [DEBUG] Heartbeat sent successfully
2025-02-14 22:43:34 [DEBUG] Heartbeat sent successfully
2025-02-14 22:44:35 [DEBUG] Heartbeat sent successfully
2025-02-14 22:45:36 [DEBUG] Heartbeat sent successfully
2025-02-14 22:46:37 [DEBUG] Heartbeat sent successfully
2025-02-14 22:47:39 [DEBUG] Heartbeat sent successfully
2025-02-14 22:48:40 [DEBUG] Heartbeat sent successfully
2025-02-14 22:49:41 [DEBUG] Heartbeat sent successfully
2025-02-14 22:50:42 [DEBUG] Heartbeat sent successfully
2025-02-14 22:51:43 [DEBUG] Heartbeat sent successfully
2025-02-14 22:52:44 [DEBUG] Heartbeat sent successfully
2025-02-14 22:53:46 [DEBUG] Heartbeat sent successfully
2025-02-14 22:55:42 [DEBUG] Heartbeat sent successfully
2025-02-14 22:56:43 [DEBUG] Heartbeat sent successfully
2025-02-14 22:57:44 [DEBUG] Heartbeat sent successfully
2025-02-14 22:58:46 [DEBUG] Heartbeat sent successfully
2025-02-14 22:59:47 [DEBUG] Heartbeat sent successfully
2025-02-14 23:00:48 [DEBUG] Heartbeat sent successfully
2025-02-14 23:01:49 [DEBUG] Heartbeat sent successfully
2025-02-14 23:02:50 [DEBUG] Heartbeat sent successfully
2025-02-14 23:03:52 [DEBUG] Heartbeat sent successfully
2025-02-14 23:04:53 [DEBUG] Heartbeat sent successfully
2025-02-14 23:05:54 [DEBUG] Heartbeat sent successfully
2025-02-14 23:06:55 [DEBUG] Heartbeat sent successfully
2025-02-14 23:07:56 [DEBUG] Heartbeat sent successfully
2025-02-14 23:08:58 [DEBUG] Heartbeat sent successfully
2025-02-14 23:09:59 [DEBUG] Heartbeat sent successfully
2025-02-14 23:11:00 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:12:01 [DEBUG] Heartbeat sent successfully
2025-02-14 23:13:02 [DEBUG] Heartbeat sent successfully
2025-02-14 23:14:03 [DEBUG] Heartbeat sent successfully
2025-02-14 23:15:05 [DEBUG] Heartbeat sent successfully
2025-02-14 23:16:06 [DEBUG] Heartbeat sent successfully
2025-02-14 23:17:07 [DEBUG] Heartbeat sent successfully
2025-02-14 23:18:08 [DEBUG] Heartbeat sent successfully
2025-02-14 23:19:09 [DEBUG] Heartbeat sent successfully
2025-02-14 23:20:10 [DEBUG] Heartbeat sent successfully
2025-02-14 23:21:12 [DEBUG] Heartbeat sent successfully
2025-02-14 23:22:13 [DEBUG] Heartbeat sent successfully
2025-02-14 23:23:14 [DEBUG] Heartbeat sent successfully
2025-02-14 23:24:15 [DEBUG] Heartbeat sent successfully
2025-02-14 23:25:16 [DEBUG] Heartbeat sent successfully
2025-02-14 23:26:18 [DEBUG] Heartbeat sent successfully
2025-02-14 23:27:19 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:28:20 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:29:21 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:30:22 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:31:23 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:32:24 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:33:25 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:34:26 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:35:27 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:36:28 [ERROR] 获取私网IPv4地址失败: 2
2025-02-14 23:36:28 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:37:29 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:38:30 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:39:31 [DEBUG] Heartbeat sent successfully
2025-02-14 23:40:32 [DEBUG] Heartbeat sent successfully
2025-02-14 23:41:34 [DEBUG] Heartbeat sent successfully
2025-02-14 23:42:35 [DEBUG] Heartbeat sent successfully
2025-02-14 23:43:36 [DEBUG] Heartbeat sent successfully
2025-02-14 23:44:37 [DEBUG] Heartbeat sent successfully
2025-02-14 23:45:38 [DEBUG] Heartbeat sent successfully
2025-02-14 23:46:39 [DEBUG] Heartbeat sent successfully
2025-02-14 23:47:41 [DEBUG] Heartbeat sent successfully
2025-02-14 23:48:42 [DEBUG] Heartbeat sent successfully
2025-02-14 23:49:43 [DEBUG] Heartbeat sent successfully
2025-02-14 23:50:44 [DEBUG] Heartbeat sent successfully
2025-02-14 23:51:45 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:52:46 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:53:47 [ERROR] 获取私网IPv4地址失败: 2
2025-02-14 23:53:47 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-14 23:54:48 [DEBUG] Heartbeat sent successfully
2025-02-14 23:55:50 [DEBUG] Heartbeat sent successfully
2025-02-14 23:56:51 [DEBUG] Heartbeat sent successfully
2025-02-14 23:57:52 [DEBUG] Heartbeat sent successfully
2025-02-14 23:58:53 [DEBUG] Heartbeat sent successfully
2025-02-14 23:59:54 [DEBUG] Heartbeat sent successfully
