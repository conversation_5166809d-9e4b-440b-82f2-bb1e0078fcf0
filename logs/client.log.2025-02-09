2025-02-09 20:13:34 [INFO] Client starting...
2025-02-09 20:13:35 [INFO] Login successful
2025-02-09 20:13:35 [INFO] Checking registration status...
2025-02-09 20:13:36 [INFO] Client already registered
2025-02-09 20:13:36 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 20:13:36 [INFO] Starting heartbeat...
2025-02-09 20:13:37 [DEBUG] Heartbeat sent successfully
2025-02-09 20:13:37 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:13:37 [INFO] SSH tunnel established
2025-02-09 20:13:37 [INFO] 注册成功
2025-02-09 20:14:39 [DEBUG] Heartbeat sent successfully
2025-02-09 20:15:40 [DEBUG] Heartbeat sent successfully
2025-02-09 20:16:41 [DEBUG] Heartbeat sent successfully
2025-02-09 20:17:42 [DEBUG] Heartbeat sent successfully
2025-02-09 20:18:43 [DEBUG] Heartbeat sent successfully
2025-02-09 20:19:44 [DEBUG] Heartbeat sent successfully
2025-02-09 20:20:22 [INFO] 收到消息: start_task
2025-02-09 20:20:22 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 3, 'network_interface': 'ens37'}
2025-02-09 20:20:22 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 20:20:23 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 20:20:24 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 20:20:45 [DEBUG] Heartbeat sent successfully
2025-02-09 20:21:46 [DEBUG] Heartbeat sent successfully
2025-02-09 20:22:48 [DEBUG] Heartbeat sent successfully
2025-02-09 20:23:49 [DEBUG] Heartbeat sent successfully
2025-02-09 20:24:50 [DEBUG] Heartbeat sent successfully
2025-02-09 20:25:51 [DEBUG] Heartbeat sent successfully
2025-02-09 20:26:52 [DEBUG] Heartbeat sent successfully
2025-02-09 20:27:53 [DEBUG] Heartbeat sent successfully
2025-02-09 20:28:54 [DEBUG] Heartbeat sent successfully
2025-02-09 20:29:55 [DEBUG] Heartbeat sent successfully
2025-02-09 20:30:20 [INFO] Program interrupted by user
2025-02-09 20:30:29 [INFO] Client starting...
2025-02-09 20:30:30 [INFO] Login successful
2025-02-09 20:30:30 [INFO] Checking registration status...
2025-02-09 20:30:30 [INFO] Client already registered
2025-02-09 20:30:30 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 20:30:30 [INFO] Starting heartbeat...
2025-02-09 20:30:31 [DEBUG] Heartbeat sent successfully
2025-02-09 20:30:31 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:30:31 [INFO] SSH tunnel established
2025-02-09 20:30:31 [INFO] 注册成功
2025-02-09 20:31:24 [INFO] 收到消息: start_task
2025-02-09 20:31:24 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 4, 'network_interface': 'ens37'}
2025-02-09 20:31:32 [DEBUG] Heartbeat sent successfully
2025-02-09 20:32:27 [ERROR] WebSocket连接断开，准备重连
2025-02-09 20:32:27 [INFO] WebSocket连接已安全关闭
2025-02-09 20:32:27 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:32:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 20:32:32 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:32:32 [INFO] 注册成功
2025-02-09 20:32:34 [DEBUG] Heartbeat sent successfully
2025-02-09 20:33:29 [INFO] 收到消息: start_task
2025-02-09 20:33:29 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 5, 'network_interface': 'ens37'}
2025-02-09 20:33:35 [DEBUG] Heartbeat sent successfully
2025-02-09 20:34:36 [DEBUG] Heartbeat sent successfully
2025-02-09 20:35:37 [DEBUG] Heartbeat sent successfully
2025-02-09 20:36:38 [DEBUG] Heartbeat sent successfully
2025-02-09 20:37:39 [DEBUG] Heartbeat sent successfully
2025-02-09 20:38:40 [DEBUG] Heartbeat sent successfully
2025-02-09 20:39:42 [DEBUG] Heartbeat sent successfully
2025-02-09 20:40:43 [DEBUG] Heartbeat sent successfully
2025-02-09 20:41:44 [DEBUG] Heartbeat sent successfully
2025-02-09 20:42:45 [DEBUG] Heartbeat sent successfully
2025-02-09 20:43:35 [INFO] Program interrupted by user
2025-02-09 20:43:36 [INFO] Client starting...
2025-02-09 20:43:36 [INFO] Login successful
2025-02-09 20:43:36 [INFO] Checking registration status...
2025-02-09 20:43:36 [INFO] Client already registered
2025-02-09 20:43:36 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 20:43:36 [INFO] Starting heartbeat...
2025-02-09 20:43:37 [DEBUG] Heartbeat sent successfully
2025-02-09 20:43:38 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:43:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 20:43:38 [INFO] SSH tunnel established
2025-02-09 20:43:43 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:43:43 [INFO] 注册成功
2025-02-09 20:44:39 [DEBUG] Heartbeat sent successfully
2025-02-09 20:44:39 [INFO] 收到消息: start_task
2025-02-09 20:44:39 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 6, 'network_interface': 'ens37'}
2025-02-09 20:45:40 [DEBUG] Heartbeat sent successfully
2025-02-09 20:46:41 [DEBUG] Heartbeat sent successfully
2025-02-09 20:47:42 [DEBUG] Heartbeat sent successfully
2025-02-09 20:48:43 [DEBUG] Heartbeat sent successfully
2025-02-09 20:49:44 [DEBUG] Heartbeat sent successfully
2025-02-09 20:50:43 [INFO] Program interrupted by user
2025-02-09 20:50:51 [INFO] Client starting...
2025-02-09 20:50:52 [INFO] Login successful
2025-02-09 20:50:52 [INFO] Checking registration status...
2025-02-09 20:50:52 [INFO] Client already registered
2025-02-09 20:50:52 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 20:50:52 [INFO] Starting heartbeat...
2025-02-09 20:50:53 [DEBUG] Heartbeat sent successfully
2025-02-09 20:50:53 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 20:50:53 [INFO] SSH tunnel established
2025-02-09 20:50:53 [INFO] 注册成功
2025-02-09 20:51:48 [INFO] 收到消息: start_task
2025-02-09 20:51:48 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 7, 'network_interface': 'ens37'}
2025-02-09 20:51:48 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 20:51:48 [DEBUG] iperf3 服务端测试完成，开始处理结果
2025-02-09 20:51:48 [ERROR] iperf3 服务端测试失败: 'TestResult' object has no attribute 'time'
2025-02-09 20:51:48 [ERROR] 错误详情: Traceback (most recent call last):
  File "/home/<USER>/tss/client/core/task_executor.py", line 58, in handle_task_start
    'time': result.time,
            ^^^^^^^^^^^
AttributeError: 'TestResult' object has no attribute 'time'

2025-02-09 20:51:54 [DEBUG] Heartbeat sent successfully
2025-02-09 20:52:55 [DEBUG] Heartbeat sent successfully
2025-02-09 20:53:56 [DEBUG] Heartbeat sent successfully
2025-02-09 20:54:57 [DEBUG] Heartbeat sent successfully
2025-02-09 20:55:58 [DEBUG] Heartbeat sent successfully
2025-02-09 20:57:00 [DEBUG] Heartbeat sent successfully
2025-02-09 20:58:01 [DEBUG] Heartbeat sent successfully
2025-02-09 20:59:02 [DEBUG] Heartbeat sent successfully
2025-02-09 21:00:03 [DEBUG] Heartbeat sent successfully
2025-02-09 21:01:04 [DEBUG] Heartbeat sent successfully
2025-02-09 21:02:05 [DEBUG] Heartbeat sent successfully
2025-02-09 21:03:06 [DEBUG] Heartbeat sent successfully
2025-02-09 21:03:14 [ERROR] WebSocket连接断开，准备重连
2025-02-09 21:03:14 [INFO] WebSocket连接已安全关闭
2025-02-09 21:03:14 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:03:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:03:17 [INFO] Program interrupted by user
2025-02-09 21:03:17 [INFO] Client starting...
2025-02-09 21:03:18 [INFO] Login successful
2025-02-09 21:03:18 [INFO] Checking registration status...
2025-02-09 21:03:18 [INFO] Client already registered
2025-02-09 21:03:18 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:03:18 [INFO] Starting heartbeat...
2025-02-09 21:03:19 [DEBUG] Heartbeat sent successfully
2025-02-09 21:03:19 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:03:19 [INFO] SSH tunnel established
2025-02-09 21:03:19 [INFO] 注册成功
2025-02-09 21:04:15 [INFO] 收到消息: start_task
2025-02-09 21:04:15 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 8, 'network_interface': 'ens37'}
2025-02-09 21:04:15 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:04:15 [DEBUG] iperf3 服务端测试完成，开始处理结果
2025-02-09 21:04:15 [INFO] iperf3 服务端执行完成，结果: result={"error": "unable to start listener for connections: Temporary failure in name resolution"}
2025-02-09 21:04:15 [INFO] iperf3 服务端结果已发送
2025-02-09 21:04:20 [DEBUG] Heartbeat sent successfully
2025-02-09 21:05:22 [DEBUG] Heartbeat sent successfully
2025-02-09 21:06:23 [DEBUG] Heartbeat sent successfully
2025-02-09 21:06:28 [INFO] Program interrupted by user
2025-02-09 21:12:22 [INFO] Client starting...
2025-02-09 21:12:23 [INFO] Login successful
2025-02-09 21:12:23 [INFO] Checking registration status...
2025-02-09 21:12:23 [INFO] Client already registered
2025-02-09 21:12:23 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:12:23 [INFO] Starting heartbeat...
2025-02-09 21:12:24 [DEBUG] Heartbeat sent successfully
2025-02-09 21:12:24 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:12:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:12:24 [INFO] SSH tunnel established
2025-02-09 21:12:29 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:12:29 [INFO] 注册成功
2025-02-09 21:13:25 [DEBUG] Heartbeat sent successfully
2025-02-09 21:13:25 [INFO] 收到消息: start_task
2025-02-09 21:13:25 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 9, 'network_interface': 'ens37'}
2025-02-09 21:13:25 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:13:25 [INFO] iperf3 服务端执行完成，结果: result={"error": "unable to start listener for connections: Temporary failure in name resolution"}
2025-02-09 21:13:25 [DEBUG] 调用堆栈: ['  File "/home/<USER>/tss/client/main.py", line 114, in <module>\n    main()\n', '  File "/home/<USER>/tss/client/main.py", line 103, in main\n    loop.run_until_complete(async_main())\n', '  File "/usr/lib/python3.12/asyncio/base_events.py", line 674, in run_until_complete\n    self.run_forever()\n', '  File "/usr/lib/python3.12/asyncio/base_events.py", line 641, in run_forever\n    self._run_once()\n', '  File "/usr/lib/python3.12/asyncio/base_events.py", line 1987, in _run_once\n    handle._run()\n', '  File "/usr/lib/python3.12/asyncio/events.py", line 88, in _run\n    self._context.run(self._callback, *self._args)\n', '  File "/home/<USER>/tss/client/core/connection.py", line 126, in start\n    await self.message_handlers[message_type](data)\n', '  File "/home/<USER>/tss/client/core/task_executor.py", line 55, in handle_task_start\n    logger.debug(f"调用堆栈: {traceback.format_stack()}")\n']
2025-02-09 21:14:26 [DEBUG] Heartbeat sent successfully
2025-02-09 21:14:49 [ERROR] WebSocket连接断开，准备重连
2025-02-09 21:14:49 [INFO] WebSocket连接已安全关闭
2025-02-09 21:14:49 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:14:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:14:53 [INFO] Program interrupted by user
2025-02-09 21:17:46 [INFO] Client starting...
2025-02-09 21:17:47 [INFO] Login successful
2025-02-09 21:17:47 [INFO] Checking registration status...
2025-02-09 21:17:47 [INFO] Client already registered
2025-02-09 21:17:47 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:17:47 [INFO] Starting heartbeat...
2025-02-09 21:17:48 [DEBUG] Heartbeat sent successfully
2025-02-09 21:17:48 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:17:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:17:48 [INFO] SSH tunnel established
2025-02-09 21:17:53 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:17:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:17:58 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:17:58 [INFO] 注册成功
2025-02-09 21:18:49 [DEBUG] Heartbeat sent successfully
2025-02-09 21:19:50 [DEBUG] Heartbeat sent successfully
2025-02-09 21:20:51 [DEBUG] Heartbeat sent successfully
2025-02-09 21:20:53 [INFO] 收到消息: start_task
2025-02-09 21:20:53 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 10, 'network_interface': 'ens37'}
2025-02-09 21:20:53 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:20:53 [DEBUG] iperf3 服务端配置: port=39999, bind_address=ens37
2025-02-09 21:20:53 [INFO] iperf3 服务端执行完成，结果: {"error": "unable to start listener for connections: Temporary failure in name resolution"}
2025-02-09 21:20:53 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 21:20:54 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 21:20:55 [ERROR] 发送消息失败: Object of type TestResult is not JSON serializable
2025-02-09 21:20:55 [INFO] iperf3 服务端结果已发送
2025-02-09 21:21:44 [INFO] Program interrupted by user
2025-02-09 21:22:14 [INFO] Client starting...
2025-02-09 21:22:14 [INFO] Login successful
2025-02-09 21:22:14 [INFO] Checking registration status...
2025-02-09 21:22:14 [INFO] Client already registered
2025-02-09 21:22:14 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:22:14 [INFO] Starting heartbeat...
2025-02-09 21:22:15 [DEBUG] Heartbeat sent successfully
2025-02-09 21:22:15 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:22:15 [INFO] SSH tunnel established
2025-02-09 21:22:15 [INFO] 注册成功
2025-02-09 21:22:17 [INFO] Program interrupted by user
2025-02-09 21:22:17 [ERROR] 关闭WebSocket连接时发生错误: no running event loop
2025-02-09 21:22:17 [INFO] 连接管理器已停止
2025-02-09 21:22:55 [INFO] Client starting...
2025-02-09 21:22:56 [INFO] Login successful
2025-02-09 21:22:56 [INFO] Checking registration status...
2025-02-09 21:22:56 [INFO] Client already registered
2025-02-09 21:22:56 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:22:56 [INFO] Starting heartbeat...
2025-02-09 21:22:57 [DEBUG] Heartbeat sent successfully
2025-02-09 21:22:57 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:22:57 [INFO] SSH tunnel established
2025-02-09 21:22:57 [INFO] 注册成功
2025-02-09 21:22:58 [INFO] Program interrupted by user
2025-02-09 21:22:58 [ERROR] 关闭WebSocket连接时发生错误: no running event loop
2025-02-09 21:22:58 [INFO] 连接管理器已停止
2025-02-09 21:23:14 [INFO] Client starting...
2025-02-09 21:23:15 [INFO] Login successful
2025-02-09 21:23:15 [INFO] Checking registration status...
2025-02-09 21:23:15 [INFO] Client already registered
2025-02-09 21:23:15 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:23:15 [INFO] Starting heartbeat...
2025-02-09 21:23:16 [DEBUG] Heartbeat sent successfully
2025-02-09 21:23:16 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:23:16 [INFO] SSH tunnel established
2025-02-09 21:23:16 [INFO] 注册成功
2025-02-09 21:23:18 [INFO] Program interrupted by user
2025-02-09 21:23:18 [ERROR] 关闭WebSocket连接时发生错误: no running event loop
2025-02-09 21:23:18 [INFO] 连接管理器已停止
2025-02-09 21:28:47 [INFO] Client starting...
2025-02-09 21:28:48 [INFO] Login successful
2025-02-09 21:28:48 [INFO] Checking registration status...
2025-02-09 21:28:48 [INFO] Client already registered
2025-02-09 21:28:48 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:28:48 [INFO] Starting heartbeat...
2025-02-09 21:28:49 [DEBUG] Heartbeat sent successfully
2025-02-09 21:28:49 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:28:49 [INFO] SSH tunnel established
2025-02-09 21:28:49 [INFO] 注册成功
2025-02-09 21:29:50 [DEBUG] Heartbeat sent successfully
2025-02-09 21:29:53 [INFO] 收到消息: start_task
2025-02-09 21:29:53 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 11, 'network_interface': 'ens37'}
2025-02-09 21:29:53 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:29:53 [DEBUG] iperf3 服务端配置: port=39999, bind_address=*
2025-02-09 21:30:51 [DEBUG] Heartbeat sent successfully
2025-02-09 21:31:52 [DEBUG] Heartbeat sent successfully
2025-02-09 21:32:54 [DEBUG] Heartbeat sent successfully
2025-02-09 21:33:55 [DEBUG] Heartbeat sent successfully
2025-02-09 21:34:56 [DEBUG] Heartbeat sent successfully
2025-02-09 21:35:57 [DEBUG] Heartbeat sent successfully
2025-02-09 21:36:58 [DEBUG] Heartbeat sent successfully
2025-02-09 21:37:59 [DEBUG] Heartbeat sent successfully
2025-02-09 21:39:00 [DEBUG] Heartbeat sent successfully
2025-02-09 21:40:01 [DEBUG] Heartbeat sent successfully
2025-02-09 21:41:02 [DEBUG] Heartbeat sent successfully
2025-02-09 21:42:04 [DEBUG] Heartbeat sent successfully
2025-02-09 21:42:36 [INFO] Program interrupted by user
2025-02-09 21:42:43 [INFO] Client starting...
2025-02-09 21:42:44 [INFO] Login successful
2025-02-09 21:42:44 [INFO] Checking registration status...
2025-02-09 21:42:44 [INFO] Client already registered
2025-02-09 21:42:44 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:42:44 [INFO] Starting heartbeat...
2025-02-09 21:42:45 [DEBUG] Heartbeat sent successfully
2025-02-09 21:42:45 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:42:45 [INFO] SSH tunnel established
2025-02-09 21:42:45 [INFO] 注册成功
2025-02-09 21:43:40 [INFO] 收到消息: start_task
2025-02-09 21:43:40 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 13, 'network_interface': 'ens37'}
2025-02-09 21:43:40 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:43:40 [DEBUG] iperf3 服务端配置: port=39999, bind_address=*
2025-02-09 21:43:46 [DEBUG] Heartbeat sent successfully
2025-02-09 21:44:47 [DEBUG] Heartbeat sent successfully
2025-02-09 21:45:48 [DEBUG] Heartbeat sent successfully
2025-02-09 21:46:49 [DEBUG] Heartbeat sent successfully
2025-02-09 21:47:50 [DEBUG] Heartbeat sent successfully
2025-02-09 21:48:52 [DEBUG] Heartbeat sent successfully
2025-02-09 21:49:53 [DEBUG] Heartbeat sent successfully
2025-02-09 21:50:54 [DEBUG] Heartbeat sent successfully
2025-02-09 21:51:55 [DEBUG] Heartbeat sent successfully
2025-02-09 21:52:07 [INFO] Program interrupted by user
2025-02-09 21:52:09 [INFO] Client starting...
2025-02-09 21:52:10 [INFO] Login successful
2025-02-09 21:52:10 [INFO] Checking registration status...
2025-02-09 21:52:10 [INFO] Client already registered
2025-02-09 21:52:10 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:52:10 [INFO] Starting heartbeat...
2025-02-09 21:52:11 [DEBUG] Heartbeat sent successfully
2025-02-09 21:52:11 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:52:11 [INFO] SSH tunnel established
2025-02-09 21:52:11 [INFO] 注册成功
2025-02-09 21:52:15 [ERROR] WebSocket连接断开，准备重连
2025-02-09 21:52:15 [INFO] WebSocket连接已安全关闭
2025-02-09 21:52:15 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:52:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:52:20 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:52:20 [INFO] 注册成功
2025-02-09 21:53:12 [DEBUG] Heartbeat sent successfully
2025-02-09 21:53:16 [INFO] 收到消息: start_task
2025-02-09 21:53:16 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 14, 'network_interface': 'ens37'}
2025-02-09 21:53:16 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:53:16 [DEBUG] iperf3 服务端配置: port=39999
2025-02-09 21:53:44 [INFO] Program interrupted by user
2025-02-09 21:57:01 [INFO] Client starting...
2025-02-09 21:57:02 [INFO] Login successful
2025-02-09 21:57:02 [INFO] Checking registration status...
2025-02-09 21:57:02 [INFO] Client already registered
2025-02-09 21:57:02 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 21:57:02 [INFO] Starting heartbeat...
2025-02-09 21:57:03 [DEBUG] Heartbeat sent successfully
2025-02-09 21:57:03 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:57:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 21:57:03 [INFO] SSH tunnel established
2025-02-09 21:57:08 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 21:57:08 [INFO] 注册成功
2025-02-09 21:58:04 [DEBUG] Heartbeat sent successfully
2025-02-09 21:58:04 [INFO] 收到消息: start_task
2025-02-09 21:58:04 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 15, 'network_interface': 'ens37'}
2025-02-09 21:58:04 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 21:58:04 [DEBUG] iperf3 服务端配置: port=39999
2025-02-09 21:59:05 [DEBUG] Heartbeat sent successfully
2025-02-09 22:00:06 [DEBUG] Heartbeat sent successfully
2025-02-09 22:01:07 [DEBUG] Heartbeat sent successfully
2025-02-09 22:02:08 [DEBUG] Heartbeat sent successfully
2025-02-09 22:03:09 [DEBUG] Heartbeat sent successfully
2025-02-09 22:03:43 [INFO] Program interrupted by user
2025-02-09 22:03:44 [INFO] Client starting...
2025-02-09 22:03:45 [INFO] Login successful
2025-02-09 22:03:45 [INFO] Checking registration status...
2025-02-09 22:03:45 [INFO] Client already registered
2025-02-09 22:03:45 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 22:03:45 [INFO] Starting heartbeat...
2025-02-09 22:03:46 [DEBUG] Heartbeat sent successfully
2025-02-09 22:03:46 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:03:46 [INFO] SSH tunnel established
2025-02-09 22:03:46 [INFO] 注册成功
2025-02-09 22:03:49 [ERROR] WebSocket连接断开，准备重连
2025-02-09 22:03:49 [INFO] WebSocket连接已安全关闭
2025-02-09 22:03:49 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:03:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 22:03:54 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:03:54 [INFO] 注册成功
2025-02-09 22:04:47 [DEBUG] Heartbeat sent successfully
2025-02-09 22:04:49 [INFO] 收到消息: start_task
2025-02-09 22:04:49 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 16, 'network_interface': 'ens37'}
2025-02-09 22:04:49 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 22:04:49 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=::
2025-02-09 22:05:48 [DEBUG] Heartbeat sent successfully
2025-02-09 22:06:49 [DEBUG] Heartbeat sent successfully
2025-02-09 22:07:50 [DEBUG] Heartbeat sent successfully
2025-02-09 22:08:52 [DEBUG] Heartbeat sent successfully
2025-02-09 22:09:17 [INFO] Program interrupted by user
2025-02-09 22:09:23 [INFO] Client starting...
2025-02-09 22:09:24 [INFO] Login successful
2025-02-09 22:09:24 [INFO] Checking registration status...
2025-02-09 22:09:24 [INFO] Client already registered
2025-02-09 22:09:24 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 22:09:24 [INFO] Starting heartbeat...
2025-02-09 22:09:25 [DEBUG] Heartbeat sent successfully
2025-02-09 22:09:25 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:09:25 [INFO] SSH tunnel established
2025-02-09 22:09:25 [INFO] 注册成功
2025-02-09 22:10:20 [INFO] 收到消息: start_task
2025-02-09 22:10:20 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 17, 'network_interface': 'ens37'}
2025-02-09 22:10:20 [INFO] iperf3 服务端启动配置:
2025-02-09 22:10:20 [INFO]   - 端口: 39999
2025-02-09 22:10:20 [INFO]   - IPv6模式: True
2025-02-09 22:10:20 [INFO]   - 绑定地址: ::
2025-02-09 22:10:20 [INFO]   - 网卡: ens37
2025-02-09 22:10:20 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 22:10:20 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=::
2025-02-09 22:10:26 [DEBUG] Heartbeat sent successfully
2025-02-09 22:11:27 [DEBUG] Heartbeat sent successfully
2025-02-09 22:12:28 [DEBUG] Heartbeat sent successfully
2025-02-09 22:13:29 [DEBUG] Heartbeat sent successfully
2025-02-09 22:14:30 [DEBUG] Heartbeat sent successfully
2025-02-09 22:15:31 [DEBUG] Heartbeat sent successfully
2025-02-09 22:16:33 [DEBUG] Heartbeat sent successfully
2025-02-09 22:17:34 [DEBUG] Heartbeat sent successfully
2025-02-09 22:18:35 [DEBUG] Heartbeat sent successfully
2025-02-09 22:19:36 [DEBUG] Heartbeat sent successfully
2025-02-09 22:20:37 [DEBUG] Heartbeat sent successfully
2025-02-09 22:21:38 [DEBUG] Heartbeat sent successfully
2025-02-09 22:22:39 [DEBUG] Heartbeat sent successfully
2025-02-09 22:23:40 [DEBUG] Heartbeat sent successfully
2025-02-09 22:24:42 [DEBUG] Heartbeat sent successfully
2025-02-09 22:25:43 [DEBUG] Heartbeat sent successfully
2025-02-09 22:26:44 [DEBUG] Heartbeat sent successfully
2025-02-09 22:27:45 [DEBUG] Heartbeat sent successfully
2025-02-09 22:28:46 [DEBUG] Heartbeat sent successfully
2025-02-09 22:29:26 [INFO] Program interrupted by user
2025-02-09 22:29:28 [INFO] Client starting...
2025-02-09 22:29:29 [INFO] Login successful
2025-02-09 22:29:29 [INFO] Checking registration status...
2025-02-09 22:29:29 [INFO] Client already registered
2025-02-09 22:29:29 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 22:29:29 [INFO] Starting heartbeat...
2025-02-09 22:29:30 [DEBUG] Heartbeat sent successfully
2025-02-09 22:29:30 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:29:30 [INFO] SSH tunnel established
2025-02-09 22:29:30 [INFO] 注册成功
2025-02-09 22:29:34 [ERROR] WebSocket连接断开，准备重连
2025-02-09 22:29:34 [INFO] WebSocket连接已安全关闭
2025-02-09 22:29:34 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:29:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 22:29:39 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 22:29:39 [INFO] 注册成功
2025-02-09 22:30:31 [DEBUG] Heartbeat sent successfully
2025-02-09 22:30:35 [INFO] 收到消息: start_task
2025-02-09 22:30:35 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 18, 'interface': 'fd15:4ba5:5a2b:1008::104'}
2025-02-09 22:30:35 [INFO] iperf3 服务端启动配置:
2025-02-09 22:30:35 [INFO]   - 端口: 39999
2025-02-09 22:30:35 [INFO]   - IPv6模式: True
2025-02-09 22:30:35 [INFO]   - 绑定地址: fd15:4ba5:5a2b:1008::104
2025-02-09 22:30:35 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 22:30:35 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=fd15:4ba5:5a2b:1008::104
2025-02-09 22:31:32 [DEBUG] Heartbeat sent successfully
2025-02-09 22:32:34 [DEBUG] Heartbeat sent successfully
2025-02-09 22:33:35 [DEBUG] Heartbeat sent successfully
2025-02-09 22:34:36 [DEBUG] Heartbeat sent successfully
2025-02-09 22:35:37 [DEBUG] Heartbeat sent successfully
2025-02-09 22:36:38 [DEBUG] Heartbeat sent successfully
2025-02-09 22:37:39 [DEBUG] Heartbeat sent successfully
2025-02-09 22:38:40 [DEBUG] Heartbeat sent successfully
2025-02-09 22:39:41 [DEBUG] Heartbeat sent successfully
2025-02-09 23:22:46 [INFO] Client starting...
2025-02-09 23:22:52 [ERROR] Login failed with status code: 404
2025-02-09 23:22:52 [ERROR] Authentication failed, exiting...
2025-02-09 23:22:52 [INFO] Client stopped
2025-02-09 23:24:10 [INFO] Client starting...
2025-02-09 23:24:11 [INFO] 收到中断信号，正在停止...
2025-02-09 23:24:11 [INFO] Client stopped
2025-02-09 23:25:05 [INFO] Client starting...
2025-02-09 23:25:06 [INFO] Login successful
2025-02-09 23:25:06 [INFO] Checking registration status...
2025-02-09 23:25:06 [INFO] Client already registered
2025-02-09 23:25:06 [INFO] Starting SSH tunnel to **************:40000
2025-02-09 23:25:06 [INFO] Starting heartbeat...
2025-02-09 23:25:07 [DEBUG] Heartbeat sent successfully
2025-02-09 23:25:07 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 23:25:07 [INFO] SSH tunnel established
2025-02-09 23:25:07 [INFO] 注册成功
2025-02-09 23:25:50 [INFO] 收到消息: start_task
2025-02-09 23:25:50 [INFO] 收到任务启动请求: task_id=67_server, config={'type': 'iperf3_server', 'port': 39999, 'result_id': 19, 'interface': 'fd15:4ba5:5a2b:1008::104'}
2025-02-09 23:25:50 [INFO] iperf3 服务端启动配置:
2025-02-09 23:25:50 [INFO]   - 端口: 39999
2025-02-09 23:25:50 [INFO]   - IPv6模式: True
2025-02-09 23:25:50 [INFO]   - 绑定地址: fd15:4ba5:5a2b:1008::104
2025-02-09 23:25:50 [INFO] 开始执行 iperf3 服务端测试...
2025-02-09 23:25:50 [DEBUG] iperf3 服务端配置: port=39999, ipv6_only=True, bind_address=fd15:4ba5:5a2b:1008::104
2025-02-09 23:26:08 [DEBUG] Heartbeat sent successfully
2025-02-09 23:26:52 [INFO] === iperf3 服务端结果 ===
2025-02-09 23:26:52 [DEBUG] 结果对象: {
	"start":	{
		"connected":	[{
				"socket":	17,
				"local_host":	"fd15:4ba5:5a2b:1008::104",
				"local_port":	39999,
				"remote_host":	"fd15:4ba5:5a2b:1008::105",
				"remote_port":	59355
			}],
		"version":	"iperf 3.16",
		"system_info":	"Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64",
		"target_bitrate":	50000000,
		"sock_bufsize":	0,
		"sndbuf_actual":	16384,
		"rcvbuf_actual":	131072,
		"timestamp":	{
			"time":	"Sun, 09 Feb 2025 15:25:52 GMT",
			"timesecs":	1739114752
		},
		"accepted_connection":	{
			"host":	"fd15:4ba5:5a2b:1008::105",
			"port":	36785
		},
		"cookie":	"kqne2mbzo6x7ihpdljp245ijqr6qllnv3gvm",
		"tcp_mss_default":	0,
		"target_bitrate":	50000000,
		"fq_rate":	0,
		"test_start":	{
			"protocol":	"TCP",
			"num_streams":	1,
			"blksize":	131072,
			"omit":	0,
			"duration":	60,
			"bytes":	0,
			"blocks":	0,
			"reverse":	0,
			"tos":	0,
			"target_bitrate":	50000000,
			"bidir":	0,
			"fqrate":	0
		}
	},
	"intervals":	[{
			"streams":	[{
					"socket":	17,
					"start":	0,
					"end":	1.000466,
					"seconds":	1.000465989112854,
					"bytes":	6291456,
					"bits_per_second":	50308204.924218088,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	0,
				"end":	1.000466,
				"seconds":	1.000465989112854,
				"bytes":	6291456,
				"bits_per_second":	50308204.924218088,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	1.000466,
					"end":	2.001137,
					"seconds":	1.0006710290908813,
					"bytes":	6291456,
					"bits_per_second":	50297896.648138955,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	1.000466,
				"end":	2.001137,
				"seconds":	1.0006710290908813,
				"bytes":	6291456,
				"bits_per_second":	50297896.648138955,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	2.001137,
					"end":	3.001158,
					"seconds":	1.0000209808349609,
					"bytes":	6291456,
					"bits_per_second":	50330592.0221553,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	2.001137,
				"end":	3.001158,
				"seconds":	1.0000209808349609,
				"bytes":	6291456,
				"bits_per_second":	50330592.0221553,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	3.001158,
					"end":	4.001061,
					"seconds":	0.99990302324295044,
					"bytes":	6160384,
					"bits_per_second":	49287851.77602718,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	3.001158,
				"end":	4.001061,
				"seconds":	0.99990302324295044,
				"bytes":	6160384,
				"bits_per_second":	49287851.77602718,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	4.001061,
					"end":	5.00106,
					"seconds":	0.99999898672103882,
					"bytes":	6291456,
					"bits_per_second":	50331699.000051677,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	4.001061,
				"end":	5.00106,
				"seconds":	0.99999898672103882,
				"bytes":	6291456,
				"bits_per_second":	50331699.000051677,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	5.00106,
					"end":	6.00123,
					"seconds":	1.0001699924468994,
					"bytes":	6291456,
					"bits_per_second":	50323093.454208173,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	5.00106,
				"end":	6.00123,
				"seconds":	1.0001699924468994,
				"bytes":	6291456,
				"bits_per_second":	50323093.454208173,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	6.00123,
					"end":	7.001326,
					"seconds":	1.0000959634780884,
					"bytes":	6160384,
					"bits_per_second":	49278343.078803726,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	6.00123,
				"end":	7.001326,
				"seconds":	1.0000959634780884,
				"bytes":	6160384,
				"bits_per_second":	49278343.078803726,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	7.001326,
					"end":	8.001085,
					"seconds":	0.999759018421173,
					"bytes":	6291456,
					"bits_per_second":	50343779.9235701,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	7.001326,
				"end":	8.001085,
				"seconds":	0.999759018421173,
				"bytes":	6291456,
				"bits_per_second":	50343779.9235701,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	8.001085,
					"end":	9.000482,
					"seconds":	0.99939697980880737,
					"bytes":	6291456,
					"bits_per_second":	50362017.313309118,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	8.001085,
				"end":	9.000482,
				"seconds":	0.99939697980880737,
				"bytes":	6291456,
				"bits_per_second":	50362017.313309118,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	9.000482,
					"end":	10.0012,
					"seconds":	1.0007179975509644,
					"bytes":	6160384,
					"bits_per_second":	49247712.263204426,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	9.000482,
				"end":	10.0012,
				"seconds":	1.0007179975509644,
				"bytes":	6160384,
				"bits_per_second":	49247712.263204426,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	10.0012,
					"end":	11.001309,
					"seconds":	1.0001089572906494,
					"bytes":	6291456,
					"bits_per_second":	50326164.597456686,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	10.0012,
				"end":	11.001309,
				"seconds":	1.0001089572906494,
				"bytes":	6291456,
				"bits_per_second":	50326164.597456686,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	11.001309,
					"end":	12.001232,
					"seconds":	0.9999229907989502,
					"bytes":	6291456,
					"bits_per_second":	50335524.298510648,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	11.001309,
				"end":	12.001232,
				"seconds":	0.9999229907989502,
				"bytes":	6291456,
				"bits_per_second":	50335524.298510648,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	12.001232,
					"end":	13.001188,
					"seconds":	0.99995601177215576,
					"bytes":	6160384,
					"bits_per_second":	49285239.970365174,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	12.001232,
				"end":	13.001188,
				"seconds":	0.99995601177215576,
				"bytes":	6160384,
				"bits_per_second":	49285239.970365174,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	13.001188,
					"end":	14.001112,
					"seconds":	0.99992400407791138,
					"bytes":	6291456,
					"bits_per_second":	50335473.2907065,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	13.001188,
				"end":	14.001112,
				"seconds":	0.99992400407791138,
				"bytes":	6291456,
				"bits_per_second":	50335473.2907065,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	14.001112,
					"end":	15.001293,
					"seconds":	1.0001809597015381,
					"bytes":	6291456,
					"bits_per_second":	50322541.64788276,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	14.001112,
				"end":	15.001293,
				"seconds":	1.0001809597015381,
				"bytes":	6291456,
				"bits_per_second":	50322541.64788276,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	15.001293,
					"end":	16.001202,
					"seconds":	0.999908983707428,
					"bytes":	6160384,
					"bits_per_second":	49287557.970796429,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	15.001293,
				"end":	16.001202,
				"seconds":	0.999908983707428,
				"bytes":	6160384,
				"bits_per_second":	49287557.970796429,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	16.001202,
					"end":	17.001094,
					"seconds":	0.999891996383667,
					"bytes":	6291456,
					"bits_per_second":	50337084.587171078,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	16.001202,
				"end":	17.001094,
				"seconds":	0.999891996383667,
				"bytes":	6291456,
				"bits_per_second":	50337084.587171078,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	17.001094,
					"end":	18.001255,
					"seconds":	1.0001610517501831,
					"bytes":	6291456,
					"bits_per_second":	50323543.305275269,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	17.001094,
				"end":	18.001255,
				"seconds":	1.0001610517501831,
				"bytes":	6291456,
				"bits_per_second":	50323543.305275269,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	18.001255,
					"end":	19.001112,
					"seconds":	0.999857008457184,
					"bytes":	6160384,
					"bits_per_second":	49290120.070314445,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	18.001255,
				"end":	19.001112,
				"seconds":	0.999857008457184,
				"bytes":	6160384,
				"bits_per_second":	49290120.070314445,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	19.001112,
					"end":	20.00114,
					"seconds":	1.0000280141830444,
					"bytes":	6291456,
					"bits_per_second":	50330238.039498888,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	19.001112,
				"end":	20.00114,
				"seconds":	1.0000280141830444,
				"bytes":	6291456,
				"bits_per_second":	50330238.039498888,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	20.00114,
					"end":	21.001213,
					"seconds":	1.0000729560852051,
					"bytes":	6291456,
					"bits_per_second":	50327976.2678752,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	20.00114,
				"end":	21.001213,
				"seconds":	1.0000729560852051,
				"bytes":	6291456,
				"bits_per_second":	50327976.2678752,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	21.001213,
					"end":	22.001192,
					"seconds":	0.999979019165039,
					"bytes":	6291456,
					"bits_per_second":	50332704.022156224,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	21.001213,
				"end":	22.001192,
				"seconds":	0.999979019165039,
				"bytes":	6291456,
				"bits_per_second":	50332704.022156224,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	22.001192,
					"end":	23.001314,
					"seconds":	1.0001219511032104,
					"bytes":	6160384,
					"bits_per_second":	49277062.607852,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	22.001192,
				"end":	23.001314,
				"seconds":	1.0001219511032104,
				"bytes":	6160384,
				"bits_per_second":	49277062.607852,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	23.001314,
					"end":	24.00118,
					"seconds":	0.999866008758545,
					"bytes":	6291456,
					"bits_per_second":	50338392.903758027,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	23.001314,
				"end":	24.00118,
				"seconds":	0.999866008758545,
				"bytes":	6291456,
				"bits_per_second":	50338392.903758027,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	24.00118,
					"end":	25.001062,
					"seconds":	0.99988198280334473,
					"bytes":	6291456,
					"bits_per_second":	50337588.7011049,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	24.00118,
				"end":	25.001062,
				"seconds":	0.99988198280334473,
				"bytes":	6291456,
				"bits_per_second":	50337588.7011049,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	25.001062,
					"end":	26.001085,
					"seconds":	1.0000230073928833,
					"bytes":	6160384,
					"bits_per_second":	49281938.1510869,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	25.001062,
				"end":	26.001085,
				"seconds":	1.0000230073928833,
				"bytes":	6160384,
				"bits_per_second":	49281938.1510869,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	26.001085,
					"end":	27.00112,
					"seconds":	1.0000350475311279,
					"bytes":	6291456,
					"bits_per_second":	50329884.061821677,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	26.001085,
				"end":	27.00112,
				"seconds":	1.0000350475311279,
				"bytes":	6291456,
				"bits_per_second":	50329884.061821677,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	27.00112,
					"end":	28.001187,
					"seconds":	1.0000669956207275,
					"bytes":	6291456,
					"bits_per_second":	50328276.2258941,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	27.00112,
				"end":	28.001187,
				"seconds":	1.0000669956207275,
				"bytes":	6291456,
				"bits_per_second":	50328276.2258941,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	28.001187,
					"end":	29.00113,
					"seconds":	0.99994301795959473,
					"bytes":	6160384,
					"bits_per_second":	49285880.410028934,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	28.001187,
				"end":	29.00113,
				"seconds":	0.99994301795959473,
				"bytes":	6160384,
				"bits_per_second":	49285880.410028934,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	29.00113,
					"end":	30.001122,
					"seconds":	0.9999920129776,
					"bytes":	6291456,
					"bits_per_second":	50332050.0032108,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	29.00113,
				"end":	30.001122,
				"seconds":	0.9999920129776,
				"bytes":	6291456,
				"bits_per_second":	50332050.0032108,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	30.001122,
					"end":	31.001086,
					"seconds":	0.99996399879455566,
					"bytes":	6291456,
					"bits_per_second":	50333460.065236531,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	30.001122,
				"end":	31.001086,
				"seconds":	0.99996399879455566,
				"bytes":	6291456,
				"bits_per_second":	50333460.065236531,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	31.001086,
					"end":	32.001082,
					"seconds":	0.9999960064888,
					"bytes":	6160384,
					"bits_per_second":	49283268.813285977,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	31.001086,
				"end":	32.001082,
				"seconds":	0.9999960064888,
				"bytes":	6160384,
				"bits_per_second":	49283268.813285977,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	32.001082,
					"end":	33.000413,
					"seconds":	0.999330997467041,
					"bytes":	6291456,
					"bits_per_second":	50365342.541733764,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	32.001082,
				"end":	33.000413,
				"seconds":	0.999330997467041,
				"bytes":	6291456,
				"bits_per_second":	50365342.541733764,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	33.000413,
					"end":	34.00105,
					"seconds":	1.0006370544433594,
					"bytes":	6291456,
					"bits_per_second":	50299604.413509153,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	33.000413,
				"end":	34.00105,
				"seconds":	1.0006370544433594,
				"bytes":	6291456,
				"bits_per_second":	50299604.413509153,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	34.00105,
					"end":	35.00122,
					"seconds":	1.0001699924468994,
					"bytes":	6160384,
					"bits_per_second":	49274695.673912168,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	34.00105,
				"end":	35.00122,
				"seconds":	1.0001699924468994,
				"bytes":	6160384,
				"bits_per_second":	49274695.673912168,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	35.00122,
					"end":	36.001167,
					"seconds":	0.99994701147079468,
					"bytes":	6291456,
					"bits_per_second":	50334315.1413279,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	35.00122,
				"end":	36.001167,
				"seconds":	0.99994701147079468,
				"bytes":	6291456,
				"bits_per_second":	50334315.1413279,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	36.001167,
					"end":	37.001036,
					"seconds":	0.99986898899078369,
					"bytes":	6291456,
					"bits_per_second":	50338242.8639998,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	36.001167,
				"end":	37.001036,
				"seconds":	0.99986898899078369,
				"bytes":	6291456,
				"bits_per_second":	50338242.8639998,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	37.001036,
					"end":	38.001242,
					"seconds":	1.0002059936523438,
					"bytes":	6160384,
					"bits_per_second":	49272922.090816863,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	37.001036,
				"end":	38.001242,
				"seconds":	1.0002059936523438,
				"bytes":	6160384,
				"bits_per_second":	49272922.090816863,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	38.001242,
					"end":	39.001106,
					"seconds":	0.99986398220062256,
					"bytes":	6291456,
					"bits_per_second":	50338494.931304529,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	38.001242,
				"end":	39.001106,
				"seconds":	0.99986398220062256,
				"bytes":	6291456,
				"bits_per_second":	50338494.931304529,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	39.001106,
					"end":	40.000483,
					"seconds":	0.99937701225280762,
					"bytes":	6291456,
					"bits_per_second":	50363023.546581082,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	39.001106,
				"end":	40.000483,
				"seconds":	0.99937701225280762,
				"bytes":	6291456,
				"bits_per_second":	50363023.546581082,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	40.000483,
					"end":	41.001149,
					"seconds":	1.00066602230072,
					"bytes":	6160384,
					"bits_per_second":	49250270.221715838,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	40.000483,
				"end":	41.001149,
				"seconds":	1.00066602230072,
				"bytes":	6160384,
				"bits_per_second":	49250270.221715838,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	41.001149,
					"end":	42.001047,
					"seconds":	0.99989801645278931,
					"bytes":	6291456,
					"bits_per_second":	50336781.523534939,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	41.001149,
				"end":	42.001047,
				"seconds":	0.99989801645278931,
				"bytes":	6291456,
				"bits_per_second":	50336781.523534939,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	42.001047,
					"end":	43.001156,
					"seconds":	1.0001089572906494,
					"bytes":	6291456,
					"bits_per_second":	50326164.597456686,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	42.001047,
				"end":	43.001156,
				"seconds":	1.0001089572906494,
				"bytes":	6291456,
				"bits_per_second":	50326164.597456686,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	43.001156,
					"end":	44.001059,
					"seconds":	0.99990302324295044,
					"bytes":	6291456,
					"bits_per_second":	50336529.473389462,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	43.001156,
				"end":	44.001059,
				"seconds":	0.99990302324295044,
				"bytes":	6291456,
				"bits_per_second":	50336529.473389462,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	44.001059,
					"end":	45.001086,
					"seconds":	1.0000269412994385,
					"bytes":	6160384,
					"bits_per_second":	49281744.285770349,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	44.001059,
				"end":	45.001086,
				"seconds":	1.0000269412994385,
				"bytes":	6160384,
				"bits_per_second":	49281744.285770349,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	45.001086,
					"end":	46.001089,
					"seconds":	1.0000029802322388,
					"bytes":	6291456,
					"bits_per_second":	50331498.000447035,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	45.001086,
				"end":	46.001089,
				"seconds":	1.0000029802322388,
				"bytes":	6291456,
				"bits_per_second":	50331498.000447035,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	46.001089,
					"end":	47.00109,
					"seconds":	1.0000009536743164,
					"bytes":	6291456,
					"bits_per_second":	50331600.000045776,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	46.001089,
				"end":	47.00109,
				"seconds":	1.0000009536743164,
				"bytes":	6291456,
				"bits_per_second":	50331600.000045776,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	47.00109,
					"end":	48.001215,
					"seconds":	1.0001250505447388,
					"bytes":	6160384,
					"bits_per_second":	49276909.895574518,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	47.00109,
				"end":	48.001215,
				"seconds":	1.0001250505447388,
				"bytes":	6160384,
				"bits_per_second":	49276909.895574518,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	48.001215,
					"end":	49.001047,
					"seconds":	0.99983197450637817,
					"bytes":	6291456,
					"bits_per_second":	50340106.4212304,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	48.001215,
				"end":	49.001047,
				"seconds":	0.99983197450637817,
				"bytes":	6291456,
				"bits_per_second":	50340106.4212304,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	49.001047,
					"end":	50.001103,
					"seconds":	1.0000560283660889,
					"bytes":	6291456,
					"bits_per_second":	50328828.157991141,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	49.001047,
				"end":	50.001103,
				"seconds":	1.0000560283660889,
				"bytes":	6291456,
				"bits_per_second":	50328828.157991141,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	50.001103,
					"end":	51.001076,
					"seconds":	0.99997299909591675,
					"bytes":	6160384,
					"bits_per_second":	49284402.723430738,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	50.001103,
				"end":	51.001076,
				"seconds":	0.99997299909591675,
				"bytes":	6160384,
				"bits_per_second":	49284402.723430738,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	51.001076,
					"end":	52.001185,
					"seconds":	1.0001089572906494,
					"bytes":	6291456,
					"bits_per_second":	50326164.597456686,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	51.001076,
				"end":	52.001185,
				"seconds":	1.0001089572906494,
				"bytes":	6291456,
				"bits_per_second":	50326164.597456686,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	52.001185,
					"end":	53.001042,
					"seconds":	0.999857008457184,
					"bytes":	6291456,
					"bits_per_second":	50338846.029257312,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	52.001185,
				"end":	53.001042,
				"seconds":	0.999857008457184,
				"bytes":	6291456,
				"bits_per_second":	50338846.029257312,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	53.001042,
					"end":	54.001176,
					"seconds":	1.0001339912414551,
					"bytes":	6160384,
					"bits_per_second":	49276469.384692624,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	53.001042,
				"end":	54.001176,
				"seconds":	1.0001339912414551,
				"bytes":	6160384,
				"bits_per_second":	49276469.384692624,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	54.001176,
					"end":	55.001206,
					"seconds":	1.0000300407409668,
					"bytes":	6291456,
					"bits_per_second":	50330136.045420237,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	54.001176,
				"end":	55.001206,
				"seconds":	1.0000300407409668,
				"bytes":	6291456,
				"bits_per_second":	50330136.045420237,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	55.001206,
					"end":	56.001073,
					"seconds":	0.999867022037506,
					"bytes":	6291456,
					"bits_per_second":	50338341.89013987,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	55.001206,
				"end":	56.001073,
				"seconds":	0.999867022037506,
				"bytes":	6291456,
				"bits_per_second":	50338341.89013987,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	56.001073,
					"end":	57.00053,
					"seconds":	0.99945700168609619,
					"bytes":	6160384,
					"bits_per_second":	49309847.163868837,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	56.001073,
				"end":	57.00053,
				"seconds":	0.99945700168609619,
				"bytes":	6160384,
				"bits_per_second":	49309847.163868837,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	57.00053,
					"end":	58.001084,
					"seconds":	1.0005539655685425,
					"bytes":	6291456,
					"bits_per_second":	50303781.437116355,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	57.00053,
				"end":	58.001084,
				"seconds":	1.0005539655685425,
				"bytes":	6291456,
				"bits_per_second":	50303781.437116355,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	58.001084,
					"end":	59.001149,
					"seconds":	1.0000649690628052,
					"bytes":	6291456,
					"bits_per_second":	50328378.212435037,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	58.001084,
				"end":	59.001149,
				"seconds":	1.0000649690628052,
				"bytes":	6291456,
				"bits_per_second":	50328378.212435037,
				"omitted":	false,
				"sender":	false
			}
		}, {
			"streams":	[{
					"socket":	17,
					"start":	59.001149,
					"end":	60.000502,
					"seconds":	0.999352991580963,
					"bytes":	6160384,
					"bits_per_second":	49314979.206731386,
					"omitted":	false,
					"sender":	false
				}],
			"sum":	{
				"start":	59.001149,
				"end":	60.000502,
				"seconds":	0.999352991580963,
				"bytes":	6160384,
				"bits_per_second":	49314979.206731386,
				"omitted":	false,
				"sender":	false
			}
		}],
	"end":	{
		"streams":	[{
				"sender":	{
					"socket":	17,
					"start":	0,
					"end":	60.000502,
					"seconds":	60.000502,
					"bytes":	0,
					"bits_per_second":	0,
					"sender":	false
				},
				"receiver":	{
					"socket":	17,
					"start":	0,
					"end":	60.000502,
					"seconds":	60.000502,
					"bytes":	374996992,
					"bits_per_second":	49999180.6068556,
					"sender":	false
				}
			}],
		"sum_sent":	{
			"start":	0,
			"end":	60.000502,
			"seconds":	60.000502,
			"bytes":	0,
			"bits_per_second":	0,
			"sender":	false
		},
		"sum_received":	{
			"start":	0,
			"end":	60.000502,
			"seconds":	60.000502,
			"bytes":	374996992,
			"bits_per_second":	49999180.6068556,
			"sender":	false
		},
		"cpu_utilization_percent":	{
			"host_total":	1.6326132044509722,
			"host_user":	0.25108933990545024,
			"host_system":	1.3815321978016337,
			"remote_total":	0,
			"remote_user":	0,
			"remote_system":	0
		},
		"receiver_tcp_congestion":	"cubic"
	}
}

2025-02-09 23:26:52 [DEBUG] 结果属性: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', 'blksize', 'duration', 'error', 'json', 'local_cpu_system', 'local_cpu_total', 'local_cpu_user', 'local_host', 'local_port', 'num_streams', 'omit', 'protocol', 'received_MB_s', 'received_Mbps', 'received_bps', 'received_bytes', 'received_kB_s', 'received_kbps', 'remote_cpu_system', 'remote_cpu_total', 'remote_cpu_user', 'remote_host', 'remote_port', 'retransmits', 'reverse', 'sent_MB_s', 'sent_Mbps', 'sent_bps', 'sent_bytes', 'sent_kB_s', 'sent_kbps', 'system_info', 'tcp_mss_default', 'text', 'time', 'timesecs', 'type', 'version']
2025-02-09 23:26:52 [INFO] 服务端 JSON 结果: {'start': {'connected': [{'socket': 17, 'local_host': 'fd15:4ba5:5a2b:1008::104', 'local_port': 39999, 'remote_host': 'fd15:4ba5:5a2b:1008::105', 'remote_port': 59355}], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64', 'target_bitrate': 50000000, 'sock_bufsize': 0, 'sndbuf_actual': 16384, 'rcvbuf_actual': 131072, 'timestamp': {'time': 'Sun, 09 Feb 2025 15:25:52 GMT', 'timesecs': 1739114752}, 'accepted_connection': {'host': 'fd15:4ba5:5a2b:1008::105', 'port': 36785}, 'cookie': 'kqne2mbzo6x7ihpdljp245ijqr6qllnv3gvm', 'tcp_mss_default': 0, 'fq_rate': 0, 'test_start': {'protocol': 'TCP', 'num_streams': 1, 'blksize': 131072, 'omit': 0, 'duration': 60, 'bytes': 0, 'blocks': 0, 'reverse': 0, 'tos': 0, 'target_bitrate': 50000000, 'bidir': 0, 'fqrate': 0}}, 'intervals': [{'streams': [{'socket': 17, 'start': 0, 'end': 1.000466, 'seconds': 1.000465989112854, 'bytes': 6291456, 'bits_per_second': 50308204.92421809, 'omitted': False, 'sender': False}], 'sum': {'start': 0, 'end': 1.000466, 'seconds': 1.000465989112854, 'bytes': 6291456, 'bits_per_second': 50308204.92421809, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 1.000466, 'end': 2.001137, 'seconds': 1.0006710290908813, 'bytes': 6291456, 'bits_per_second': 50297896.648138955, 'omitted': False, 'sender': False}], 'sum': {'start': 1.000466, 'end': 2.001137, 'seconds': 1.0006710290908813, 'bytes': 6291456, 'bits_per_second': 50297896.648138955, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 2.001137, 'end': 3.001158, 'seconds': 1.000020980834961, 'bytes': 6291456, 'bits_per_second': 50330592.0221553, 'omitted': False, 'sender': False}], 'sum': {'start': 2.001137, 'end': 3.001158, 'seconds': 1.000020980834961, 'bytes': 6291456, 'bits_per_second': 50330592.0221553, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 3.001158, 'end': 4.001061, 'seconds': 0.9999030232429504, 'bytes': 6160384, 'bits_per_second': 49287851.77602718, 'omitted': False, 'sender': False}], 'sum': {'start': 3.001158, 'end': 4.001061, 'seconds': 0.9999030232429504, 'bytes': 6160384, 'bits_per_second': 49287851.77602718, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 4.001061, 'end': 5.00106, 'seconds': 0.9999989867210388, 'bytes': 6291456, 'bits_per_second': 50331699.00005168, 'omitted': False, 'sender': False}], 'sum': {'start': 4.001061, 'end': 5.00106, 'seconds': 0.9999989867210388, 'bytes': 6291456, 'bits_per_second': 50331699.00005168, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 5.00106, 'end': 6.00123, 'seconds': 1.0001699924468994, 'bytes': 6291456, 'bits_per_second': 50323093.45420817, 'omitted': False, 'sender': False}], 'sum': {'start': 5.00106, 'end': 6.00123, 'seconds': 1.0001699924468994, 'bytes': 6291456, 'bits_per_second': 50323093.45420817, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 6.00123, 'end': 7.001326, 'seconds': 1.0000959634780884, 'bytes': 6160384, 'bits_per_second': 49278343.078803726, 'omitted': False, 'sender': False}], 'sum': {'start': 6.00123, 'end': 7.001326, 'seconds': 1.0000959634780884, 'bytes': 6160384, 'bits_per_second': 49278343.078803726, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 7.001326, 'end': 8.001085, 'seconds': 0.999759018421173, 'bytes': 6291456, 'bits_per_second': 50343779.9235701, 'omitted': False, 'sender': False}], 'sum': {'start': 7.001326, 'end': 8.001085, 'seconds': 0.999759018421173, 'bytes': 6291456, 'bits_per_second': 50343779.9235701, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 8.001085, 'end': 9.000482, 'seconds': 0.9993969798088074, 'bytes': 6291456, 'bits_per_second': 50362017.31330912, 'omitted': False, 'sender': False}], 'sum': {'start': 8.001085, 'end': 9.000482, 'seconds': 0.9993969798088074, 'bytes': 6291456, 'bits_per_second': 50362017.31330912, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 9.000482, 'end': 10.0012, 'seconds': 1.0007179975509644, 'bytes': 6160384, 'bits_per_second': 49247712.263204426, 'omitted': False, 'sender': False}], 'sum': {'start': 9.000482, 'end': 10.0012, 'seconds': 1.0007179975509644, 'bytes': 6160384, 'bits_per_second': 49247712.263204426, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 10.0012, 'end': 11.001309, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}], 'sum': {'start': 10.0012, 'end': 11.001309, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 11.001309, 'end': 12.001232, 'seconds': 0.9999229907989502, 'bytes': 6291456, 'bits_per_second': 50335524.29851065, 'omitted': False, 'sender': False}], 'sum': {'start': 11.001309, 'end': 12.001232, 'seconds': 0.9999229907989502, 'bytes': 6291456, 'bits_per_second': 50335524.29851065, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 12.001232, 'end': 13.001188, 'seconds': 0.9999560117721558, 'bytes': 6160384, 'bits_per_second': 49285239.970365174, 'omitted': False, 'sender': False}], 'sum': {'start': 12.001232, 'end': 13.001188, 'seconds': 0.9999560117721558, 'bytes': 6160384, 'bits_per_second': 49285239.970365174, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 13.001188, 'end': 14.001112, 'seconds': 0.9999240040779114, 'bytes': 6291456, 'bits_per_second': 50335473.2907065, 'omitted': False, 'sender': False}], 'sum': {'start': 13.001188, 'end': 14.001112, 'seconds': 0.9999240040779114, 'bytes': 6291456, 'bits_per_second': 50335473.2907065, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 14.001112, 'end': 15.001293, 'seconds': 1.000180959701538, 'bytes': 6291456, 'bits_per_second': 50322541.64788276, 'omitted': False, 'sender': False}], 'sum': {'start': 14.001112, 'end': 15.001293, 'seconds': 1.000180959701538, 'bytes': 6291456, 'bits_per_second': 50322541.64788276, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 15.001293, 'end': 16.001202, 'seconds': 0.999908983707428, 'bytes': 6160384, 'bits_per_second': 49287557.97079643, 'omitted': False, 'sender': False}], 'sum': {'start': 15.001293, 'end': 16.001202, 'seconds': 0.999908983707428, 'bytes': 6160384, 'bits_per_second': 49287557.97079643, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 16.001202, 'end': 17.001094, 'seconds': 0.999891996383667, 'bytes': 6291456, 'bits_per_second': 50337084.58717108, 'omitted': False, 'sender': False}], 'sum': {'start': 16.001202, 'end': 17.001094, 'seconds': 0.999891996383667, 'bytes': 6291456, 'bits_per_second': 50337084.58717108, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 17.001094, 'end': 18.001255, 'seconds': 1.000161051750183, 'bytes': 6291456, 'bits_per_second': 50323543.30527527, 'omitted': False, 'sender': False}], 'sum': {'start': 17.001094, 'end': 18.001255, 'seconds': 1.000161051750183, 'bytes': 6291456, 'bits_per_second': 50323543.30527527, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 18.001255, 'end': 19.001112, 'seconds': 0.999857008457184, 'bytes': 6160384, 'bits_per_second': 49290120.070314445, 'omitted': False, 'sender': False}], 'sum': {'start': 18.001255, 'end': 19.001112, 'seconds': 0.999857008457184, 'bytes': 6160384, 'bits_per_second': 49290120.070314445, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 19.001112, 'end': 20.00114, 'seconds': 1.0000280141830444, 'bytes': 6291456, 'bits_per_second': 50330238.03949889, 'omitted': False, 'sender': False}], 'sum': {'start': 19.001112, 'end': 20.00114, 'seconds': 1.0000280141830444, 'bytes': 6291456, 'bits_per_second': 50330238.03949889, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 20.00114, 'end': 21.001213, 'seconds': 1.000072956085205, 'bytes': 6291456, 'bits_per_second': 50327976.2678752, 'omitted': False, 'sender': False}], 'sum': {'start': 20.00114, 'end': 21.001213, 'seconds': 1.000072956085205, 'bytes': 6291456, 'bits_per_second': 50327976.2678752, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 21.001213, 'end': 22.001192, 'seconds': 0.999979019165039, 'bytes': 6291456, 'bits_per_second': 50332704.02215622, 'omitted': False, 'sender': False}], 'sum': {'start': 21.001213, 'end': 22.001192, 'seconds': 0.999979019165039, 'bytes': 6291456, 'bits_per_second': 50332704.02215622, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 22.001192, 'end': 23.001314, 'seconds': 1.0001219511032104, 'bytes': 6160384, 'bits_per_second': 49277062.607852, 'omitted': False, 'sender': False}], 'sum': {'start': 22.001192, 'end': 23.001314, 'seconds': 1.0001219511032104, 'bytes': 6160384, 'bits_per_second': 49277062.607852, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 23.001314, 'end': 24.00118, 'seconds': 0.999866008758545, 'bytes': 6291456, 'bits_per_second': 50338392.90375803, 'omitted': False, 'sender': False}], 'sum': {'start': 23.001314, 'end': 24.00118, 'seconds': 0.999866008758545, 'bytes': 6291456, 'bits_per_second': 50338392.90375803, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 24.00118, 'end': 25.001062, 'seconds': 0.9998819828033447, 'bytes': 6291456, 'bits_per_second': 50337588.7011049, 'omitted': False, 'sender': False}], 'sum': {'start': 24.00118, 'end': 25.001062, 'seconds': 0.9998819828033447, 'bytes': 6291456, 'bits_per_second': 50337588.7011049, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 25.001062, 'end': 26.001085, 'seconds': 1.0000230073928833, 'bytes': 6160384, 'bits_per_second': 49281938.1510869, 'omitted': False, 'sender': False}], 'sum': {'start': 25.001062, 'end': 26.001085, 'seconds': 1.0000230073928833, 'bytes': 6160384, 'bits_per_second': 49281938.1510869, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 26.001085, 'end': 27.00112, 'seconds': 1.000035047531128, 'bytes': 6291456, 'bits_per_second': 50329884.06182168, 'omitted': False, 'sender': False}], 'sum': {'start': 26.001085, 'end': 27.00112, 'seconds': 1.000035047531128, 'bytes': 6291456, 'bits_per_second': 50329884.06182168, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 27.00112, 'end': 28.001187, 'seconds': 1.0000669956207275, 'bytes': 6291456, 'bits_per_second': 50328276.2258941, 'omitted': False, 'sender': False}], 'sum': {'start': 27.00112, 'end': 28.001187, 'seconds': 1.0000669956207275, 'bytes': 6291456, 'bits_per_second': 50328276.2258941, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 28.001187, 'end': 29.00113, 'seconds': 0.9999430179595947, 'bytes': 6160384, 'bits_per_second': 49285880.410028934, 'omitted': False, 'sender': False}], 'sum': {'start': 28.001187, 'end': 29.00113, 'seconds': 0.9999430179595947, 'bytes': 6160384, 'bits_per_second': 49285880.410028934, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 29.00113, 'end': 30.001122, 'seconds': 0.9999920129776, 'bytes': 6291456, 'bits_per_second': 50332050.0032108, 'omitted': False, 'sender': False}], 'sum': {'start': 29.00113, 'end': 30.001122, 'seconds': 0.9999920129776, 'bytes': 6291456, 'bits_per_second': 50332050.0032108, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 30.001122, 'end': 31.001086, 'seconds': 0.9999639987945557, 'bytes': 6291456, 'bits_per_second': 50333460.06523653, 'omitted': False, 'sender': False}], 'sum': {'start': 30.001122, 'end': 31.001086, 'seconds': 0.9999639987945557, 'bytes': 6291456, 'bits_per_second': 50333460.06523653, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 31.001086, 'end': 32.001082, 'seconds': 0.9999960064888, 'bytes': 6160384, 'bits_per_second': 49283268.81328598, 'omitted': False, 'sender': False}], 'sum': {'start': 31.001086, 'end': 32.001082, 'seconds': 0.9999960064888, 'bytes': 6160384, 'bits_per_second': 49283268.81328598, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 32.001082, 'end': 33.000413, 'seconds': 0.999330997467041, 'bytes': 6291456, 'bits_per_second': 50365342.541733764, 'omitted': False, 'sender': False}], 'sum': {'start': 32.001082, 'end': 33.000413, 'seconds': 0.999330997467041, 'bytes': 6291456, 'bits_per_second': 50365342.541733764, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 33.000413, 'end': 34.00105, 'seconds': 1.0006370544433594, 'bytes': 6291456, 'bits_per_second': 50299604.41350915, 'omitted': False, 'sender': False}], 'sum': {'start': 33.000413, 'end': 34.00105, 'seconds': 1.0006370544433594, 'bytes': 6291456, 'bits_per_second': 50299604.41350915, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 34.00105, 'end': 35.00122, 'seconds': 1.0001699924468994, 'bytes': 6160384, 'bits_per_second': 49274695.67391217, 'omitted': False, 'sender': False}], 'sum': {'start': 34.00105, 'end': 35.00122, 'seconds': 1.0001699924468994, 'bytes': 6160384, 'bits_per_second': 49274695.67391217, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 35.00122, 'end': 36.001167, 'seconds': 0.9999470114707947, 'bytes': 6291456, 'bits_per_second': 50334315.1413279, 'omitted': False, 'sender': False}], 'sum': {'start': 35.00122, 'end': 36.001167, 'seconds': 0.9999470114707947, 'bytes': 6291456, 'bits_per_second': 50334315.1413279, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 36.001167, 'end': 37.001036, 'seconds': 0.9998689889907837, 'bytes': 6291456, 'bits_per_second': 50338242.8639998, 'omitted': False, 'sender': False}], 'sum': {'start': 36.001167, 'end': 37.001036, 'seconds': 0.9998689889907837, 'bytes': 6291456, 'bits_per_second': 50338242.8639998, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 37.001036, 'end': 38.001242, 'seconds': 1.0002059936523438, 'bytes': 6160384, 'bits_per_second': 49272922.09081686, 'omitted': False, 'sender': False}], 'sum': {'start': 37.001036, 'end': 38.001242, 'seconds': 1.0002059936523438, 'bytes': 6160384, 'bits_per_second': 49272922.09081686, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 38.001242, 'end': 39.001106, 'seconds': 0.9998639822006226, 'bytes': 6291456, 'bits_per_second': 50338494.93130453, 'omitted': False, 'sender': False}], 'sum': {'start': 38.001242, 'end': 39.001106, 'seconds': 0.9998639822006226, 'bytes': 6291456, 'bits_per_second': 50338494.93130453, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 39.001106, 'end': 40.000483, 'seconds': 0.9993770122528076, 'bytes': 6291456, 'bits_per_second': 50363023.54658108, 'omitted': False, 'sender': False}], 'sum': {'start': 39.001106, 'end': 40.000483, 'seconds': 0.9993770122528076, 'bytes': 6291456, 'bits_per_second': 50363023.54658108, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 40.000483, 'end': 41.001149, 'seconds': 1.00066602230072, 'bytes': 6160384, 'bits_per_second': 49250270.22171584, 'omitted': False, 'sender': False}], 'sum': {'start': 40.000483, 'end': 41.001149, 'seconds': 1.00066602230072, 'bytes': 6160384, 'bits_per_second': 49250270.22171584, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 41.001149, 'end': 42.001047, 'seconds': 0.9998980164527893, 'bytes': 6291456, 'bits_per_second': 50336781.52353494, 'omitted': False, 'sender': False}], 'sum': {'start': 41.001149, 'end': 42.001047, 'seconds': 0.9998980164527893, 'bytes': 6291456, 'bits_per_second': 50336781.52353494, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 42.001047, 'end': 43.001156, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}], 'sum': {'start': 42.001047, 'end': 43.001156, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 43.001156, 'end': 44.001059, 'seconds': 0.9999030232429504, 'bytes': 6291456, 'bits_per_second': 50336529.47338946, 'omitted': False, 'sender': False}], 'sum': {'start': 43.001156, 'end': 44.001059, 'seconds': 0.9999030232429504, 'bytes': 6291456, 'bits_per_second': 50336529.47338946, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 44.001059, 'end': 45.001086, 'seconds': 1.0000269412994385, 'bytes': 6160384, 'bits_per_second': 49281744.28577035, 'omitted': False, 'sender': False}], 'sum': {'start': 44.001059, 'end': 45.001086, 'seconds': 1.0000269412994385, 'bytes': 6160384, 'bits_per_second': 49281744.28577035, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 45.001086, 'end': 46.001089, 'seconds': 1.0000029802322388, 'bytes': 6291456, 'bits_per_second': 50331498.000447035, 'omitted': False, 'sender': False}], 'sum': {'start': 45.001086, 'end': 46.001089, 'seconds': 1.0000029802322388, 'bytes': 6291456, 'bits_per_second': 50331498.000447035, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 46.001089, 'end': 47.00109, 'seconds': 1.0000009536743164, 'bytes': 6291456, 'bits_per_second': 50331600.00004578, 'omitted': False, 'sender': False}], 'sum': {'start': 46.001089, 'end': 47.00109, 'seconds': 1.0000009536743164, 'bytes': 6291456, 'bits_per_second': 50331600.00004578, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 47.00109, 'end': 48.001215, 'seconds': 1.0001250505447388, 'bytes': 6160384, 'bits_per_second': 49276909.89557452, 'omitted': False, 'sender': False}], 'sum': {'start': 47.00109, 'end': 48.001215, 'seconds': 1.0001250505447388, 'bytes': 6160384, 'bits_per_second': 49276909.89557452, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 48.001215, 'end': 49.001047, 'seconds': 0.9998319745063782, 'bytes': 6291456, 'bits_per_second': 50340106.4212304, 'omitted': False, 'sender': False}], 'sum': {'start': 48.001215, 'end': 49.001047, 'seconds': 0.9998319745063782, 'bytes': 6291456, 'bits_per_second': 50340106.4212304, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 49.001047, 'end': 50.001103, 'seconds': 1.0000560283660889, 'bytes': 6291456, 'bits_per_second': 50328828.15799114, 'omitted': False, 'sender': False}], 'sum': {'start': 49.001047, 'end': 50.001103, 'seconds': 1.0000560283660889, 'bytes': 6291456, 'bits_per_second': 50328828.15799114, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 50.001103, 'end': 51.001076, 'seconds': 0.9999729990959167, 'bytes': 6160384, 'bits_per_second': 49284402.72343074, 'omitted': False, 'sender': False}], 'sum': {'start': 50.001103, 'end': 51.001076, 'seconds': 0.9999729990959167, 'bytes': 6160384, 'bits_per_second': 49284402.72343074, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 51.001076, 'end': 52.001185, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}], 'sum': {'start': 51.001076, 'end': 52.001185, 'seconds': 1.0001089572906494, 'bytes': 6291456, 'bits_per_second': 50326164.597456686, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 52.001185, 'end': 53.001042, 'seconds': 0.999857008457184, 'bytes': 6291456, 'bits_per_second': 50338846.02925731, 'omitted': False, 'sender': False}], 'sum': {'start': 52.001185, 'end': 53.001042, 'seconds': 0.999857008457184, 'bytes': 6291456, 'bits_per_second': 50338846.02925731, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 53.001042, 'end': 54.001176, 'seconds': 1.000133991241455, 'bytes': 6160384, 'bits_per_second': 49276469.384692624, 'omitted': False, 'sender': False}], 'sum': {'start': 53.001042, 'end': 54.001176, 'seconds': 1.000133991241455, 'bytes': 6160384, 'bits_per_second': 49276469.384692624, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 54.001176, 'end': 55.001206, 'seconds': 1.0000300407409668, 'bytes': 6291456, 'bits_per_second': 50330136.04542024, 'omitted': False, 'sender': False}], 'sum': {'start': 54.001176, 'end': 55.001206, 'seconds': 1.0000300407409668, 'bytes': 6291456, 'bits_per_second': 50330136.04542024, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 55.001206, 'end': 56.001073, 'seconds': 0.999867022037506, 'bytes': 6291456, 'bits_per_second': 50338341.89013987, 'omitted': False, 'sender': False}], 'sum': {'start': 55.001206, 'end': 56.001073, 'seconds': 0.999867022037506, 'bytes': 6291456, 'bits_per_second': 50338341.89013987, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 56.001073, 'end': 57.00053, 'seconds': 0.9994570016860962, 'bytes': 6160384, 'bits_per_second': 49309847.16386884, 'omitted': False, 'sender': False}], 'sum': {'start': 56.001073, 'end': 57.00053, 'seconds': 0.9994570016860962, 'bytes': 6160384, 'bits_per_second': 49309847.16386884, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 57.00053, 'end': 58.001084, 'seconds': 1.0005539655685425, 'bytes': 6291456, 'bits_per_second': 50303781.437116355, 'omitted': False, 'sender': False}], 'sum': {'start': 57.00053, 'end': 58.001084, 'seconds': 1.0005539655685425, 'bytes': 6291456, 'bits_per_second': 50303781.437116355, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 58.001084, 'end': 59.001149, 'seconds': 1.0000649690628052, 'bytes': 6291456, 'bits_per_second': 50328378.21243504, 'omitted': False, 'sender': False}], 'sum': {'start': 58.001084, 'end': 59.001149, 'seconds': 1.0000649690628052, 'bytes': 6291456, 'bits_per_second': 50328378.21243504, 'omitted': False, 'sender': False}}, {'streams': [{'socket': 17, 'start': 59.001149, 'end': 60.000502, 'seconds': 0.999352991580963, 'bytes': 6160384, 'bits_per_second': 49314979.20673139, 'omitted': False, 'sender': False}], 'sum': {'start': 59.001149, 'end': 60.000502, 'seconds': 0.999352991580963, 'bytes': 6160384, 'bits_per_second': 49314979.20673139, 'omitted': False, 'sender': False}}], 'end': {'streams': [{'sender': {'socket': 17, 'start': 0, 'end': 60.000502, 'seconds': 60.000502, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'receiver': {'socket': 17, 'start': 0, 'end': 60.000502, 'seconds': 60.000502, 'bytes': 374996992, 'bits_per_second': 49999180.6068556, 'sender': False}}], 'sum_sent': {'start': 0, 'end': 60.000502, 'seconds': 60.000502, 'bytes': 0, 'bits_per_second': 0, 'sender': False}, 'sum_received': {'start': 0, 'end': 60.000502, 'seconds': 60.000502, 'bytes': 374996992, 'bits_per_second': 49999180.6068556, 'sender': False}, 'cpu_utilization_percent': {'host_total': 1.6326132044509722, 'host_user': 0.25108933990545024, 'host_system': 1.3815321978016337, 'remote_total': 0, 'remote_user': 0, 'remote_system': 0}, 'receiver_tcp_congestion': 'cubic'}}
2025-02-09 23:27:09 [DEBUG] Heartbeat sent successfully
2025-02-09 23:27:48 [ERROR] WebSocket连接断开，准备重连
2025-02-09 23:27:48 [INFO] WebSocket连接已安全关闭
2025-02-09 23:27:48 [INFO] 正在连接到WebSocket服务器: ws://**************:8765
2025-02-09 23:27:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('**************', 8765)
2025-02-09 23:27:50 [INFO] Program interrupted by user
