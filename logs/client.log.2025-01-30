2025-01-30 21:20:04 [INFO] Client starting...
2025-01-30 21:20:05 [INFO] Login successful
2025-01-30 21:20:05 [INFO] Checking registration status...
2025-01-30 21:20:05 [INFO] Client already registered
2025-01-30 21:20:05 [INFO] Starting SSH tunnel to **************:40000
2025-01-30 21:20:05 [INFO] Starting heartbeat...
2025-01-30 21:20:06 [DEBUG] System metrics: {'cpu_usage': 7.2, 'memory_usage': 52.1, 'network_usage': {'sent_rate': 3470.72, 'recv_rate': 778.13, 'total_rate': 4248.85}, 'disk_io': {'util_percent': [0.1]}, 'disk_usage': 26.1}
2025-01-30 21:20:06 [DEBUG] Heartbeat data: {'serial_number': 'VMware-56 4d 8c d9 3e c2 71 2f-3e 85 7a cf 71 8b 47 df', 'ipv4_private': '**************', 'ipv6_address': 'fd15:4ba5:5a2b:1008:20c:29ff:fe8b:47df', 'cpu_usage': 7.2, 'memory_usage': 52.1, 'sent_rate': 3470.72, 'recv_rate': 778.13, 'total_rate': 4248.85, 'disk_io': 0.1, 'disk_usage': 26.1}
2025-01-30 21:20:06 [DEBUG] Heartbeat sent successfully
2025-01-30 21:20:06 [INFO] SSH tunnel established
2025-01-30 21:20:07 [INFO] Program interrupted by user
2025-01-30 21:33:21 [INFO] Client starting...
2025-01-30 21:33:21 [INFO] Login successful
2025-01-30 21:33:21 [INFO] Checking registration status...
2025-01-30 21:33:21 [INFO] Client already registered
2025-01-30 21:33:21 [INFO] Starting SSH tunnel to **************:40000
2025-01-30 21:33:21 [INFO] Starting heartbeat...
2025-01-30 21:33:22 [DEBUG] System metrics: {'cpu_usage': 8.0, 'memory_usage': 52.2, 'network_usage': {'sent_rate': 2148.72, 'recv_rate': 657.13, 'total_rate': 2805.85}, 'disk_io': {'util_percent': [0.1]}, 'disk_usage': 26.1}
2025-01-30 21:33:22 [DEBUG] Heartbeat data: {'serial_number': 'VMware-56 4d 8c d9 3e c2 71 2f-3e 85 7a cf 71 8b 47 df', 'ipv4_private': '**************', 'ipv6_address': 'fd15:4ba5:5a2b:1008:20c:29ff:fe8b:47df', 'cpu_usage': 8.0, 'memory_usage': 52.2, 'sent_rate': 2148.72, 'recv_rate': 657.13, 'total_rate': 2805.85, 'disk_io': 0.1, 'disk_usage': 26.1}
2025-01-30 21:33:23 [DEBUG] Heartbeat sent successfully
2025-01-30 21:33:23 [INFO] Connecting to WebSocket URL: ws://**************:8000/ws/client/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MzMwNDAxLCJpYXQiOjE3MzgyNDQwMDEsImp0aSI6IjRiNTc3MzM5NDIzMDRkY2U4MGI5ZWIxNThkZjcwMmE1IiwidXNlcl9pZCI6NH0.LdjqJCV4irztTUJd0om6XyPINpvPx4xqAKnPSRoUnuU/VMware-56%204d%208c%20d9%203e%20c2%2071%202f-3e%2085%207a%20cf%2071%208b%2047%20df/
2025-01-30 21:33:23 [INFO] SSH tunnel established
2025-01-30 21:33:23 [ERROR] Failed to establish WebSocket connection: server rejected WebSocket connection: HTTP 500
2025-01-30 21:33:23 [ERROR] WebSocket connection error: server rejected WebSocket connection: HTTP 500
2025-01-30 21:33:23 [INFO] SSH tunnel stopped
2025-01-30 21:33:23 [ERROR] Unexpected error occurred: server rejected WebSocket connection: HTTP 500
2025-01-30 21:33:23 [ERROR] Traceback:
Traceback (most recent call last):
  File "/home/<USER>/tss/client/main.py", line 72, in async_main
    await asyncio.gather(ssh_tunnel_task, heartbeat_task, executor_task)
  File "/home/<USER>/tss/client/core/task_executor.py", line 28, in connect_websocket
    await self.connection.connect()
  File "/home/<USER>/venv_tss/lib/python3.12/site-packages/backoff/_async.py", line 151, in retry
    ret = await target(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/tss/client/core/connection.py", line 54, in connect
    self.websocket = await websockets.connect(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/venv_tss/lib/python3.12/site-packages/websockets/legacy/client.py", line 655, in __await_impl_timeout__
    return await self.__await_impl__()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/venv_tss/lib/python3.12/site-packages/websockets/legacy/client.py", line 662, in __await_impl__
    await protocol.handshake(
  File "/home/<USER>/venv_tss/lib/python3.12/site-packages/websockets/legacy/client.py", line 329, in handshake
    raise InvalidStatusCode(status_code, response_headers)
websockets.exceptions.InvalidStatusCode: server rejected WebSocket connection: HTTP 500

2025-01-30 21:33:23 [INFO] Client stopped
2025-01-30 21:44:59 [INFO] Client starting...
2025-01-30 21:45:00 [INFO] Login successful
2025-01-30 21:45:00 [INFO] Checking registration status...
2025-01-30 21:45:00 [INFO] Client already registered
2025-01-30 21:45:00 [INFO] Starting SSH tunnel to **************:40000
2025-01-30 21:45:00 [INFO] Starting heartbeat...
2025-01-30 21:45:01 [DEBUG] System metrics: {'cpu_usage': 6.5, 'memory_usage': 50.5, 'network_usage': {'sent_rate': 1085.84, 'recv_rate': 639.33, 'total_rate': 1725.17}, 'disk_io': {'util_percent': [0.1]}, 'disk_usage': 26.1}
2025-01-30 21:45:01 [DEBUG] Heartbeat data: {'serial_number': 'VMware-56 4d 8c d9 3e c2 71 2f-3e 85 7a cf 71 8b 47 df', 'ipv4_private': '**************', 'ipv6_address': 'fd15:4ba5:5a2b:1008:20c:29ff:fe8b:47df', 'cpu_usage': 6.5, 'memory_usage': 50.5, 'sent_rate': 1085.84, 'recv_rate': 639.33, 'total_rate': 1725.17, 'disk_io': 0.1, 'disk_usage': 26.1}
2025-01-30 21:45:01 [DEBUG] Heartbeat sent successfully
2025-01-30 21:45:01 [INFO] Connecting to WebSocket URL: ws://**************:8000/ws/client/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzM4MzMxMTAwLCJpYXQiOjE3MzgyNDQ3MDAsImp0aSI6IjIzYmYxNjhhMGViMzRhMmRiNGM5YjY2YmZiZjA2MzJmIiwidXNlcl9pZCI6NH0.NWORCi-OtQVoI_su7tnDk49qUALhdCrXWdxPaBUU5wQ/VMware-56%204d%208c%20d9%203e%20c2%2071%202f-3e%2085%207a%20cf%2071%208b%2047%20df/
2025-01-30 21:45:01 [INFO] SSH tunnel established
2025-01-30 21:45:01 [INFO] WebSocket connection established and identity sent
2025-01-30 21:45:01 [ERROR] WebSocket connection closed
2025-01-30 21:45:06 [ERROR] WebSocket connection closed
2025-01-30 21:45:11 [ERROR] WebSocket connection closed
2025-01-30 21:45:16 [ERROR] WebSocket connection closed
2025-01-30 21:45:21 [ERROR] WebSocket connection closed
2025-01-30 21:45:26 [ERROR] WebSocket connection closed
2025-01-30 21:45:27 [INFO] Program interrupted by user
