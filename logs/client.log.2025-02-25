2025-02-25 11:41:57 [INFO] WebSocket连接已安全关闭
2025-02-25 11:41:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:41:57 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 11:42:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:02 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 11:42:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 11:42:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 11:42:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 11:42:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:22 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 11:42:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:42:27 [INFO] 注册成功
2025-02-25 11:42:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:43:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:44:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:45:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:46:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:47:53 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:48:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:49:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:50:56 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 11:50:56 [ERROR] WebSocket连接断开，准备重连
2025-02-25 11:50:56 [INFO] WebSocket连接已安全关闭
2025-02-25 11:50:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 11:50:57 [INFO] 注册成功
2025-02-25 12:04:16 [ERROR] WebSocket连接断开，准备重连
2025-02-25 12:04:16 [INFO] WebSocket连接已安全关闭
2025-02-25 12:04:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:16 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 12:04:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:21 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 12:04:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:26 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 12:04:31 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 12:04:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 12:04:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 12:04:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:46 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 12:04:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 12:04:52 [INFO] 注册成功
2025-02-25 12:05:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:06:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:07:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:08:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:09:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:10:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:11:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:12:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:13:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:14:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:15:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:16:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 12:17:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:14:59 [ERROR] WebSocket连接断开，准备重连
2025-02-25 13:14:59 [INFO] WebSocket连接已安全关闭
2025-02-25 13:14:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:14:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:04 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 13:15:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:09 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:14 [ERROR] Heartbeat failed with status code: 503
2025-02-25 13:15:14 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:15:39 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:15:44 [INFO] 注册成功
2025-02-25 13:16:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:17:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:18:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:19:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:20:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:21:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:22:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:23:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:24:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:25:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:26:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:27:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:28:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:29:30 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:30:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:31:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:32:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:33:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:34:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:35:37 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:36:38 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:37:40 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:38:41 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:39:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:40:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:41:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:42:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:43:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:44:48 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:45:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:46:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:47:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:56:24 [ERROR] WebSocket连接断开，准备重连
2025-02-25 13:56:24 [INFO] WebSocket连接已安全关闭
2025-02-25 13:56:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:56:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:56:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:56:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 13:56:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:51 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 13:56:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 13:56:56 [INFO] 注册成功
2025-02-25 13:57:01 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:58:02 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 13:59:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:00:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:00:52 [INFO] 收到消息: start_task
2025-02-25 14:00:52 [INFO] 收到任务启动请求: task_id=87_client, config={'type': 'iperf3_client', 'server_ip': '240e:34c:71:59e0:20c:29ff:fe54:d98f', 'port': 39999, 'result_id': 137, 'interface': '240e:34c:71:59e0:20c:29ff:fe8b:47e9', 'bandwidth': None, 'network_interface': 'ens37', 'direction': 'download', 'interval': 5, 'mode': 'traffic', 'traffic': 500}
2025-02-25 14:00:52 [INFO] 启动 iperf3 客户端: iperf3 -c 240e:34c:71:59e0:20c:29ff:fe54:d98f -p 39999 --bind-dev ens37 --json -i 5 -6 --connect-timeout 20 -n 500M
2025-02-25 14:00:52 [INFO] iperf3 client result: {'start': {'connected': [], 'version': 'iperf 3.16', 'system_info': 'Linux dev 6.8.0-52-generic #53-Ubuntu SMP PREEMPT_DYNAMIC Sat Jan 11 00:06:25 UTC 2025 x86_64'}, 'intervals': [], 'end': {}, 'error': 'unable to connect to server - server may have stopped running or use a different port, firewall issue, etc.: Connection timed out'}
2025-02-25 14:01:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:02:07 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:03:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:04:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:05:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:06:12 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:07:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:08:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:09:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:10:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:11:18 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:12:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:13:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:14:04 [ERROR] WebSocket连接断开，准备重连
2025-02-25 14:14:04 [INFO] WebSocket连接已安全关闭
2025-02-25 14:14:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:14:07 [INFO] 注册成功
2025-02-25 14:14:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:16:04 [ERROR] WebSocket连接断开，准备重连
2025-02-25 14:16:04 [INFO] WebSocket连接已安全关闭
2025-02-25 14:16:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:16:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 14:16:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:16:09 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 14:16:14 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:16:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 14:16:19 [ERROR] 获取私网IPv4地址失败: 2
2025-02-25 14:16:19 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 14:16:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:16:19 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 14:16:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 14:16:25 [INFO] 注册成功
2025-02-25 14:17:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:18:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:19:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:20:23 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:21:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:22:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:23:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 14:24:28 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:17:48 [ERROR] WebSocket连接断开，准备重连
2025-02-25 16:17:48 [INFO] WebSocket连接已安全关闭
2025-02-25 16:17:48 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:17:48 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:17:53 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:17:53 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:17:55 [ERROR] Heartbeat failed with status code: 503
2025-02-25 16:17:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:17:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:18:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:18:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:18:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:18:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:18:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:18:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:18:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:18:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:25:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:03 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 16:25:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:08 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 16:25:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:25:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 16:25:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:23 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 16:25:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:25:27 [INFO] 注册成功
2025-02-25 16:25:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:26:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:27:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:28:35 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:29:36 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:30:44 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:31:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:33:42 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:33:42 [ERROR] WebSocket连接断开，准备重连
2025-02-25 16:33:42 [INFO] WebSocket连接已安全关闭
2025-02-25 16:33:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 16:33:43 [INFO] 注册成功
2025-02-25 16:34:43 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:35:45 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:36:46 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:37:47 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:38:49 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:39:50 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:40:51 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:41:52 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:42:54 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 16:43:55 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:09:18 [ERROR] WebSocket连接断开，准备重连
2025-02-25 17:09:18 [INFO] WebSocket连接已安全关闭
2025-02-25 17:09:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:09:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:09:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:09:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:09:34 [ERROR] Heartbeat failed with status code: 503
2025-02-25 17:09:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:09:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:09:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:09:40 [ERROR] 连接失败: [Errno 101] Network is unreachable
2025-02-25 17:09:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:09:45 [INFO] 注册成功
2025-02-25 17:10:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:12:55 [ERROR] Heartbeat failed with status code: 503
2025-02-25 17:12:55 [ERROR] WebSocket连接断开，准备重连
2025-02-25 17:12:55 [INFO] WebSocket连接已安全关闭
2025-02-25 17:12:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:12:55 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:00 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:00 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:05 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:05 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:10 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:10 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:15 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:15 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:20 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:20 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:25 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:25 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:30 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:30 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:35 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:35 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:45 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:13:56 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 17:13:56 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:13:56 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:01 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:01 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:06 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:06 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:11 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:11 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:16 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:16 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:21 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:21 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:26 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:26 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:31 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:31 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:36 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:36 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:41 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:41 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:46 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:46 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:51 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:14:57 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 17:14:57 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:14:57 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:02 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:02 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:07 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:07 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:12 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:12 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:17 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:17 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:22 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:22 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:27 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:27 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:32 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:32 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:37 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:37 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:42 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:42 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:47 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:47 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:52 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:52 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:15:58 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 17:15:58 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:15:58 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:03 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:03 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:08 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:08 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:13 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:13 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:18 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:18 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:23 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:23 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:28 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:28 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:33 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:33 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:38 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:38 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:44 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:44 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:49 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:49 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:54 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:54 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:16:59 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 17:16:59 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:16:59 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:04 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:04 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:09 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:09 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:14 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:14 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:19 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:19 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:24 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:24 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:29 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:29 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:34 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:34 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:40 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:40 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:45 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:45 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:50 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:50 [ERROR] 连接失败: [Errno 111] Connect call failed ('*************', 8765)
2025-02-25 17:17:55 [INFO] 正在连接到WebSocket服务器: ws://*************:8765
2025-02-25 17:17:55 [INFO] 注册成功
2025-02-25 17:18:00 [ERROR] 获取私网IPv4地址失败: 2
2025-02-25 17:18:00 [WARNING] 未获取到必要的IP地址，等待重试...
2025-02-25 17:19:02 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:20:03 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:21:04 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:22:05 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:23:06 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:24:08 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:25:09 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:26:10 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:27:11 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:28:13 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:29:14 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:30:15 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:31:16 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:32:17 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:33:19 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:34:20 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:35:21 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:36:22 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:37:24 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:38:25 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:39:26 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:40:27 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:41:29 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:42:30 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:43:31 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:44:32 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:45:33 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:46:34 [ERROR] Heartbeat failed: {'detail': 'Given token not valid for any token type', 'code': 'token_not_valid', 'messages': [{'token_class': 'AccessToken', 'token_type': 'access', 'message': 'Token is invalid or expired'}]}
2025-02-25 17:46:55 [ERROR] WebSocket连接断开，准备重连
2025-02-25 17:46:55 [INFO] WebSocket连接已安全关闭
