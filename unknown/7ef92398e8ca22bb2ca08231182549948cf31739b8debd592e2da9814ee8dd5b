from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from .views import (
    DomainViewSet, BrasViewSet, VpdnUserViewSet,
    AuthRecordViewSet, DetailViewSet, OnlineRecordViewSet
)

router = DefaultRouter()
router.register(r'domains', DomainViewSet, basename='domain')
router.register(r'bras', BrasViewSet, basename='bras')
router.register(r'users', VpdnUserViewSet, basename='vpdn-user')
router.register(r'auth-records', AuthRecordViewSet, basename='auth-record')
router.register(r'details', DetailViewSet, basename='detail')
router.register(r'online-records', OnlineRecordViewSet, basename='online-record')

urlpatterns = [
    path('', include(router.urls)),
]
